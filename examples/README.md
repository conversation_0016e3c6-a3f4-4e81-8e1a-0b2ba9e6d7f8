# Pinnacle IO Examples

This directory contains comprehensive examples demonstrating how to use the `pinnacle_io` library for reading Pinnacle treatment planning data.

## Example Files

### 1. `basic_usage.py`
Demonstrates the fundamental usage patterns for reading Pinnacle data using the `PinnacleReader` class.

**Topics covered:**
- Basic `PinnacleReader` initialization
- Reading institution data
- Modern ID-based data access methods (recommended)
- Legacy path-based data access methods (still supported)
- Reading patient data with both approaches
- Reading imaging data (ImageSet, ImageInfo) with ID-based methods
- Reading treatment plan data (trials, ROIs, points, setup, machines) with ID-based methods
- Reading dose data with Trial object requirements
- Comparing ID-based vs path-based approaches

**Run example:**
```bash
python examples/basic_usage.py
```

### 2. `file_formats.py`
Shows how to read Pinnacle data from different file formats: directories, tar files, and zip files.

**Topics covered:**
- Reading from directories with ID-based methods
- Reading from tar files (.tar, .tar.gz, .tgz) with ID-based methods
- Reading from zip files (.zip) with ID-based methods
- Comparing file formats
- Direct reader service usage with ID-based access
- ID-based method demonstration across all formats
- Performance considerations

**Run example:**
```bash
python examples/file_formats.py
```

### 3. `writing_data.py`
Demonstrates writing capabilities and limitations of the library.

**Topics covered:**
- Current writing limitations
- Model serialization
- Data modification in memory
- Export alternatives (CSV, JSON, Excel, NIfTI)
- Future writing API design

**Run example:**
```bash
python examples/writing_data.py
```

### 4. `error_handling.py`
Shows comprehensive error handling patterns when working with Pinnacle data.

**Topics covered:**
- Handling FileNotFoundError
- Handling unsupported file formats
- Handling corrupted data
- Handling missing files
- Robust data reading patterns
- Data validation
- Logging and debugging

**Run example:**
```bash
python examples/error_handling.py
```

## Prerequisites

Before running the examples, ensure you have:

1. **Installed pinnacle_io:**
   ```bash
   pip install -e .
   ```

2. **Valid Pinnacle data:** 
   - Update file paths in examples to point to your actual Pinnacle data
   - Supported formats: directories, tar files, or zip files

## Quick Start

1. **Clone and install:**
   ```bash
   git clone <repository-url>
   cd pinnacle_io
   pip install -e .
   ```

2. **Update paths:**
   Edit the example files to use your actual Pinnacle data paths:
   ```python
   # Change this line in each example
   reader = PinnacleReader("/path/to/your/pinnacle/data")
   ```

3. **Run an example:**
   ```bash
   python examples/basic_usage.py
   ```

## Common Usage Patterns

### Reading Institution Data
```python
from pinnacle_io import PinnacleReader

reader = PinnacleReader("/path/to/pinnacle/data")
institution = reader.get_institution()
print(f"Institution: {institution.name}")
```

### Reading Patient Data (ID-Based - Recommended)
```python
# Modern ID-based approach
patient = reader.get_patient(institution=1, patient=1)
print(f"Patient: {patient.first_name} {patient.last_name}")

# Legacy path-based approach (still supported)
for patient_lite in institution.patient_lite_list:
    patient = reader.get_patient(patient_lite.patient_path)
    print(f"Patient: {patient.first_name} {patient.last_name}")
```

### Reading Treatment Plans (ID-Based - Recommended)
```python
# Modern ID-based approach
trials = reader.get_trials(institution=1, patient=1, plan=1)
for trial in trials:
    print(f"Trial: {trial.name}, Dose: {trial.prescription_dose} cGy")

rois = reader.get_rois(institution=1, patient=1, plan=1)
for roi in rois:
    print(f"ROI: {roi.name}, Type: {roi.type}")

# Legacy path-based approach (still supported)
trials = reader.get_trials("Institution_1/Mount_0/Patient_1/Plan_1")
rois = reader.get_rois("Institution_1/Mount_0/Patient_1/Plan_1")
```

### Reading Imaging Data (ID-Based)
```python
# Get full image set with pixel data
image_set = reader.get_image_set(institution=1, patient=1, image_set=0)

# Get image header only (no pixel data, faster)
image_header = reader.get_image_header(institution=1, patient=1, image_set=0)

# Get image metadata
image_info = reader.get_image_info(institution=1, patient=1, image_set=0)
```

### Reading Dose Data (Requires Trial Object)
```python
# Get trials first
trials = reader.get_trials(institution=1, patient=1, plan=1)

# Read dose data (requires trial object, not just ID)
dose = reader.get_dose(institution=1, patient=1, plan=1, trial=trials[0])
print(f"Max dose: {dose.max_dose} cGy")
```

### Error Handling
```python
try:
    reader = PinnacleReader("/path/to/pinnacle/data")
    institution = reader.get_institution()
    
    # ID-based access with error handling
    patient = reader.get_patient(institution=1, patient=1)
    trials = reader.get_trials(institution=1, patient=1, plan=1)
    
except FileNotFoundError:
    print("Data path not found")
except ValueError as e:
    print(f"Unsupported file format: {e}")
except Exception as e:
    print(f"Error reading data: {e}")
```

## ID-Based vs Path-Based Methods

### Benefits of ID-Based Methods (Recommended)

**Modern ID-based approach:**
```python
# Clean, intuitive, semantic
patient = reader.get_patient(institution=1, patient=1)
trials = reader.get_trials(institution=1, patient=1, plan=1)
image_set = reader.get_image_set(institution=1, patient=1, image_set=0)
```

**Legacy path-based approach:**
```python
# Verbose, error-prone paths
patient = reader.get_patient("Institution_1/Mount_0/Patient_1")
trials = reader.get_trials("Institution_1/Mount_0/Patient_1/Plan_1")
image_set = reader.get_image_set("Institution_1/Mount_0/Patient_1", image_set_id=0)
```

**Advantages of ID-based methods:**
- ✅ More intuitive and readable
- ✅ No manual path construction required
- ✅ Consistent with medical record organization
- ✅ Less error-prone (no path typos)
- ✅ Works with both ID numbers and model objects
- ✅ Same performance as path-based methods
- ✅ Available across all file formats (directory, tar, zip)

## File Structure

The examples assume a typical Pinnacle directory structure:
```
pinnacle_data/
├── Institution
├── Institution_1/
│   └── Mount_0/
│       └── Patient_1/
│           ├── Patient
│           ├── ImageSet_1.header
│           ├── ImageSet_1.img
│           ├── ImageSet_1.ImageInfo
│           └── Plan_1/
│               ├── plan.Trial
│               ├── plan.roi
│               ├── plan.Points
│               ├── plan.PatientSetup
│               ├── plan.Pinnacle.Machines
│               └── plan.Trial.binary.001
```

## Troubleshooting

### Common Issues

1. **FileNotFoundError:**
   - Verify the path exists
   - Check file permissions
   - Ensure the path points to the correct data

2. **ValueError (unsupported format):**
   - Supported formats: directories, .tar, .tar.gz, .tgz, .zip
   - Check file extension

3. **Missing files within archives:**
   - Verify the archive contains expected Pinnacle files
   - Check if files are in the correct directory structure

4. **Import errors:**
   - Ensure pinnacle_io is installed: `pip install -e .`
   - Check Python path and virtual environment

### Getting Help

1. **Check the main README:** `../README.md`
2. **Run tests:** `pytest tests/`
3. **Enable logging:** Add logging to see detailed error messages
4. **Check file structure:** Ensure your data matches expected structure

## Contributing

When adding new examples:

1. **Follow the existing pattern:**
   - Include comprehensive docstrings
   - Add error handling
   - Use meaningful variable names
   - Include print statements for user feedback

2. **Update this README:**
   - Add your example to the file list
   - Include topics covered
   - Add run instructions

3. **Test thoroughly:**
   - Test with different file formats
   - Test error conditions
   - Verify output is helpful