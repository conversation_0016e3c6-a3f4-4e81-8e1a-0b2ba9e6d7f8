"""
File format examples for pinnacle_io library.

This module demonstrates how to read Pinnacle data from different
file formats: directories, tar files, and zip files.
Features both modern ID-based methods and legacy path-based methods.
"""

from pinnacle_io import PinnacleReader
from pathlib import Path

def read_from_directory():
    """Demonstrate reading from a directory."""
    
    print("Reading from Directory:")
    print("-" * 30)
    
    # Read from extracted Pinnacle data directory
    directory_path = "/path/to/pinnacle/data"
    
    try:
        reader = PinnacleReader(directory_path)
        institution = reader.get_institution()
        
        print(f"✓ Successfully read from directory: {directory_path}")
        print(f"  Institution: {institution.name}")
        print(f"  Patients: {len(institution.patient_lite_list)}")
        
        # Demonstrate ID-based access with directory format
        try:
            patient = reader.get_patient(institution=1, patient=1)
            print(f"  ID-based patient access: {patient.first_name} {patient.last_name}")
        except Exception:
            print("  (No patient data available for ID-based demo)")
        
        return reader
        
    except Exception as e:
        print(f"✗ Error reading from directory: {e}")
        return None

def read_from_tar_file():
    """Demonstrate reading from tar files."""
    
    print("\nReading from Tar Files:")
    print("-" * 30)
    
    # Common tar file extensions
    tar_files = [
        "/path/to/pinnacle/data.tar",
        "/path/to/pinnacle/data.tar.gz",
        "/path/to/pinnacle/data.tgz"
    ]
    
    for tar_path in tar_files:
        try:
            reader = PinnacleReader(tar_path)
            institution = reader.get_institution()
            
            print(f"✓ Successfully read from tar: {Path(tar_path).name}")
            print(f"  Institution: {institution.name}")
            print(f"  Patients: {len(institution.patient_lite_list)}")
            
            # Demonstrate ID-based access with tar format
            try:
                patient = reader.get_patient(institution=1, patient=1)
                print(f"  ID-based patient access: {patient.first_name} {patient.last_name}")
            except Exception:
                print("  (No patient data available for ID-based demo)")
            
            return reader
            
        except FileNotFoundError:
            print(f"- Tar file not found: {Path(tar_path).name}")
        except Exception as e:
            print(f"✗ Error reading from tar: {e}")
    
    return None

def read_from_zip_file():
    """Demonstrate reading from zip files."""
    
    print("\nReading from Zip Files:")
    print("-" * 30)
    
    zip_path = "/path/to/pinnacle/data.zip"
    
    try:
        reader = PinnacleReader(zip_path)
        institution = reader.get_institution()
        
        print(f"✓ Successfully read from zip: {Path(zip_path).name}")
        print(f"  Institution: {institution.name}")
        print(f"  Patients: {len(institution.patient_lite_list)}")
        
        # Demonstrate ID-based access with zip format
        try:
            patient = reader.get_patient(institution=1, patient=1)
            print(f"  ID-based patient access: {patient.first_name} {patient.last_name}")
        except Exception:
            print("  (No patient data available for ID-based demo)")
        
        return reader
        
    except FileNotFoundError:
        print(f"- Zip file not found: {Path(zip_path).name}")
    except Exception as e:
        print(f"✗ Error reading from zip: {e}")
    
    return None

def compare_file_formats():
    """Compare reading from different file formats."""
    
    print("\nComparing File Formats:")
    print("-" * 30)
    
    formats = [
        ("Directory", read_from_directory),
        ("Tar File", read_from_tar_file),
        ("Zip File", read_from_zip_file)
    ]
    
    successful_readers = []
    
    for format_name, reader_func in formats:
        reader = reader_func()
        if reader:
            successful_readers.append((format_name, reader))
    
    if successful_readers:
        print(f"\nSuccessfully read from {len(successful_readers)} format(s):")
        for format_name, reader in successful_readers:
            print(f"  - {format_name}")
    else:
        print("\nNo file formats were successfully read.")
        print("Make sure you have valid Pinnacle data files at the specified paths.")

def demonstrate_reader_services():
    """Demonstrate direct usage of reader services."""
    
    print("\nDirect Reader Services Usage:")
    print("-" * 30)
    
    from pinnacle_io import FileReader, TarFileReader, ZipFileReader
    
    # Directory reader with ID-based methods
    try:
        file_reader = FileReader("/path/to/pinnacle/data")
        institution = file_reader.get_institution()
        print(f"✓ FileReader: {institution.name}")
        
        # Demonstrate ID-based access
        patient = file_reader.get_patient(institution=1, patient=1)
        print(f"  ID-based patient: {patient.first_name} {patient.last_name}")
        
        trials = file_reader.get_trials(institution=1, patient=1, plan=1)
        print(f"  ID-based trials: {len(trials)} found")
        
    except Exception as e:
        print(f"✗ FileReader error: {e}")
    
    # Tar reader with ID-based methods
    try:
        tar_reader = TarFileReader("/path/to/data.tar.gz")
        institution = tar_reader.get_institution()
        print(f"✓ TarFileReader: {institution.name}")
        
        # Demonstrate ID-based access
        patient = tar_reader.get_patient(institution=1, patient=1)
        print(f"  ID-based patient: {patient.first_name} {patient.last_name}")
        
    except Exception as e:
        print(f"✗ TarFileReader error: {e}")
    
    # Zip reader with ID-based methods
    try:
        zip_reader = ZipFileReader("/path/to/data.zip")
        institution = zip_reader.get_institution()
        print(f"✓ ZipFileReader: {institution.name}")
        
        # Demonstrate ID-based access
        patient = zip_reader.get_patient(institution=1, patient=1)
        print(f"  ID-based patient: {patient.first_name} {patient.last_name}")
        
    except Exception as e:
        print(f"✗ ZipFileReader error: {e}")

def performance_comparison():
    """Compare performance of different file formats."""
    
    print("\nPerformance Considerations:")
    print("-" * 30)
    
    print("Directory access:")
    print("  ✓ Fastest for individual file access")
    print("  ✓ Best for development and testing")
    print("  ✗ Requires extraction of archived data")
    
    print("\nTar file access:")
    print("  ✓ Good compression ratios")
    print("  ✓ Preserves file structure and metadata")
    print("  ~ Sequential access can be slower")
    
    print("\nZip file access:")
    print("  ✓ Wide compatibility")
    print("  ✓ Random access to files")
    print("  ~ Slightly larger than tar.gz files")

def main():
    """Main example function."""
    
    print("Pinnacle IO File Format Examples")
    print("=" * 40)
    
    # Test different file formats
    compare_file_formats()
    
    # Demonstrate reader services
    demonstrate_reader_services()
    
    # Performance comparison
    performance_comparison()
    
    print("\n" + "=" * 40)
    print("File format examples completed!")
    print("\nRecommendations:")
    print("1. Use PinnacleReader for automatic format detection")
    print("   It handles directory, tar, and zip files transparently.")
    print("2. Use ID-based methods for cleaner, more readable code:")
    print("   reader.get_patient(institution=1, patient=1)")
    print("   Instead of: reader.get_patient('Institution_1/Mount_0/Patient_1')")
    print("3. All file formats support both ID-based and path-based methods")

if __name__ == "__main__":
    main()