all = [
    "Pillow",
    "PyYAML",
    "anthropic",
    "anyio",
    "astroid",
    "attrs",
    "dash",
    "dbfread",
    "dicompyler-core",
    "doc8",
    "imageio",
    "ipython",
    "jupyter-book",
    "keyring",
    "matplotlib",
    "natsort",
    "networkx",
    "numba",
    "numpy",
    "packaging",
    "pandas",
    "plotly",
    "pre-commit",
    "psutil",
    "pydicom",
    "pylibjpeg-libjpeg",
    "pylint",
    "pymssql",
    "pynetdicom",
    "pyright",
    "pytest",
    "pytest-rerunfailures",
    "pytest-sugar",
    "python-dateutil",
    "pywin32",
    "readme-renderer",
    "requests",
    "rope",
    "ruff",
    "scikit-learn",
    "scipy",
    "shapely",
    "sphinx-argparse",
    "sphinx-book-theme",
    "sqlalchemy",
    "streamlit",
    "tabulate",
    "timeago",
    "toml",
    "tqdm",
    "trio",
    "trio-asyncio",
    "watchdog",
    "xmltodict",
]
cli = ["toml"]
comparables = ["flashgamma"]
dev = ["doc8", "pre-commit", "pyright", "readme-renderer", "rope", "ruff", "tabulate"]
dicom = ["pydicom", "pynetdicom"]
docs = [
    "ipython",
    "jupyter-book",
    "matplotlib",
    "networkx",
    "numba",
    "numpy",
    "pydicom",
    "scipy",
    "sphinx-argparse",
    "sphinx-book-theme",
    "toml",
    "tqdm",
]
doctests = ["ruff", "sphinx-book-theme", "tabulate"]
icom = ["numpy"]
mosaiq = ["attrs", "pandas", "pymssql", "scikit-learn", "sqlalchemy", "toml"]
propagate = ["ruff"]
tests = [
    "astroid",
    "numba",
    "psutil",
    "pylint",
    "pytest",
    "pytest-rerunfailures",
    "pytest-sugar",
    "python-dateutil",
    "tqdm",
]
user = [
    "Pillow",
    "PyYAML",
    "anthropic",
    "anyio",
    "attrs",
    "dash",
    "dbfread",
    "dicompyler-core",
    "imageio",
    "keyring",
    "matplotlib",
    "natsort",
    "numba",
    "numpy",
    "packaging",
    "pandas",
    "plotly",
    "pydicom",
    "pylibjpeg-libjpeg",
    "pymssql",
    "pynetdicom",
    "python-dateutil",
    "pywin32",
    "requests",
    "scikit-learn",
    "scipy",
    "shapely",
    "sqlalchemy",
    "streamlit",
    "timeago",
    "toml",
    "tqdm",
    "trio",
    "trio-asyncio",
    "watchdog",
    "xmltodict",
]
