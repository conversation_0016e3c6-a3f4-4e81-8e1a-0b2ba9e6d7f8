# Copyright (C) 2021 Cancer Care Associates

# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at

#     http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from pymedphys._streamlit import categories
from pymedphys._streamlit.apps.metersetmap import _trf
from pymedphys._streamlit.utilities import config as st_config

CATEGORY = categories.DRAFT
TITLE = "TRF Explorer"


def main():
    config = st_config.get_config()

    _trf.trf_input_method(config)
