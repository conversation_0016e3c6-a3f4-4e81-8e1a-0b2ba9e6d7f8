"""
This needs some additional work to become more generally usable but can be used at the moment
for testing purposes by simply running the 'PlanComplexity.py' file which will open up a
tkinter filedialog window to choose a file locally and will perform both the MCS as well
as the beam area/irregularity/modulation calculations on the RTPlan file.

Only works where all beams in the plan are either IMRT/VMAT and has only been tested on
plans exported by Pinnacle, but it should work for ant RTPlan file as far as i am aware.
"""
