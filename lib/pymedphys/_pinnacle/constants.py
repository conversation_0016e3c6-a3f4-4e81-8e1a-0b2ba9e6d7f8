# Copyright (C) 2019 South Western Sydney Local Health District,
# University of New South Wales

# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at

#     http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# This work is derived from:
# https://github.com/AndrewWAlexander/Pinnacle-tar-DICOM
# which is released under the following license:

# Copyright (c) [2017] [<PERSON><PERSON>, <PERSON>]

# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:

# The above copyright notice and this permission notice shall be included in all
# copies or substantial portions of the Software.

# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
# SOFTWARE.


GTransferSyntaxUID = "1.2.840.10008.1.2"
GImplementationClassUID = "1.2.826.0.1.3680043.8.498.75006884747854523615841001"

RTDOSEModality = "RTDOSE"
RTPLANModality = "RTPLAN"
RTSTRUCTModality = "RTSTRUCT"

RTDoseSOPClassUID = "1.2.840.10008.5.1.4.1.1.481.2"
RTStructSOPClassUID = "1.2.840.10008.5.1.4.1.1.481.3"
RTPlanSOPClassUID = "1.2.840.10008.5.1.4.1.1.481.5"

Manufacturer = "PyMedPhys Pinnacle Export"

# Colors
colors = {}
colors["red"] = ["255", "0", "0"]
colors["pink"] = ["255", "20", "147"]
colors["blue"] = ["0", "0", "255"]
colors["green"] = ["0", "255", "0"]
colors["purple"] = ["125", "38", "205"]
colors["yellow"] = ["255", "255", "0"]
colors["orange"] = ["255", "140", "0"]
colors["darkgreen"] = ["0", "100", "0"]
colors["skyblue"] = ["0", "191", "255"]
colors["lightpink"] = ["255", "192", "203"]
colors["turquois"] = ["72", "209", "204"]
colors["brown"] = ["139", "69", "19"]
colors["gold"] = ["255", "193", "37"]
colors["lightpurple"] = ["221", "160", "221"]
colors["olive"] = ["107", "142", "35"]
colors["brick"] = ["142", "35", "35"]
colors["peach"] = ["245", "204", "176"]
colors["lightblue"] = ["191", "239", "255"]
colors["maroon"] = ["139", "28", "98"]
colors["tomato"] = ["255", "99", "71"]
colors["lavender"] = ["230", "230", "250"]
colors["khaki"] = ["195", "176", "145"]
colors["skin"] = ["234", "192", "134"]
colors["seashell"] = ["255", "245", "238"]
colors["yellowgreen"] = ["173", "255", "47"]
colors["forest"] = ["34", "139", "34"]
colors["slateblue"] = ["16", "93", "157"]
colors["grey"] = ["127", "127", "127"]
colors["aquamarine"] = ["127", "255", "212"]
colors["lightorange"] = ["239", "118", "51"]
colors["steelblue"] = ["70", "130", "180"]
colors["teal"] = ["0", "128", "128"]
colors["greyscale"] = ["106", "108", "110"]
colors["inverse_grey"] = ["149", "147", "145"]
