[Ident]
IDENT_ID = "int"
IDENT_Set_ID = "int"
Version = "smallint"
Create_DtTm = "datetime"
Create_ID = "int"
Edit_DtTm = "datetime"
Edit_ID = "int"
Sanct_DtTm = "datetime"
Sanct_ID = "int"
Sanct_DtTm2 = "datetime"
Sanct_Id2 = "int"
Status_Inactive = "bit"
Start_DtTm = "datetime"
End_DtTm = "datetime"
Pat_Id1 = "int"
IDA = "varchar"
IDB = "varchar"
IDB_Inst_ID = "int"
IDC = "varchar"
IDC_Inst_Id = "int"
IDD = "varchar"
IDD_Inst_Id = "int"
IDE = "varchar"
IDE_Inst_Id = "int"
IDF = "varchar"
IDF_Inst_Id = "int"
Trace_Status = "varchar"
PortalID = "varchar"
PortalGenerate_DtTm = "datetime"
PortalActivate_DtTm = "datetime"
RowVers = "timestamp"

[Patient]
Pat_ID1 = "int"
Create_DtTm = "datetime"
Create_ID = "int"
Birth_DtTm = "datetime"
Edit_DtTm = "datetime"
Edit_ID = "int"
Sanct_DtTm = "datetime"
Sanct_ID = "int"
Sanct_DtTm2 = "datetime"
Sanct_Id2 = "int"
Note_ID = "int"
Salutation = "varchar"
SS_Number = "varchar"
Last_Name = "varchar"
First_Name = "varchar"
MIddle_Name = "varchar"
Suffix = "varchar"
Alias_Name = "varchar"
Stmt_Cycle = "smallint"
Use_Alternate_Stmt = "tinyint"
Clin_Status = "smallint"
Inactive = "bit"
Restricted = "tinyint"
Status_Private = "bit"
Maiden_Name = "varchar"
Last_Name_Secondary = "varchar"
First_Name_Secondary = "varchar"
Middle_Name_Secondary = "varchar"
Last_Name_SingleByte = "varchar"
First_Name_SingleByte = "varchar"
Middle_Name_SingleByte = "varchar"
MultipleBirth_PRO_ID = "int"
MultipleBirthOrder = "smallint"
MotherMaidenName = "varchar"
RowVers = "timestamp"

[TxField]
FLD_ID = "int"
FLD_SET_ID = "int"
Version = "smallint"
Pat_ID1 = "int"
Create_DtTm = "datetime"
Create_ID = "int"
Edit_DtTm = "datetime"
Edit_ID = "int"
Sanct_DtTm = "datetime"
Sanct_ID = "int"
Calibration_DtTm = "datetime"
Calibration_ID = "int"
TieredSecurity1 = "bigint"
Cosigner_PNP_ID = "int"
Note_ID = "int"
SIT_Set_ID = "int"
Field_Label = "varchar"
Field_Name = "varchar"
Machine_ID_Staff_ID = "int"
MachineCharID = "int"
Resequence = "smallint"
Start_DtTm = "datetime"
Last_Tx_DtTm = "datetime"
Fractions_Tx = "smallint"
TentativeFx = "smallint"
Cgray = "decimal"
Pf_Open_Coef = "float"
Pf_Open_Dlta = "decimal"
Pf_Clsd_Coef = "float"
PIAcqDevice = "smallint"
SID = "decimal"
Tol_Tbl_ID = "int"
Arc_Mu = "decimal"
Dyn_Wedge = "varchar"
Wdg_Appl = "varchar"
Block = "varchar"
Blk_Desc = "varchar"
Comp_Fda = "varchar"
FDA_Desc = "varchar"
Bolus = "varchar"
Head_Ang = "decimal"
Sad = "decimal"
Ssd = "decimal"
SDD = "decimal"
Mlc = "smallint"
Alert = "char"
IMG_ID = "int"
DisplaySequence = "smallint"
Notes = "varchar"
SIM_ID = "int"
SystemLock_DtTm = "datetime"
IsHidden = "bit"
IsDownloaded = "bit"
IsExtLocked = "bit"
IsAFSLocked = "bit"
IsEditLocked = "bit"
BlockSlot = "tinyint"
WdgApplSlot = "tinyint"
CompFDASlot = "tinyint"
Open_Quality = "tinyint"
CLSD_Quality = "tinyint"
TxFromDHS_ID = "int"
Beam_Type_Flag = "tinyint"
Mass_Number = "int"
Atomic_Number = "int"
Charge_State = "int"
Scan_Mode_Enum = "tinyint"
VSAD_X = "decimal"
VSAD_Y = "decimal"
Beam_Line_Data_Table_Version = "varchar"
Max_Coll_Diameter = "decimal"
Nominal_SOBP_Width = "decimal"
Plan_Distal_Distance = "decimal"
Beam_Check_Flag = "bit"
Respiration_Gating_Flag = "bit"
Respiration_Cycle = "decimal"
Related_FLD_SET_ID = "int"
FieldRelationship_Enum = "tinyint"
Calibration_Status_Enum = "tinyint"
Do_Position_Verify = "bit"
AirGap = "decimal"
XVI_Preset = "varchar"
Billing_Dose_Ratio = "decimal"
Cost_Coefficient = "smallint"
Type_Enum = "tinyint"
Modality_Enum = "tinyint"
ControlPoints = "smallint"
IndexMethod_Enum = "tinyint"
IndexReference_Enum = "tinyint"
Fld_X_Mode_Enum = "tinyint"
Fld_Y_Mode_Enum = "tinyint"
IsFromDataImport = "bit"
IsModifiedAfterDataImport = "bit"
Type_UserDefined = "varchar"
Modality_UserDefined = "varchar"
OriginalPlanUID = "char"
FldDoc_OBJ_ID = "int"
IsDocBased = "bit"
Meterset = "decimal"
MetersetRound = "decimal"
MetersetUnit_Enum = "tinyint"
cGrayPerMeterset = "float"
BackupTimer = "decimal"
kVApplCode = "char"
kVApplShape_Enum = "tinyint"
kVFSD = "smallint"
kVApplDimension1 = "smallint"
kVApplDimension2 = "smallint"
kVFilterCode = "char"
kVTubeCurrent = "decimal"
kVHVL = "decimal"
kVHVLEquivalent_Enum = "tinyint"
IsFieldDocChanged = "bit"
OriginalBeamName = "char"
OriginalBeamNumber = "int"
PIOpenMeterset = "decimal"
PIClsdMeterset = "decimal"
IsFFF = "bit"
IsProtected = "bit"
IsXraysAppl = "bit"
ScatteringMode = "varchar"
IsHDTAuthorizationRequested = "bit"
TotalReferenceAirKerma = "decimal"
XVI_PresetType_Enum = "tinyint"
CcGE = "decimal"
LongitudinalSpreadingTechnique = "tinyint"
LateralSpreadingTechnique = "tinyint"
DosePoint_X = "decimal"
DosePoint_Y = "decimal"
DosePoint_Z = "decimal"
DosePointWET = "decimal"
RowVers = "timestamp"

[Site]
SIT_ID = "int"
SIT_SET_ID = "int"
Version = "smallint"
Pat_ID1 = "int"
Create_DtTm = "datetime"
Create_ID = "int"
Edit_DtTm = "datetime"
Edit_ID = "int"
Sanct_DtTm = "datetime"
Sanct_Id = "int"
Sanct_DtTm2 = "datetime"
Sanct_Id2 = "int"
Control_Reason = "varchar"
TieredSecurity1 = "bigint"
Cosigner_PNP_ID = "int"
SystemLock_DtTm = "datetime"
Note_ID = "int"
Eff_DtTm = "datetime"
Site_Name = "varchar"
Setup_Note_ID = "int"
Technique = "varchar"
Modality = "varchar"
Energy = "smallint"
Target = "varchar"
Rx_Depth = "decimal"
Target_Units = "varchar"
Dose_Tx = "decimal"
Dose_Ttl = "decimal"
Dose_Ttl_Cum = "decimal"
Fractions = "smallint"
Frac_Pattern = "varchar"
Notes = "varchar"
Tentative_Fx = "smallint"
Arc_DtTm = "datetime"
Start_DtTm = "datetime"
Last_DtTm = "datetime"
Dose_Act = "smallint"
Frac_Act = "smallint"
Chg_ID = "int"
DisplaySequence = "smallint"
Reference_SIT_Set_ID = "int"
Reference_Fraction = "smallint"
Reference_Fx_Offset = "smallint"
ThresholdFxDoseRel = "smallint"
ThresholdFxDoseAbs = "smallint"
AND_Rel_With_Abs = "bit"
NonFxDose = "smallint"
FxType = "smallint"
PCP_ID = "int"
Holidays_OK = "bit"
SATURDAY_OK = "bit"
SUNDAY_OK = "bit"
Status_Enum = "tinyint"
UseSiteThresholdFx = "bit"
Modality_Enum = "tinyint"
IsFromDataImport = "bit"
IsModifiedAfterDataImport = "bit"
IsDoseInCcGE = "bit"
RowVers = "timestamp"

[TrackTreatment]
TTX_ID = "int"
Create_DtTm = "datetime"
Create_ID = "int"
Edit_DtTm = "datetime"
Edit_ID = "int"
Pat_ID1 = "int"
FLD_ID = "int"
SIT_ID = "int"
Machine_ID_Staff_ID = "int"
MAC_ID = "int"
Inst_ID = "int"
Staff_ID = "int"
Staff_ID2 = "int"
PTC_ID = "int"
Sent_DtTm = "datetime"
WasCFS = "bit"
WasPassivePlan = "bit"
WasQAMode = "bit"
WasBeamEnabled = "bit"
WasBeamOn = "bit"
WasBeamComplete = "bit"
WasCanceled = "bit"
WasRecorded = "bit"
DHS_ID = "int"
WasResolved = "bit"
Resolved_Staff_ID = "int"
Resolved_DtTm = "datetime"
RowVers = "timestamp"

[Chklist]
Chk_id = "int"
Chk_set_id = "int"
Version = "smallint"
Create_DtTm = "datetime"
Create_ID = "int"
Edit_DtTm = "datetime"
Edit_ID = "int"
Sanct_DtTm = "datetime"
Sanct_ID = "int"
Sanct_DtTm2 = "datetime"
Sanct_Id2 = "int"
Pat_ID1 = "int"
Note_ID = "int"
Inst_ID = "int"
Chklist_ID = "smallint"
Item_Seq = "smallint"
Qcl_Type = "smallint"
Due_DtTm = "datetime"
Elpsd_Action = "smallint"
Act_DtTm = "datetime"
Activity = "varchar"
Req_Staff_ID = "int"
Rsp_Staff_ID = "int"
Com_Staff_ID = "int"
Billable = "smallint"
PRS_ID = "int"
Complete = "smallint"
Supr_DtTm = "datetime"
Supr_ID = "int"
Notes = "varchar"
Suppressed = "bit"
FBL_ID = "int"
TSK_ID = "int"
Est_Dur = "int"
Priority_PRO_ID = "int"
Code_Group = "varchar"
Instructions = "varchar"
Start_DtTm = "datetime"
RowVers = "timestamp"

[QCLTask]
TSK_ID = "int"
Create_DtTm = "datetime"
Create_ID = "int"
Edit_DtTm = "datetime"
Edit_ID = "int"
Description = "varchar"
Inactive = "bit"
Due_DtTm = "datetime"
Responsible_Staff_ID = "int"
Est_Dur = "int"
Priority_PRO_ID = "int"
Billable = "int"
Code_Group = "varchar"
ProcCode_PRS_ID = "int"
Elpsd_Action = "smallint"
RowVers = "timestamp"

[Staff]
Staff_ID = "int"
PNP_ID = "int"
Initials = "char"
User_Name = "char"
PasswordBytes = "binary"
Code = "char"
View1 = "int"
Modify1 = "int"
Approve1 = "int"
View2 = "int"
Modify2 = "int"
Approve2 = "int"
View3 = "int"
Modify3 = "int"
Approve3 = "int"
View4 = "int"
Modify4 = "int"
Approve4 = "int"
IsGlobal = "bit"
TieredSecurity1 = "bigint"
Ledger_Security = "int"
Ledger_Security_All = "bit"
View5 = "int"
Modify5 = "int"
Approve5 = "int"
Create_DtTm = "datetime"
Create_ID = "int"
Edit_DtTm = "datetime"
Edit_ID = "int"
Sanct_DtTm = "datetime"
Sanct_ID = "int"
Note_ID = "int"
Type = "varchar"
Category = "int"
Sch_Duration = "int"
Clinic_ID = "varchar"
Overrides = "int"
Bill_Credentials = "varchar"
Other_Credentials = "varchar"
Acc_Assign = "bit"
Last_Name = "char"
First_Name = "varchar"
Mdl_Initial = "char"
Suffix = "varchar"
Birth_DtTm = "datetime"
Gender = "varchar"
Adr1 = "varchar"
Adr2 = "varchar"
City = "varchar"
State_Province = "varchar"
Postal = "varchar"
Country = "varchar"
Work_Phone = "varchar"
Work_Phone_Ex = "varchar"
Fax = "varchar"
Pager = "varchar"
Pager_Ex = "varchar"
CellPhone = "varchar"
EMail = "varchar"
Home_Phone = "varchar"
Home_Phone_Ex = "varchar"
Switches = "int"
Private = "smallint"
Restricted = "smallint"
Sch_ID1 = "int"
Sch_ID2 = "int"
Sch_ID3 = "int"
Sch_ID4 = "int"
Sch_ID5 = "int"
Mail_Pref = "smallint"
ChartDestination = "tinyint"
Deleted = "bit"
Status_inactive = "bit"
Status_Login = "bit"
Status_Unaprv = "bit"
Staff_Role = "smallint"
Dept_type = "tinyint"
Machine_Type = "tinyint"
CC_From_Schedule = "tinyint"
Can_Be_Scheduled = "bit"
TemplateOverrideLevel = "int"
NonPhysician = "tinyint"
AllowCustomizeLayouts = "bit"
AllowPushLayouts = "bit"
OverrideFilterLockdown = "bit"
Last_Name_SingleByte = "varchar"
First_Name_SingleByte = "varchar"
Mdl_Initial_SingleByte = "char"
HPG_Eligible = "bit"
DirectMailAddress = "varchar"
RowVers = "timestamp"

[TxFieldPoint]
TFP_ID = "int"
FLD_ID = "int"
Create_DtTm = "datetime"
Create_ID = "int"
Edit_DtTm = "datetime"
Edit_ID = "int"
Point = "smallint"
Index = "decimal"
MLC_Leaves = "smallint"
A_Leaf_Set = "binary"
B_Leaf_Set = "binary"
Gantry_Ang = "decimal"
Coll_Ang = "decimal"
Field_X = "decimal"
Coll_X1 = "decimal"
Coll_X2 = "decimal"
Field_Y = "decimal"
Coll_Y1 = "decimal"
Coll_Y2 = "decimal"
MLC_Type = "smallint"
Shape_ID = "int"
Fit_Method = "smallint"
Slice_Num = "smallint"
IsMotorizedWedgeIn = "bit"
Flat_Top_Length = "decimal"
Spill_Length = "decimal"
Beam_Intensity = "decimal"
Change_Check_Data_Before = "binary"
Change_Check_Data_After = "binary"
Peak_Range = "decimal"
Energy = "smallint"
Energy_Unit_Enum = "tinyint"
Meterset_Rate = "decimal"
Gantry_Pitch_Ang = "decimal"
Gantry_Pitch_Dir_Enum = "tinyint"
Gantry_Dir_Enum = "tinyint"
Coll_Dir_Enum = "tinyint"
Couch_Pitch_Ang = "decimal"
Couch_Pitch_Dir_Enum = "tinyint"
Couch_Roll_Ang = "decimal"
Couch_Roll_Dir_Enum = "tinyint"
Couch_Vrt = "decimal"
Couch_Ang = "decimal"
Couch_Ang_Dir_Enum = "tinyint"
Couch_Lat = "decimal"
Couch_Lng = "decimal"
Couch_Top_Axis_Distance = "decimal"
Couch_Top_Dir_Enum = "tinyint"
Head_Fixation_Ang = "decimal"
Snout_Position = "decimal"
Isocenter_X = "decimal"
Isocenter_Y = "decimal"
Isocenter_Z = "decimal"
Entry_Point_X = "decimal"
Entry_Point_Y = "decimal"
Entry_Point_Z = "decimal"
Couch_Top = "decimal"
Change_Check_Data_Before_Count = "tinyint"
Change_Check_Data_After_Count = "tinyint"
IsModifiedAfterDataImport = "bit"
Energy_Per_Nucleon = "decimal"
IsCouch_VrtRelative = "bit"
IsCouch_LatRelative = "bit"
IsCouch_LngRelative = "bit"
IsCouch_AngRelative = "bit"
IsCouch_Pitch_AngRelative = "bit"
IsCouch_Roll_AngRelative = "bit"
Catheters = "smallint"
Chair_Swivel_Ang = "decimal"
Chair_Swivel_Dir_Enum = "tinyint"
Chair_Tilt_Ang = "decimal"
Chair_Tilt_Dir_Enum = "tinyint"
SnoutRotation = "decimal"
Isocenter_MLC_Distance = "decimal"
DosePointWET = "decimal"
WobblingRadius_X = "decimal"
WobblingRadius_Y = "decimal"
LayercGrayPerMeterset = "decimal"
RowVers = "timestamp"

[PatientFamilyHistory]
PFH_ID = "int"
Pat_ID1 = "int"
Name = "varchar"
RowVers = "timestamp"

[PatientName]
PatientNameId = "int"
Pat_ID1 = "int"
Last_Name = "varchar"
First_Name = "varchar"
Middle_Name = "varchar"
RowVers = "timestamp"
