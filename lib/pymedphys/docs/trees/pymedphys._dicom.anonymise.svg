<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.43.0 (0)
 -->
<!-- Title: pymedphys._dicom.anonymise Pages: 1 -->
<svg width="790pt" height="476pt"
 viewBox="0.00 0.00 790.13 476.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 472)">
<title>pymedphys._dicom.anonymise</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-472 786.13,-472 786.13,4 -4,4"/>
<!-- pymedphys._dicom.anonymise -->
<g id="node1" class="node">
<title>pymedphys._dicom.anonymise</title>
<g id="a_node1"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/anonymise/__init__.py" xlink:title="_dicom.anonymise">
<ellipse fill="none" stroke="black" cx="519.74" cy="-378" rx="95.58" ry="18"/>
<text text-anchor="middle" x="519.74" y="-374.3" font-family="Times,serif" font-size="14.00">_dicom.anonymise</text>
</a>
</g>
</g>
<!-- pymedphys._dicom.anonymise.api -->
<g id="node2" class="node">
<title>pymedphys._dicom.anonymise.api</title>
<g id="a_node2"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/anonymise/api.py" xlink:title="_dicom.anonymise.api">
<ellipse fill="none" stroke="black" cx="519.74" cy="-306" rx="112.38" ry="18"/>
<text text-anchor="middle" x="519.74" y="-302.3" font-family="Times,serif" font-size="14.00">_dicom.anonymise.api</text>
</a>
</g>
</g>
<!-- pymedphys._dicom.anonymise&#45;&gt;pymedphys._dicom.anonymise.api -->
<g id="edge1" class="edge">
<title>pymedphys._dicom.anonymise&#45;&gt;pymedphys._dicom.anonymise.api</title>
<path fill="none" stroke="black" d="M519.74,-359.7C519.74,-351.98 519.74,-342.71 519.74,-334.11"/>
<polygon fill="black" stroke="black" points="523.24,-334.1 519.74,-324.1 516.24,-334.1 523.24,-334.1"/>
</g>
<!-- pymedphys._dicom.anonymise.core -->
<g id="node3" class="node">
<title>pymedphys._dicom.anonymise.core</title>
<g id="a_node3"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/anonymise/core.py" xlink:title="_dicom.anonymise.core">
<ellipse fill="none" stroke="black" cx="410.74" cy="-234" rx="118.08" ry="18"/>
<text text-anchor="middle" x="410.74" y="-230.3" font-family="Times,serif" font-size="14.00">_dicom.anonymise.core</text>
</a>
</g>
</g>
<!-- pymedphys._dicom.anonymise.api&#45;&gt;pymedphys._dicom.anonymise.core -->
<g id="edge3" class="edge">
<title>pymedphys._dicom.anonymise.api&#45;&gt;pymedphys._dicom.anonymise.core</title>
<path fill="none" stroke="black" d="M493.91,-288.41C479.35,-279.06 460.99,-267.27 445.22,-257.14"/>
<polygon fill="black" stroke="black" points="447.07,-254.17 436.76,-251.71 443.29,-260.06 447.07,-254.17"/>
</g>
<!-- pymedphys._dicom.utilities -->
<g id="node5" class="node">
<title>pymedphys._dicom.utilities</title>
<g id="a_node5"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/utilities/__init__.py" xlink:title="_dicom.utilities">
<ellipse fill="none" stroke="black" cx="638.74" cy="-234" rx="81.49" ry="18"/>
<text text-anchor="middle" x="638.74" y="-230.3" font-family="Times,serif" font-size="14.00">_dicom.utilities</text>
</a>
</g>
</g>
<!-- pymedphys._dicom.anonymise.api&#45;&gt;pymedphys._dicom.utilities -->
<g id="edge4" class="edge">
<title>pymedphys._dicom.anonymise.api&#45;&gt;pymedphys._dicom.utilities</title>
<path fill="none" stroke="black" d="M547.94,-288.41C564.33,-278.77 585.15,-266.53 602.71,-256.19"/>
<polygon fill="black" stroke="black" points="604.72,-259.08 611.56,-250.99 601.17,-253.04 604.72,-259.08"/>
</g>
<!-- pymedphys._dicom.anonymise.strategy -->
<g id="node4" class="node">
<title>pymedphys._dicom.anonymise.strategy</title>
<g id="a_node4"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/anonymise/strategy.py" xlink:title="_dicom.anonymise.strategy">
<ellipse fill="none" stroke="black" cx="390.74" cy="-162" rx="137.58" ry="18"/>
<text text-anchor="middle" x="390.74" y="-158.3" font-family="Times,serif" font-size="14.00">_dicom.anonymise.strategy</text>
</a>
</g>
</g>
<!-- pymedphys._dicom.anonymise.core&#45;&gt;pymedphys._dicom.anonymise.strategy -->
<g id="edge2" class="edge">
<title>pymedphys._dicom.anonymise.core&#45;&gt;pymedphys._dicom.anonymise.strategy</title>
<path fill="none" stroke="black" d="M405.8,-215.7C403.57,-207.9 400.89,-198.51 398.41,-189.83"/>
<polygon fill="black" stroke="black" points="401.74,-188.76 395.63,-180.1 395.01,-190.68 401.74,-188.76"/>
</g>
<!-- pymedphys._dicom.constants -->
<g id="node7" class="node">
<title>pymedphys._dicom.constants</title>
<g id="a_node7"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/constants/__init__.py" xlink:title="_dicom.constants">
<ellipse fill="none" stroke="black" cx="390.74" cy="-90" rx="90.98" ry="18"/>
<text text-anchor="middle" x="390.74" y="-86.3" font-family="Times,serif" font-size="14.00">_dicom.constants</text>
</a>
</g>
</g>
<!-- pymedphys._dicom.anonymise.strategy&#45;&gt;pymedphys._dicom.constants -->
<g id="edge9" class="edge">
<title>pymedphys._dicom.anonymise.strategy&#45;&gt;pymedphys._dicom.constants</title>
<path fill="none" stroke="black" d="M390.74,-143.7C390.74,-135.98 390.74,-126.71 390.74,-118.11"/>
<polygon fill="black" stroke="black" points="394.24,-118.1 390.74,-108.1 387.24,-118.1 394.24,-118.1"/>
</g>
<!-- pymedphys._dicom.utilities.files -->
<g id="node6" class="node">
<title>pymedphys._dicom.utilities.files</title>
<g id="a_node6"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/utilities/files.py" xlink:title="_dicom.utilities.files">
<ellipse fill="none" stroke="black" cx="649.74" cy="-162" rx="103.18" ry="18"/>
<text text-anchor="middle" x="649.74" y="-158.3" font-family="Times,serif" font-size="14.00">_dicom.utilities.files</text>
</a>
</g>
</g>
<!-- pymedphys._dicom.utilities&#45;&gt;pymedphys._dicom.utilities.files -->
<g id="edge5" class="edge">
<title>pymedphys._dicom.utilities&#45;&gt;pymedphys._dicom.utilities.files</title>
<path fill="none" stroke="black" d="M641.46,-215.7C642.67,-207.98 644.13,-198.71 645.48,-190.11"/>
<polygon fill="black" stroke="black" points="648.96,-190.53 647.05,-180.1 642.04,-189.44 648.96,-190.53"/>
</g>
<!-- pymedphys._dicom.constants.core -->
<g id="node8" class="node">
<title>pymedphys._dicom.constants.core</title>
<g id="a_node8"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/constants/core.py" xlink:title="_dicom.constants.core">
<ellipse fill="none" stroke="black" cx="113.74" cy="-18" rx="113.98" ry="18"/>
<text text-anchor="middle" x="113.74" y="-14.3" font-family="Times,serif" font-size="14.00">_dicom.constants.core</text>
</a>
</g>
</g>
<!-- pymedphys._dicom.constants&#45;&gt;pymedphys._dicom.constants.core -->
<g id="edge6" class="edge">
<title>pymedphys._dicom.constants&#45;&gt;pymedphys._dicom.constants.core</title>
<path fill="none" stroke="black" d="M337,-75.42C292.64,-64.21 229.44,-48.24 181.59,-36.15"/>
<polygon fill="black" stroke="black" points="182.18,-32.69 171.63,-33.63 180.47,-39.47 182.18,-32.69"/>
</g>
<!-- pymedphys._dicom.constants.orientation -->
<g id="node9" class="node">
<title>pymedphys._dicom.constants.orientation</title>
<g id="a_node9"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/constants/orientation.py" xlink:title="_dicom.constants.orientation">
<ellipse fill="none" stroke="black" cx="390.74" cy="-18" rx="144.87" ry="18"/>
<text text-anchor="middle" x="390.74" y="-14.3" font-family="Times,serif" font-size="14.00">_dicom.constants.orientation</text>
</a>
</g>
</g>
<!-- pymedphys._dicom.constants&#45;&gt;pymedphys._dicom.constants.orientation -->
<g id="edge7" class="edge">
<title>pymedphys._dicom.constants&#45;&gt;pymedphys._dicom.constants.orientation</title>
<path fill="none" stroke="black" d="M390.74,-71.7C390.74,-63.98 390.74,-54.71 390.74,-46.11"/>
<polygon fill="black" stroke="black" points="394.24,-46.1 390.74,-36.1 387.24,-46.1 394.24,-46.1"/>
</g>
<!-- pymedphys._dicom.constants.uuid -->
<g id="node10" class="node">
<title>pymedphys._dicom.constants.uuid</title>
<g id="a_node10"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/constants/uuid.py" xlink:title="_dicom.constants.uuid">
<ellipse fill="none" stroke="black" cx="667.74" cy="-18" rx="114.28" ry="18"/>
<text text-anchor="middle" x="667.74" y="-14.3" font-family="Times,serif" font-size="14.00">_dicom.constants.uuid</text>
</a>
</g>
</g>
<!-- pymedphys._dicom.constants&#45;&gt;pymedphys._dicom.constants.uuid -->
<g id="edge8" class="edge">
<title>pymedphys._dicom.constants&#45;&gt;pymedphys._dicom.constants.uuid</title>
<path fill="none" stroke="black" d="M444.48,-75.42C488.84,-64.21 552.04,-48.24 599.89,-36.15"/>
<polygon fill="black" stroke="black" points="601.01,-39.47 609.85,-33.63 599.3,-32.69 601.01,-39.47"/>
</g>
<!-- pymedphys.dicom.anonymise -->
<g id="node11" class="node">
<title>pymedphys.dicom.anonymise</title>
<g id="a_node11"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/anonymise/__init__.py" xlink:title="pymedphys.dicom.anonymise">
<ellipse fill="none" stroke="black" cx="519.74" cy="-450" rx="147.57" ry="18"/>
<text text-anchor="middle" x="519.74" y="-446.3" font-family="Times,serif" font-size="14.00">pymedphys.dicom.anonymise</text>
</a>
</g>
</g>
<!-- pymedphys.dicom.anonymise&#45;&gt;pymedphys._dicom.anonymise -->
<g id="edge10" class="edge">
<title>pymedphys.dicom.anonymise&#45;&gt;pymedphys._dicom.anonymise</title>
<path fill="none" stroke="black" d="M519.74,-431.7C519.74,-423.98 519.74,-414.71 519.74,-406.11"/>
<polygon fill="black" stroke="black" points="523.24,-406.1 519.74,-396.1 516.24,-406.1 523.24,-406.1"/>
</g>
</g>
</svg>
