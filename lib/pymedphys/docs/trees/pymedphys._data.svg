<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.43.0 (0)
 -->
<!-- Title: pymedphys._data Pages: 1 -->
<svg width="850pt" height="332pt"
 viewBox="0.00 0.00 850.28 332.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 328)">
<title>pymedphys._data</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-328 846.28,-328 846.28,4 -4,4"/>
<!-- pymedphys._data -->
<g id="node1" class="node">
<title>pymedphys._data</title>
<g id="a_node1"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_data/__init__.py" xlink:title="_data">
<ellipse fill="none" stroke="black" cx="397.44" cy="-234" rx="36" ry="18"/>
<text text-anchor="middle" x="397.44" y="-230.3" font-family="Times,serif" font-size="14.00">_data</text>
</a>
</g>
</g>
<!-- pymedphys._data.download -->
<g id="node2" class="node">
<title>pymedphys._data.download</title>
<g id="a_node2"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_data/download.py" xlink:title="_data.download">
<ellipse fill="none" stroke="black" cx="397.44" cy="-162" rx="83.39" ry="18"/>
<text text-anchor="middle" x="397.44" y="-158.3" font-family="Times,serif" font-size="14.00">_data.download</text>
</a>
</g>
</g>
<!-- pymedphys._data&#45;&gt;pymedphys._data.download -->
<g id="edge1" class="edge">
<title>pymedphys._data&#45;&gt;pymedphys._data.download</title>
<path fill="none" stroke="black" d="M397.44,-215.7C397.44,-207.98 397.44,-198.71 397.44,-190.11"/>
<polygon fill="black" stroke="black" points="400.94,-190.1 397.44,-180.1 393.94,-190.1 400.94,-190.1"/>
</g>
<!-- pymedphys._data.retry -->
<g id="node3" class="node">
<title>pymedphys._data.retry</title>
<g id="a_node3"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_data/retry.py" xlink:title="_data.retry">
<ellipse fill="none" stroke="black" cx="157.44" cy="-90" rx="61.99" ry="18"/>
<text text-anchor="middle" x="157.44" y="-86.3" font-family="Times,serif" font-size="14.00">_data.retry</text>
</a>
</g>
</g>
<!-- pymedphys._data.download&#45;&gt;pymedphys._data.retry -->
<g id="edge2" class="edge">
<title>pymedphys._data.download&#45;&gt;pymedphys._data.retry</title>
<path fill="none" stroke="black" d="M350.02,-147.17C309.43,-135.33 251.08,-118.31 209.26,-106.11"/>
<polygon fill="black" stroke="black" points="210.12,-102.72 199.54,-103.28 208.16,-109.44 210.12,-102.72"/>
</g>
<!-- pymedphys._data.zenodo -->
<g id="node4" class="node">
<title>pymedphys._data.zenodo</title>
<g id="a_node4"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_data/zenodo.py" xlink:title="_data.zenodo">
<ellipse fill="none" stroke="black" cx="308.44" cy="-90" rx="71.49" ry="18"/>
<text text-anchor="middle" x="308.44" y="-86.3" font-family="Times,serif" font-size="14.00">_data.zenodo</text>
</a>
</g>
</g>
<!-- pymedphys._data.download&#45;&gt;pymedphys._data.zenodo -->
<g id="edge3" class="edge">
<title>pymedphys._data.download&#45;&gt;pymedphys._data.zenodo</title>
<path fill="none" stroke="black" d="M376.35,-144.41C364.71,-135.26 350.09,-123.76 337.39,-113.77"/>
<polygon fill="black" stroke="black" points="339.4,-110.9 329.38,-107.47 335.08,-116.4 339.4,-110.9"/>
</g>
<!-- pymedphys._utilities.filehash -->
<g id="node5" class="node">
<title>pymedphys._utilities.filehash</title>
<g id="a_node5"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_utilities/filehash/__init__.py" xlink:title="_utilities.filehash">
<ellipse fill="none" stroke="black" cx="487.44" cy="-90" rx="89.88" ry="18"/>
<text text-anchor="middle" x="487.44" y="-86.3" font-family="Times,serif" font-size="14.00">_utilities.filehash</text>
</a>
</g>
</g>
<!-- pymedphys._data.download&#45;&gt;pymedphys._utilities.filehash -->
<g id="edge4" class="edge">
<title>pymedphys._data.download&#45;&gt;pymedphys._utilities.filehash</title>
<path fill="none" stroke="black" d="M418.77,-144.41C430.45,-135.32 445.11,-123.92 457.88,-113.99"/>
<polygon fill="black" stroke="black" points="460.21,-116.61 465.95,-107.71 455.91,-111.09 460.21,-116.61"/>
</g>
<!-- pymedphys._config -->
<g id="node6" class="node">
<title>pymedphys._config</title>
<g id="a_node6"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_config.py" xlink:title="_config">
<ellipse fill="none" stroke="black" cx="638.44" cy="-90" rx="42.79" ry="18"/>
<text text-anchor="middle" x="638.44" y="-86.3" font-family="Times,serif" font-size="14.00">_config</text>
</a>
</g>
</g>
<!-- pymedphys._data.download&#45;&gt;pymedphys._config -->
<g id="edge5" class="edge">
<title>pymedphys._data.download&#45;&gt;pymedphys._config</title>
<path fill="none" stroke="black" d="M447.75,-147.52C485.93,-137.14 539.65,-122.2 586.44,-108 589.72,-107 593.11,-105.95 596.51,-104.88"/>
<polygon fill="black" stroke="black" points="597.6,-108.21 606.05,-101.82 595.46,-101.54 597.6,-108.21"/>
</g>
<!-- pymedphys._utilities.filehash.core -->
<g id="node7" class="node">
<title>pymedphys._utilities.filehash.core</title>
<g id="a_node7"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_utilities/filehash/core.py" xlink:title="_utilities.filehash.core">
<ellipse fill="none" stroke="black" cx="487.44" cy="-18" rx="112.38" ry="18"/>
<text text-anchor="middle" x="487.44" y="-14.3" font-family="Times,serif" font-size="14.00">_utilities.filehash.core</text>
</a>
</g>
</g>
<!-- pymedphys._utilities.filehash&#45;&gt;pymedphys._utilities.filehash.core -->
<g id="edge6" class="edge">
<title>pymedphys._utilities.filehash&#45;&gt;pymedphys._utilities.filehash.core</title>
<path fill="none" stroke="black" d="M487.44,-71.7C487.44,-63.98 487.44,-54.71 487.44,-46.11"/>
<polygon fill="black" stroke="black" points="490.94,-46.1 487.44,-36.1 483.94,-46.1 490.94,-46.1"/>
</g>
<!-- pymedphys.data_path -->
<g id="node8" class="node">
<title>pymedphys.data_path</title>
<g id="a_node8"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_data/__init__.py" xlink:title="pymedphys.data_path">
<ellipse fill="none" stroke="black" cx="112.44" cy="-306" rx="112.38" ry="18"/>
<text text-anchor="middle" x="112.44" y="-302.3" font-family="Times,serif" font-size="14.00">pymedphys.data_path</text>
</a>
</g>
</g>
<!-- pymedphys.data_path&#45;&gt;pymedphys._data -->
<g id="edge7" class="edge">
<title>pymedphys.data_path&#45;&gt;pymedphys._data</title>
<path fill="none" stroke="black" d="M171.15,-290.58C226.37,-277.02 307.63,-257.06 356.27,-245.11"/>
<polygon fill="black" stroke="black" points="357.25,-248.47 366.13,-242.69 355.58,-241.68 357.25,-248.47"/>
</g>
<!-- pymedphys.zenodo_data_paths -->
<g id="node9" class="node">
<title>pymedphys.zenodo_data_paths</title>
<g id="a_node9"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_data/__init__.py" xlink:title="pymedphys.zenodo_data_paths">
<ellipse fill="none" stroke="black" cx="397.44" cy="-306" rx="154.87" ry="18"/>
<text text-anchor="middle" x="397.44" y="-302.3" font-family="Times,serif" font-size="14.00">pymedphys.zenodo_data_paths</text>
</a>
</g>
</g>
<!-- pymedphys.zenodo_data_paths&#45;&gt;pymedphys._data -->
<g id="edge8" class="edge">
<title>pymedphys.zenodo_data_paths&#45;&gt;pymedphys._data</title>
<path fill="none" stroke="black" d="M397.44,-287.7C397.44,-279.98 397.44,-270.71 397.44,-262.11"/>
<polygon fill="black" stroke="black" points="400.94,-262.1 397.44,-252.1 393.94,-262.1 400.94,-262.1"/>
</g>
<!-- pymedphys.zip_data_paths -->
<g id="node10" class="node">
<title>pymedphys.zip_data_paths</title>
<g id="a_node10"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_data/__init__.py" xlink:title="pymedphys.zip_data_paths">
<ellipse fill="none" stroke="black" cx="706.44" cy="-306" rx="135.68" ry="18"/>
<text text-anchor="middle" x="706.44" y="-302.3" font-family="Times,serif" font-size="14.00">pymedphys.zip_data_paths</text>
</a>
</g>
</g>
<!-- pymedphys.zip_data_paths&#45;&gt;pymedphys._data -->
<g id="edge9" class="edge">
<title>pymedphys.zip_data_paths&#45;&gt;pymedphys._data</title>
<path fill="none" stroke="black" d="M640.91,-290.15C579.96,-276.35 490.98,-256.19 439.17,-244.45"/>
<polygon fill="black" stroke="black" points="439.84,-241.02 429.32,-242.22 438.3,-247.84 439.84,-241.02"/>
</g>
</g>
</svg>
