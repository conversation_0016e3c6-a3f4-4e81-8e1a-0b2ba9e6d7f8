<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.43.0 (0)
 -->
<!-- Title: pymedphys._dicom.structure.merge Pages: 1 -->
<svg width="352pt" height="188pt"
 viewBox="0.00 0.00 352.47 188.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 184)">
<title>pymedphys._dicom.structure.merge</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-184 348.47,-184 348.47,4 -4,4"/>
<!-- pymedphys._dicom.structure.merge -->
<g id="node1" class="node">
<title>pymedphys._dicom.structure.merge</title>
<g id="a_node1"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/structure/merge.py" xlink:title="_dicom.structure.merge">
<ellipse fill="none" stroke="black" cx="172.23" cy="-90" rx="122.38" ry="18"/>
<text text-anchor="middle" x="172.23" y="-86.3" font-family="Times,serif" font-size="14.00">_dicom.structure.merge</text>
</a>
</g>
</g>
<!-- pymedphys._dicom.structure -->
<g id="node2" class="node">
<title>pymedphys._dicom.structure</title>
<g id="a_node2"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/structure/__init__.py" xlink:title="_dicom.structure">
<ellipse fill="none" stroke="black" cx="172.23" cy="-18" rx="89.08" ry="18"/>
<text text-anchor="middle" x="172.23" y="-14.3" font-family="Times,serif" font-size="14.00">_dicom.structure</text>
</a>
</g>
</g>
<!-- pymedphys._dicom.structure.merge&#45;&gt;pymedphys._dicom.structure -->
<g id="edge1" class="edge">
<title>pymedphys._dicom.structure.merge&#45;&gt;pymedphys._dicom.structure</title>
<path fill="none" stroke="black" d="M172.23,-71.7C172.23,-63.98 172.23,-54.71 172.23,-46.11"/>
<polygon fill="black" stroke="black" points="175.73,-46.1 172.23,-36.1 168.73,-46.1 175.73,-46.1"/>
</g>
<!-- pymedphys.dicom.merge_contours -->
<g id="node3" class="node">
<title>pymedphys.dicom.merge_contours</title>
<g id="a_node3"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/structure/merge.py" xlink:title="pymedphys.dicom.merge_contours">
<ellipse fill="none" stroke="black" cx="172.23" cy="-162" rx="172.47" ry="18"/>
<text text-anchor="middle" x="172.23" y="-158.3" font-family="Times,serif" font-size="14.00">pymedphys.dicom.merge_contours</text>
</a>
</g>
</g>
<!-- pymedphys.dicom.merge_contours&#45;&gt;pymedphys._dicom.structure.merge -->
<g id="edge2" class="edge">
<title>pymedphys.dicom.merge_contours&#45;&gt;pymedphys._dicom.structure.merge</title>
<path fill="none" stroke="black" d="M172.23,-143.7C172.23,-135.98 172.23,-126.71 172.23,-118.11"/>
<polygon fill="black" stroke="black" points="175.73,-118.1 172.23,-108.1 168.73,-118.1 175.73,-118.1"/>
</g>
</g>
</svg>
