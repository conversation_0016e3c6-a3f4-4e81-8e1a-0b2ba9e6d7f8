<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.43.0 (0)
 -->
<!-- Title: pymedphys._electronfactors Pages: 1 -->
<svg width="4826pt" height="260pt"
 viewBox="0.00 0.00 4826.30 260.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 256)">
<title>pymedphys._electronfactors</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-256 4822.3,-256 4822.3,4 -4,4"/>
<!-- pymedphys._electronfactors -->
<g id="node1" class="node">
<title>pymedphys._electronfactors</title>
<g id="a_node1"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_electronfactors/__init__.py" xlink:title="_electronfactors">
<ellipse fill="none" stroke="black" cx="2595.87" cy="-162" rx="85.29" ry="18"/>
<text text-anchor="middle" x="2595.87" y="-158.3" font-family="Times,serif" font-size="14.00">_electronfactors</text>
</a>
</g>
</g>
<!-- pymedphys._electronfactors.visualisation -->
<g id="node2" class="node">
<title>pymedphys._electronfactors.visualisation</title>
<g id="a_node2"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_electronfactors/visualisation.py" xlink:title="_electronfactors.visualisation">
<ellipse fill="none" stroke="black" cx="2595.87" cy="-90" rx="146.77" ry="18"/>
<text text-anchor="middle" x="2595.87" y="-86.3" font-family="Times,serif" font-size="14.00">_electronfactors.visualisation</text>
</a>
</g>
</g>
<!-- pymedphys._electronfactors&#45;&gt;pymedphys._electronfactors.visualisation -->
<g id="edge1" class="edge">
<title>pymedphys._electronfactors&#45;&gt;pymedphys._electronfactors.visualisation</title>
<path fill="none" stroke="black" d="M2595.87,-143.7C2595.87,-135.98 2595.87,-126.71 2595.87,-118.11"/>
<polygon fill="black" stroke="black" points="2599.37,-118.1 2595.87,-108.1 2592.37,-118.1 2599.37,-118.1"/>
</g>
<!-- pymedphys._electronfactors.core -->
<g id="node3" class="node">
<title>pymedphys._electronfactors.core</title>
<g id="a_node3"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_electronfactors/core.py" xlink:title="_electronfactors.core">
<ellipse fill="none" stroke="black" cx="2595.87" cy="-18" rx="108.58" ry="18"/>
<text text-anchor="middle" x="2595.87" y="-14.3" font-family="Times,serif" font-size="14.00">_electronfactors.core</text>
</a>
</g>
</g>
<!-- pymedphys._electronfactors.visualisation&#45;&gt;pymedphys._electronfactors.core -->
<g id="edge2" class="edge">
<title>pymedphys._electronfactors.visualisation&#45;&gt;pymedphys._electronfactors.core</title>
<path fill="none" stroke="black" d="M2595.87,-71.7C2595.87,-63.98 2595.87,-54.71 2595.87,-46.11"/>
<polygon fill="black" stroke="black" points="2599.37,-46.1 2595.87,-36.1 2592.37,-46.1 2599.37,-46.1"/>
</g>
<!-- pymedphys.electronfactors.calculate_percent_prediction_differences -->
<g id="node4" class="node">
<title>pymedphys.electronfactors.calculate_percent_prediction_differences</title>
<g id="a_node4"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_electronfactors/__init__.py" xlink:title="pymedphys.electronfactors.calculate_percent_prediction_differences">
<ellipse fill="none" stroke="black" cx="328.87" cy="-234" rx="328.74" ry="18"/>
<text text-anchor="middle" x="328.87" y="-230.3" font-family="Times,serif" font-size="14.00">pymedphys.electronfactors.calculate_percent_prediction_differences</text>
</a>
</g>
</g>
<!-- pymedphys.electronfactors.calculate_percent_prediction_differences&#45;&gt;pymedphys._electronfactors -->
<g id="edge3" class="edge">
<title>pymedphys.electronfactors.calculate_percent_prediction_differences&#45;&gt;pymedphys._electronfactors</title>
<path fill="none" stroke="black" d="M555.2,-220.9C592.5,-219.11 630.76,-217.4 666.87,-216 1371.23,-188.61 2220.79,-170.44 2501.11,-164.84"/>
<polygon fill="black" stroke="black" points="2501.31,-168.34 2511.24,-164.64 2501.17,-161.34 2501.31,-168.34"/>
</g>
<!-- pymedphys.electronfactors.parameterise_insert -->
<g id="node5" class="node">
<title>pymedphys.electronfactors.parameterise_insert</title>
<g id="a_node5"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_electronfactors/__init__.py" xlink:title="pymedphys.electronfactors.parameterise_insert">
<ellipse fill="none" stroke="black" cx="908.87" cy="-234" rx="233.16" ry="18"/>
<text text-anchor="middle" x="908.87" y="-230.3" font-family="Times,serif" font-size="14.00">pymedphys.electronfactors.parameterise_insert</text>
</a>
</g>
</g>
<!-- pymedphys.electronfactors.parameterise_insert&#45;&gt;pymedphys._electronfactors -->
<g id="edge4" class="edge">
<title>pymedphys.electronfactors.parameterise_insert&#45;&gt;pymedphys._electronfactors</title>
<path fill="none" stroke="black" d="M1070.25,-220.96C1097.49,-219.14 1125.48,-217.41 1151.87,-216 1660.97,-188.86 2271.8,-171.41 2501.99,-165.37"/>
<polygon fill="black" stroke="black" points="2502.1,-168.87 2512,-165.11 2501.91,-161.87 2502.1,-168.87"/>
</g>
<!-- pymedphys.electronfactors.calculate_deformability -->
<g id="node6" class="node">
<title>pymedphys.electronfactors.calculate_deformability</title>
<g id="a_node6"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_electronfactors/__init__.py" xlink:title="pymedphys.electronfactors.calculate_deformability">
<ellipse fill="none" stroke="black" cx="1408.87" cy="-234" rx="248.06" ry="18"/>
<text text-anchor="middle" x="1408.87" y="-230.3" font-family="Times,serif" font-size="14.00">pymedphys.electronfactors.calculate_deformability</text>
</a>
</g>
</g>
<!-- pymedphys.electronfactors.calculate_deformability&#45;&gt;pymedphys._electronfactors -->
<g id="edge5" class="edge">
<title>pymedphys.electronfactors.calculate_deformability&#45;&gt;pymedphys._electronfactors</title>
<path fill="none" stroke="black" d="M1583.74,-221.22C1611.22,-219.43 1639.32,-217.63 1665.87,-216 1972.99,-197.09 2337.31,-177.01 2504.16,-167.95"/>
<polygon fill="black" stroke="black" points="2504.58,-171.43 2514.37,-167.39 2504.2,-164.44 2504.58,-171.43"/>
</g>
<!-- pymedphys.electronfactors.spline_model_with_deformability -->
<g id="node7" class="node">
<title>pymedphys.electronfactors.spline_model_with_deformability</title>
<g id="a_node7"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_electronfactors/__init__.py" xlink:title="pymedphys.electronfactors.spline_model_with_deformability">
<ellipse fill="none" stroke="black" cx="1966.87" cy="-234" rx="291.65" ry="18"/>
<text text-anchor="middle" x="1966.87" y="-230.3" font-family="Times,serif" font-size="14.00">pymedphys.electronfactors.spline_model_with_deformability</text>
</a>
</g>
</g>
<!-- pymedphys.electronfactors.spline_model_with_deformability&#45;&gt;pymedphys._electronfactors -->
<g id="edge6" class="edge">
<title>pymedphys.electronfactors.spline_model_with_deformability&#45;&gt;pymedphys._electronfactors</title>
<path fill="none" stroke="black" d="M2101.42,-218.03C2225.93,-204.17 2407.16,-184 2512.12,-172.32"/>
<polygon fill="black" stroke="black" points="2512.8,-175.77 2522.35,-171.18 2512.03,-168.81 2512.8,-175.77"/>
</g>
<!-- pymedphys.electronfactors.visual_alignment_of_equivalent_ellipse -->
<g id="node8" class="node">
<title>pymedphys.electronfactors.visual_alignment_of_equivalent_ellipse</title>
<g id="a_node8"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_electronfactors/__init__.py" xlink:title="pymedphys.electronfactors.visual_alignment_of_equivalent_ellipse">
<ellipse fill="none" stroke="black" cx="2595.87" cy="-234" rx="319.24" ry="18"/>
<text text-anchor="middle" x="2595.87" y="-230.3" font-family="Times,serif" font-size="14.00">pymedphys.electronfactors.visual_alignment_of_equivalent_ellipse</text>
</a>
</g>
</g>
<!-- pymedphys.electronfactors.visual_alignment_of_equivalent_ellipse&#45;&gt;pymedphys._electronfactors -->
<g id="edge7" class="edge">
<title>pymedphys.electronfactors.visual_alignment_of_equivalent_ellipse&#45;&gt;pymedphys._electronfactors</title>
<path fill="none" stroke="black" d="M2595.87,-215.7C2595.87,-207.98 2595.87,-198.71 2595.87,-190.11"/>
<polygon fill="black" stroke="black" points="2599.37,-190.1 2595.87,-180.1 2592.37,-190.1 2599.37,-190.1"/>
</g>
<!-- pymedphys.electronfactors.convert2_ratio_perim_area -->
<g id="node9" class="node">
<title>pymedphys.electronfactors.convert2_ratio_perim_area</title>
<g id="a_node9"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_electronfactors/__init__.py" xlink:title="pymedphys.electronfactors.convert2_ratio_perim_area">
<ellipse fill="none" stroke="black" cx="3196.87" cy="-234" rx="263.75" ry="18"/>
<text text-anchor="middle" x="3196.87" y="-230.3" font-family="Times,serif" font-size="14.00">pymedphys.electronfactors.convert2_ratio_perim_area</text>
</a>
</g>
</g>
<!-- pymedphys.electronfactors.convert2_ratio_perim_area&#45;&gt;pymedphys._electronfactors -->
<g id="edge8" class="edge">
<title>pymedphys.electronfactors.convert2_ratio_perim_area&#45;&gt;pymedphys._electronfactors</title>
<path fill="none" stroke="black" d="M3069.78,-218.2C2951.89,-204.47 2779.85,-184.43 2678.73,-172.65"/>
<polygon fill="black" stroke="black" points="2678.91,-169.15 2668.57,-171.47 2678.1,-176.1 2678.91,-169.15"/>
</g>
<!-- pymedphys.electronfactors.create_transformed_mesh -->
<g id="node10" class="node">
<title>pymedphys.electronfactors.create_transformed_mesh</title>
<g id="a_node10"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_electronfactors/__init__.py" xlink:title="pymedphys.electronfactors.create_transformed_mesh">
<ellipse fill="none" stroke="black" cx="3739.87" cy="-234" rx="260.75" ry="18"/>
<text text-anchor="middle" x="3739.87" y="-230.3" font-family="Times,serif" font-size="14.00">pymedphys.electronfactors.create_transformed_mesh</text>
</a>
</g>
</g>
<!-- pymedphys.electronfactors.create_transformed_mesh&#45;&gt;pymedphys._electronfactors -->
<g id="edge9" class="edge">
<title>pymedphys.electronfactors.create_transformed_mesh&#45;&gt;pymedphys._electronfactors</title>
<path fill="none" stroke="black" d="M3555.06,-221.31C3526.53,-219.52 3497.41,-217.7 3469.87,-216 3184.07,-198.34 2845.67,-177.97 2686.81,-168.45"/>
<polygon fill="black" stroke="black" points="2686.8,-164.94 2676.61,-167.83 2686.38,-171.93 2686.8,-164.94"/>
</g>
<!-- pymedphys.electronfactors.spline_model -->
<g id="node11" class="node">
<title>pymedphys.electronfactors.spline_model</title>
<g id="a_node11"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_electronfactors/__init__.py" xlink:title="pymedphys.electronfactors.spline_model">
<ellipse fill="none" stroke="black" cx="4218.87" cy="-234" rx="200.36" ry="18"/>
<text text-anchor="middle" x="4218.87" y="-230.3" font-family="Times,serif" font-size="14.00">pymedphys.electronfactors.spline_model</text>
</a>
</g>
</g>
<!-- pymedphys.electronfactors.spline_model&#45;&gt;pymedphys._electronfactors -->
<g id="edge10" class="edge">
<title>pymedphys.electronfactors.spline_model&#45;&gt;pymedphys._electronfactors</title>
<path fill="none" stroke="black" d="M4080.93,-220.92C4057.24,-219.09 4032.86,-217.36 4009.87,-216 3513.26,-186.51 2917.49,-170.51 2690.22,-165.11"/>
<polygon fill="black" stroke="black" points="2690.08,-161.61 2680,-164.87 2689.92,-168.61 2690.08,-161.61"/>
</g>
<!-- pymedphys.electronfactors.plot_model -->
<g id="node12" class="node">
<title>pymedphys.electronfactors.plot_model</title>
<g id="a_node12"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_electronfactors/__init__.py" xlink:title="pymedphys.electronfactors.plot_model">
<ellipse fill="none" stroke="black" cx="4627.87" cy="-234" rx="190.37" ry="18"/>
<text text-anchor="middle" x="4627.87" y="-230.3" font-family="Times,serif" font-size="14.00">pymedphys.electronfactors.plot_model</text>
</a>
</g>
</g>
<!-- pymedphys.electronfactors.plot_model&#45;&gt;pymedphys._electronfactors -->
<g id="edge11" class="edge">
<title>pymedphys.electronfactors.plot_model&#45;&gt;pymedphys._electronfactors</title>
<path fill="none" stroke="black" d="M4497.5,-220.86C4474.32,-219 4450.4,-217.28 4427.87,-216 3763.74,-178.2 2962.54,-166.79 2691.07,-163.88"/>
<polygon fill="black" stroke="black" points="2690.9,-160.38 2680.86,-163.77 2690.82,-167.38 2690.9,-160.38"/>
</g>
</g>
</svg>
