<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.43.0 (0)
 -->
<!-- Title: pymedphys._dicom.dose Pages: 1 -->
<svg width="1531pt" height="476pt"
 viewBox="0.00 0.00 1531.42 476.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 472)">
<title>pymedphys._dicom.dose</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-472 1527.42,-472 1527.42,4 -4,4"/>
<!-- pymedphys._dicom.dose -->
<g id="node1" class="node">
<title>pymedphys._dicom.dose</title>
<g id="a_node1"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/dose.py" xlink:title="_dicom.dose">
<ellipse fill="none" stroke="black" cx="680.74" cy="-378" rx="67.69" ry="18"/>
<text text-anchor="middle" x="680.74" y="-374.3" font-family="Times,serif" font-size="14.00">_dicom.dose</text>
</a>
</g>
</g>
<!-- pymedphys._dicom.constants -->
<g id="node2" class="node">
<title>pymedphys._dicom.constants</title>
<g id="a_node2"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/constants/__init__.py" xlink:title="_dicom.constants">
<ellipse fill="none" stroke="black" cx="390.74" cy="-306" rx="90.98" ry="18"/>
<text text-anchor="middle" x="390.74" y="-302.3" font-family="Times,serif" font-size="14.00">_dicom.constants</text>
</a>
</g>
</g>
<!-- pymedphys._dicom.dose&#45;&gt;pymedphys._dicom.constants -->
<g id="edge1" class="edge">
<title>pymedphys._dicom.dose&#45;&gt;pymedphys._dicom.constants</title>
<path fill="none" stroke="black" d="M632.53,-365.36C584.16,-353.69 509.35,-335.63 455.76,-322.69"/>
<polygon fill="black" stroke="black" points="456.55,-319.28 446.01,-320.34 454.91,-326.09 456.55,-319.28"/>
</g>
<!-- pymedphys._dicom.structure -->
<g id="node3" class="node">
<title>pymedphys._dicom.structure</title>
<g id="a_node3"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/structure/__init__.py" xlink:title="_dicom.structure">
<ellipse fill="none" stroke="black" cx="588.74" cy="-306" rx="89.08" ry="18"/>
<text text-anchor="middle" x="588.74" y="-302.3" font-family="Times,serif" font-size="14.00">_dicom.structure</text>
</a>
</g>
</g>
<!-- pymedphys._dicom.dose&#45;&gt;pymedphys._dicom.structure -->
<g id="edge2" class="edge">
<title>pymedphys._dicom.dose&#45;&gt;pymedphys._dicom.structure</title>
<path fill="none" stroke="black" d="M659.4,-360.76C647.27,-351.53 631.89,-339.83 618.57,-329.7"/>
<polygon fill="black" stroke="black" points="620.68,-326.9 610.6,-323.63 616.44,-332.47 620.68,-326.9"/>
</g>
<!-- pymedphys._dicom.coords -->
<g id="node4" class="node">
<title>pymedphys._dicom.coords</title>
<g id="a_node4"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/coords.py" xlink:title="_dicom.coords">
<ellipse fill="none" stroke="black" cx="772.74" cy="-306" rx="76.89" ry="18"/>
<text text-anchor="middle" x="772.74" y="-302.3" font-family="Times,serif" font-size="14.00">_dicom.coords</text>
</a>
</g>
</g>
<!-- pymedphys._dicom.dose&#45;&gt;pymedphys._dicom.coords -->
<g id="edge3" class="edge">
<title>pymedphys._dicom.dose&#45;&gt;pymedphys._dicom.coords</title>
<path fill="none" stroke="black" d="M702.08,-360.76C714.3,-351.47 729.82,-339.66 743.19,-329.48"/>
<polygon fill="black" stroke="black" points="745.36,-332.23 751.2,-323.39 741.12,-326.66 745.36,-332.23"/>
</g>
<!-- pymedphys._dicom.rtplan -->
<g id="node5" class="node">
<title>pymedphys._dicom.rtplan</title>
<g id="a_node5"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/rtplan/__init__.py" xlink:title="_dicom.rtplan">
<ellipse fill="none" stroke="black" cx="941.74" cy="-306" rx="74.19" ry="18"/>
<text text-anchor="middle" x="941.74" y="-302.3" font-family="Times,serif" font-size="14.00">_dicom.rtplan</text>
</a>
</g>
</g>
<!-- pymedphys._dicom.dose&#45;&gt;pymedphys._dicom.rtplan -->
<g id="edge4" class="edge">
<title>pymedphys._dicom.dose&#45;&gt;pymedphys._dicom.rtplan</title>
<path fill="none" stroke="black" d="M726.5,-364.73C770.31,-352.98 836.58,-335.2 884,-322.49"/>
<polygon fill="black" stroke="black" points="885.07,-325.82 893.82,-319.85 883.26,-319.06 885.07,-325.82"/>
</g>
<!-- pymedphys._dicom.constants.core -->
<g id="node6" class="node">
<title>pymedphys._dicom.constants.core</title>
<g id="a_node6"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/constants/core.py" xlink:title="_dicom.constants.core">
<ellipse fill="none" stroke="black" cx="113.74" cy="-234" rx="113.98" ry="18"/>
<text text-anchor="middle" x="113.74" y="-230.3" font-family="Times,serif" font-size="14.00">_dicom.constants.core</text>
</a>
</g>
</g>
<!-- pymedphys._dicom.constants&#45;&gt;pymedphys._dicom.constants.core -->
<g id="edge5" class="edge">
<title>pymedphys._dicom.constants&#45;&gt;pymedphys._dicom.constants.core</title>
<path fill="none" stroke="black" d="M337,-291.42C292.64,-280.21 229.44,-264.24 181.59,-252.15"/>
<polygon fill="black" stroke="black" points="182.18,-248.69 171.63,-249.63 180.47,-255.47 182.18,-248.69"/>
</g>
<!-- pymedphys._dicom.constants.orientation -->
<g id="node7" class="node">
<title>pymedphys._dicom.constants.orientation</title>
<g id="a_node7"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/constants/orientation.py" xlink:title="_dicom.constants.orientation">
<ellipse fill="none" stroke="black" cx="390.74" cy="-234" rx="144.87" ry="18"/>
<text text-anchor="middle" x="390.74" y="-230.3" font-family="Times,serif" font-size="14.00">_dicom.constants.orientation</text>
</a>
</g>
</g>
<!-- pymedphys._dicom.constants&#45;&gt;pymedphys._dicom.constants.orientation -->
<g id="edge6" class="edge">
<title>pymedphys._dicom.constants&#45;&gt;pymedphys._dicom.constants.orientation</title>
<path fill="none" stroke="black" d="M390.74,-287.7C390.74,-279.98 390.74,-270.71 390.74,-262.11"/>
<polygon fill="black" stroke="black" points="394.24,-262.1 390.74,-252.1 387.24,-262.1 394.24,-262.1"/>
</g>
<!-- pymedphys._dicom.constants.uuid -->
<g id="node8" class="node">
<title>pymedphys._dicom.constants.uuid</title>
<g id="a_node8"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/constants/uuid.py" xlink:title="_dicom.constants.uuid">
<ellipse fill="none" stroke="black" cx="667.74" cy="-234" rx="114.28" ry="18"/>
<text text-anchor="middle" x="667.74" y="-230.3" font-family="Times,serif" font-size="14.00">_dicom.constants.uuid</text>
</a>
</g>
</g>
<!-- pymedphys._dicom.constants&#45;&gt;pymedphys._dicom.constants.uuid -->
<g id="edge7" class="edge">
<title>pymedphys._dicom.constants&#45;&gt;pymedphys._dicom.constants.uuid</title>
<path fill="none" stroke="black" d="M444.48,-291.42C488.84,-280.21 552.04,-264.24 599.89,-252.15"/>
<polygon fill="black" stroke="black" points="601.01,-255.47 609.85,-249.63 599.3,-248.69 601.01,-255.47"/>
</g>
<!-- pymedphys._dicom.rtplan.build -->
<g id="node9" class="node">
<title>pymedphys._dicom.rtplan.build</title>
<g id="a_node9"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/rtplan/build.py" xlink:title="_dicom.rtplan.build">
<ellipse fill="none" stroke="black" cx="921.74" cy="-234" rx="100.98" ry="18"/>
<text text-anchor="middle" x="921.74" y="-230.3" font-family="Times,serif" font-size="14.00">_dicom.rtplan.build</text>
</a>
</g>
</g>
<!-- pymedphys._dicom.rtplan&#45;&gt;pymedphys._dicom.rtplan.build -->
<g id="edge8" class="edge">
<title>pymedphys._dicom.rtplan&#45;&gt;pymedphys._dicom.rtplan.build</title>
<path fill="none" stroke="black" d="M936.8,-287.7C934.57,-279.9 931.89,-270.51 929.41,-261.83"/>
<polygon fill="black" stroke="black" points="932.74,-260.76 926.63,-252.1 926.01,-262.68 932.74,-260.76"/>
</g>
<!-- pymedphys._dicom.rtplan.adjust -->
<g id="node10" class="node">
<title>pymedphys._dicom.rtplan.adjust</title>
<g id="a_node10"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/rtplan/adjust.py" xlink:title="_dicom.rtplan.adjust">
<ellipse fill="none" stroke="black" cx="1146.74" cy="-234" rx="105.88" ry="18"/>
<text text-anchor="middle" x="1146.74" y="-230.3" font-family="Times,serif" font-size="14.00">_dicom.rtplan.adjust</text>
</a>
</g>
</g>
<!-- pymedphys._dicom.rtplan&#45;&gt;pymedphys._dicom.rtplan.adjust -->
<g id="edge9" class="edge">
<title>pymedphys._dicom.rtplan&#45;&gt;pymedphys._dicom.rtplan.adjust</title>
<path fill="none" stroke="black" d="M982.73,-291C1014.1,-280.29 1057.68,-265.41 1092.02,-253.68"/>
<polygon fill="black" stroke="black" points="1093.57,-256.85 1101.91,-250.31 1091.31,-250.23 1093.57,-256.85"/>
</g>
<!-- pymedphys._dicom.rtplan.core -->
<g id="node11" class="node">
<title>pymedphys._dicom.rtplan.core</title>
<g id="a_node11"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/rtplan/core.py" xlink:title="_dicom.rtplan.core">
<ellipse fill="none" stroke="black" cx="1146.74" cy="-162" rx="96.68" ry="18"/>
<text text-anchor="middle" x="1146.74" y="-158.3" font-family="Times,serif" font-size="14.00">_dicom.rtplan.core</text>
</a>
</g>
</g>
<!-- pymedphys._dicom.rtplan.adjust&#45;&gt;pymedphys._dicom.rtplan.core -->
<g id="edge11" class="edge">
<title>pymedphys._dicom.rtplan.adjust&#45;&gt;pymedphys._dicom.rtplan.core</title>
<path fill="none" stroke="black" d="M1146.74,-215.7C1146.74,-207.98 1146.74,-198.71 1146.74,-190.11"/>
<polygon fill="black" stroke="black" points="1150.24,-190.1 1146.74,-180.1 1143.24,-190.1 1150.24,-190.1"/>
</g>
<!-- pymedphys._utilities.transforms -->
<g id="node12" class="node">
<title>pymedphys._utilities.transforms</title>
<g id="a_node12"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_utilities/transforms/__init__.py" xlink:title="_utilities.transforms">
<ellipse fill="none" stroke="black" cx="1146.74" cy="-90" rx="104.78" ry="18"/>
<text text-anchor="middle" x="1146.74" y="-86.3" font-family="Times,serif" font-size="14.00">_utilities.transforms</text>
</a>
</g>
</g>
<!-- pymedphys._dicom.rtplan.core&#45;&gt;pymedphys._utilities.transforms -->
<g id="edge10" class="edge">
<title>pymedphys._dicom.rtplan.core&#45;&gt;pymedphys._utilities.transforms</title>
<path fill="none" stroke="black" d="M1146.74,-143.7C1146.74,-135.98 1146.74,-126.71 1146.74,-118.11"/>
<polygon fill="black" stroke="black" points="1150.24,-118.1 1146.74,-108.1 1143.24,-118.1 1150.24,-118.1"/>
</g>
<!-- pymedphys._utilities.transforms.bipolar -->
<g id="node13" class="node">
<title>pymedphys._utilities.transforms.bipolar</title>
<g id="a_node13"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_utilities/transforms/bipolar.py" xlink:title="_utilities.transforms.bipolar">
<ellipse fill="none" stroke="black" cx="1146.74" cy="-18" rx="140.28" ry="18"/>
<text text-anchor="middle" x="1146.74" y="-14.3" font-family="Times,serif" font-size="14.00">_utilities.transforms.bipolar</text>
</a>
</g>
</g>
<!-- pymedphys._utilities.transforms&#45;&gt;pymedphys._utilities.transforms.bipolar -->
<g id="edge12" class="edge">
<title>pymedphys._utilities.transforms&#45;&gt;pymedphys._utilities.transforms.bipolar</title>
<path fill="none" stroke="black" d="M1146.74,-71.7C1146.74,-63.98 1146.74,-54.71 1146.74,-46.11"/>
<polygon fill="black" stroke="black" points="1150.24,-46.1 1146.74,-36.1 1143.24,-46.1 1150.24,-46.1"/>
</g>
<!-- pymedphys.dicom.depth_dose -->
<g id="node14" class="node">
<title>pymedphys.dicom.depth_dose</title>
<g id="a_node14"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/dose.py" xlink:title="pymedphys.dicom.depth_dose">
<ellipse fill="none" stroke="black" cx="199.74" cy="-450" rx="150.27" ry="18"/>
<text text-anchor="middle" x="199.74" y="-446.3" font-family="Times,serif" font-size="14.00">pymedphys.dicom.depth_dose</text>
</a>
</g>
</g>
<!-- pymedphys.dicom.depth_dose&#45;&gt;pymedphys._dicom.dose -->
<g id="edge13" class="edge">
<title>pymedphys.dicom.depth_dose&#45;&gt;pymedphys._dicom.dose</title>
<path fill="none" stroke="black" d="M291.06,-435.71C384.92,-422.05 528.89,-401.1 613.05,-388.85"/>
<polygon fill="black" stroke="black" points="613.61,-392.31 623,-387.4 612.6,-385.38 613.61,-392.31"/>
</g>
<!-- pymedphys.dicom.profile -->
<g id="node15" class="node">
<title>pymedphys.dicom.profile</title>
<g id="a_node15"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/dose.py" xlink:title="pymedphys.dicom.profile">
<ellipse fill="none" stroke="black" cx="495.74" cy="-450" rx="127.28" ry="18"/>
<text text-anchor="middle" x="495.74" y="-446.3" font-family="Times,serif" font-size="14.00">pymedphys.dicom.profile</text>
</a>
</g>
</g>
<!-- pymedphys.dicom.profile&#45;&gt;pymedphys._dicom.dose -->
<g id="edge14" class="edge">
<title>pymedphys.dicom.profile&#45;&gt;pymedphys._dicom.dose</title>
<path fill="none" stroke="black" d="M538.18,-432.94C566.74,-422.13 604.42,-407.88 633.86,-396.74"/>
<polygon fill="black" stroke="black" points="635.39,-399.9 643.51,-393.09 632.91,-393.35 635.39,-399.9"/>
</g>
<!-- pymedphys.dicom.zyx_and_dose_from_dataset -->
<g id="node16" class="node">
<title>pymedphys.dicom.zyx_and_dose_from_dataset</title>
<g id="a_node16"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/dose.py" xlink:title="pymedphys.dicom.zyx_and_dose_from_dataset">
<ellipse fill="none" stroke="black" cx="866.74" cy="-450" rx="225.56" ry="18"/>
<text text-anchor="middle" x="866.74" y="-446.3" font-family="Times,serif" font-size="14.00">pymedphys.dicom.zyx_and_dose_from_dataset</text>
</a>
</g>
</g>
<!-- pymedphys.dicom.zyx_and_dose_from_dataset&#45;&gt;pymedphys._dicom.dose -->
<g id="edge15" class="edge">
<title>pymedphys.dicom.zyx_and_dose_from_dataset&#45;&gt;pymedphys._dicom.dose</title>
<path fill="none" stroke="black" d="M822.19,-432.23C793.61,-421.48 756.56,-407.53 727.54,-396.61"/>
<polygon fill="black" stroke="black" points="728.63,-393.28 718.04,-393.04 726.16,-399.83 728.63,-393.28"/>
</g>
<!-- pymedphys.dicom.dicom_dose_interpolate -->
<g id="node17" class="node">
<title>pymedphys.dicom.dicom_dose_interpolate</title>
<g id="a_node17"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_dicom/dose.py" xlink:title="pymedphys.dicom.dicom_dose_interpolate">
<ellipse fill="none" stroke="black" cx="1316.74" cy="-450" rx="206.86" ry="18"/>
<text text-anchor="middle" x="1316.74" y="-446.3" font-family="Times,serif" font-size="14.00">pymedphys.dicom.dicom_dose_interpolate</text>
</a>
</g>
</g>
<!-- pymedphys.dicom.dicom_dose_interpolate&#45;&gt;pymedphys._dicom.dose -->
<g id="edge16" class="edge">
<title>pymedphys.dicom.dicom_dose_interpolate&#45;&gt;pymedphys._dicom.dose</title>
<path fill="none" stroke="black" d="M1194.11,-435.5C1062.81,-421.05 858.94,-398.61 751.9,-386.83"/>
<polygon fill="black" stroke="black" points="752.17,-383.34 741.85,-385.73 751.41,-390.3 752.17,-383.34"/>
</g>
</g>
</svg>
