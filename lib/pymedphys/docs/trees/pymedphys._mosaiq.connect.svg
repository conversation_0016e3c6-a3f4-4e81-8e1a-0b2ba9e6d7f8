<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.43.0 (0)
 -->
<!-- Title: pymedphys._mosaiq.connect Pages: 1 -->
<svg width="582pt" height="116pt"
 viewBox="0.00 0.00 582.18 116.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 112)">
<title>pymedphys._mosaiq.connect</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-112 578.18,-112 578.18,4 -4,4"/>
<!-- pymedphys._mosaiq.connect -->
<g id="node1" class="node">
<title>pymedphys._mosaiq.connect</title>
<g id="a_node1"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_mosaiq/connect.py" xlink:title="_mosaiq.connect">
<ellipse fill="none" stroke="black" cx="287.09" cy="-18" rx="87.18" ry="18"/>
<text text-anchor="middle" x="287.09" y="-14.3" font-family="Times,serif" font-size="14.00">_mosaiq.connect</text>
</a>
</g>
</g>
<!-- pymedphys.mosaiq.execute -->
<g id="node2" class="node">
<title>pymedphys.mosaiq.execute</title>
<g id="a_node2"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_mosaiq/connect.py" xlink:title="pymedphys.mosaiq.execute">
<ellipse fill="none" stroke="black" cx="139.09" cy="-90" rx="139.18" ry="18"/>
<text text-anchor="middle" x="139.09" y="-86.3" font-family="Times,serif" font-size="14.00">pymedphys.mosaiq.execute</text>
</a>
</g>
</g>
<!-- pymedphys.mosaiq.execute&#45;&gt;pymedphys._mosaiq.connect -->
<g id="edge1" class="edge">
<title>pymedphys.mosaiq.execute&#45;&gt;pymedphys._mosaiq.connect</title>
<path fill="none" stroke="black" d="M174.16,-72.41C195.25,-62.43 222.24,-49.67 244.55,-39.12"/>
<polygon fill="black" stroke="black" points="246.25,-42.19 253.79,-34.75 243.25,-35.86 246.25,-42.19"/>
</g>
<!-- pymedphys.mosaiq.connect -->
<g id="node3" class="node">
<title>pymedphys.mosaiq.connect</title>
<g id="a_node3"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_mosaiq/connect.py" xlink:title="pymedphys.mosaiq.connect">
<ellipse fill="none" stroke="black" cx="435.09" cy="-90" rx="139.18" ry="18"/>
<text text-anchor="middle" x="435.09" y="-86.3" font-family="Times,serif" font-size="14.00">pymedphys.mosaiq.connect</text>
</a>
</g>
</g>
<!-- pymedphys.mosaiq.connect&#45;&gt;pymedphys._mosaiq.connect -->
<g id="edge2" class="edge">
<title>pymedphys.mosaiq.connect&#45;&gt;pymedphys._mosaiq.connect</title>
<path fill="none" stroke="black" d="M400.01,-72.41C378.92,-62.43 351.93,-49.67 329.63,-39.12"/>
<polygon fill="black" stroke="black" points="330.92,-35.86 320.39,-34.75 327.93,-42.19 330.92,-35.86"/>
</g>
</g>
</svg>
