<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.43.0 (0)
 -->
<!-- Title: pymedphys._gamma.implementation.shell Pages: 1 -->
<svg width="444pt" height="260pt"
 viewBox="0.00 0.00 444.33 260.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 256)">
<title>pymedphys._gamma.implementation.shell</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-256 440.33,-256 440.33,4 -4,4"/>
<!-- pymedphys._gamma.implementation.shell -->
<g id="node1" class="node">
<title>pymedphys._gamma.implementation.shell</title>
<g id="a_node1"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_gamma/implementation/shell.py" xlink:title="_gamma.implementation.shell">
<ellipse fill="none" stroke="black" cx="217.84" cy="-162" rx="150.27" ry="18"/>
<text text-anchor="middle" x="217.84" y="-158.3" font-family="Times,serif" font-size="14.00">_gamma.implementation.shell</text>
</a>
</g>
</g>
<!-- pymedphys._utilities.createshells -->
<g id="node2" class="node">
<title>pymedphys._utilities.createshells</title>
<g id="a_node2"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_utilities/createshells.py" xlink:title="_utilities.createshells">
<ellipse fill="none" stroke="black" cx="109.84" cy="-90" rx="109.68" ry="18"/>
<text text-anchor="middle" x="109.84" y="-86.3" font-family="Times,serif" font-size="14.00">_utilities.createshells</text>
</a>
</g>
</g>
<!-- pymedphys._gamma.implementation.shell&#45;&gt;pymedphys._utilities.createshells -->
<g id="edge1" class="edge">
<title>pymedphys._gamma.implementation.shell&#45;&gt;pymedphys._utilities.createshells</title>
<path fill="none" stroke="black" d="M191.97,-144.23C177.68,-134.97 159.74,-123.34 144.27,-113.32"/>
<polygon fill="black" stroke="black" points="145.79,-110.13 135.5,-107.63 141.99,-116.01 145.79,-110.13"/>
</g>
<!-- pymedphys._gamma.utilities -->
<g id="node3" class="node">
<title>pymedphys._gamma.utilities</title>
<g id="a_node3"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_gamma/utilities/__init__.py" xlink:title="_gamma.utilities">
<ellipse fill="none" stroke="black" cx="325.84" cy="-90" rx="87.99" ry="18"/>
<text text-anchor="middle" x="325.84" y="-86.3" font-family="Times,serif" font-size="14.00">_gamma.utilities</text>
</a>
</g>
</g>
<!-- pymedphys._gamma.implementation.shell&#45;&gt;pymedphys._gamma.utilities -->
<g id="edge2" class="edge">
<title>pymedphys._gamma.implementation.shell&#45;&gt;pymedphys._gamma.utilities</title>
<path fill="none" stroke="black" d="M243.71,-144.23C258.11,-134.9 276.21,-123.17 291.75,-113.1"/>
<polygon fill="black" stroke="black" points="294.07,-115.76 300.56,-107.39 290.26,-109.89 294.07,-115.76"/>
</g>
<!-- pymedphys._gamma.utilities.core -->
<g id="node4" class="node">
<title>pymedphys._gamma.utilities.core</title>
<g id="a_node4"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_gamma/utilities/core.py" xlink:title="_gamma.utilities.core">
<ellipse fill="none" stroke="black" cx="325.84" cy="-18" rx="110.48" ry="18"/>
<text text-anchor="middle" x="325.84" y="-14.3" font-family="Times,serif" font-size="14.00">_gamma.utilities.core</text>
</a>
</g>
</g>
<!-- pymedphys._gamma.utilities&#45;&gt;pymedphys._gamma.utilities.core -->
<g id="edge3" class="edge">
<title>pymedphys._gamma.utilities&#45;&gt;pymedphys._gamma.utilities.core</title>
<path fill="none" stroke="black" d="M325.84,-71.7C325.84,-63.98 325.84,-54.71 325.84,-46.11"/>
<polygon fill="black" stroke="black" points="329.34,-46.1 325.84,-36.1 322.34,-46.1 329.34,-46.1"/>
</g>
<!-- pymedphys.gamma -->
<g id="node5" class="node">
<title>pymedphys.gamma</title>
<g id="a_node5"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_gamma/implementation/shell.py" xlink:title="pymedphys.gamma">
<ellipse fill="none" stroke="black" cx="217.84" cy="-234" rx="100.98" ry="18"/>
<text text-anchor="middle" x="217.84" y="-230.3" font-family="Times,serif" font-size="14.00">pymedphys.gamma</text>
</a>
</g>
</g>
<!-- pymedphys.gamma&#45;&gt;pymedphys._gamma.implementation.shell -->
<g id="edge4" class="edge">
<title>pymedphys.gamma&#45;&gt;pymedphys._gamma.implementation.shell</title>
<path fill="none" stroke="black" d="M217.84,-215.7C217.84,-207.98 217.84,-198.71 217.84,-190.11"/>
<polygon fill="black" stroke="black" points="221.34,-190.1 217.84,-180.1 214.34,-190.1 221.34,-190.1"/>
</g>
</g>
</svg>
