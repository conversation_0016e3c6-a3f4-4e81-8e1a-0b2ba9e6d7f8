<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.43.0 (0)
 -->
<!-- Title: pymedphys._version Pages: 1 -->
<svg width="254pt" height="116pt"
 viewBox="0.00 0.00 253.68 116.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 112)">
<title>pymedphys._version</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-112 249.68,-112 249.68,4 -4,4"/>
<!-- pymedphys._version -->
<g id="node1" class="node">
<title>pymedphys._version</title>
<g id="a_node1"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_version.py" xlink:title="_version">
<ellipse fill="none" stroke="black" cx="122.84" cy="-18" rx="49.29" ry="18"/>
<text text-anchor="middle" x="122.84" y="-14.3" font-family="Times,serif" font-size="14.00">_version</text>
</a>
</g>
</g>
<!-- pymedphys.version_info -->
<g id="node2" class="node">
<title>pymedphys.version_info</title>
<g id="a_node2"><a xlink:href="https://github.com/pymedphys/pymedphys/tree/main/lib/pymedphys/_version.py" xlink:title="pymedphys.version_info">
<ellipse fill="none" stroke="black" cx="122.84" cy="-90" rx="122.68" ry="18"/>
<text text-anchor="middle" x="122.84" y="-86.3" font-family="Times,serif" font-size="14.00">pymedphys.version_info</text>
</a>
</g>
</g>
<!-- pymedphys.version_info&#45;&gt;pymedphys._version -->
<g id="edge1" class="edge">
<title>pymedphys.version_info&#45;&gt;pymedphys._version</title>
<path fill="none" stroke="black" d="M122.84,-71.7C122.84,-63.98 122.84,-54.71 122.84,-46.11"/>
<polygon fill="black" stroke="black" points="126.34,-46.1 122.84,-36.1 119.34,-46.1 126.34,-46.1"/>
</g>
</g>
</svg>
