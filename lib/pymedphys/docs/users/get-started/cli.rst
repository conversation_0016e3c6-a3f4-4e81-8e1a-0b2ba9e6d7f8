============================
Using the CLI
============================

PyMedPhys exposes a range of its library functions to the command line as a
command line interface (CLI). This provides the benefit of being able to call
PyMedPhys functions from a range of locations such as:

* Other programming languages
* Windows scheduler
* ``.bat`` and ``.sh`` files

You can find the technical reference for the command line tools
:doc:`here<../ref/cli/index>`.
