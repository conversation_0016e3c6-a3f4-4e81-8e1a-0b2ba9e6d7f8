{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Multilinear Interpolation Comparison"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install interpolation\n", "%pip install numba==0.59.1\n", "\n", "import timeit\n", "from typing import Sequence\n", "\n", "from matplotlib import pyplot as plt\n", "import numpy as np\n", "import scipy\n", "import interpolation\n", "\n", "import pymedphys\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def interp3d_econforge(axes, values, points_interp):\n", "    grid = interpolation.splines.CGrid(*axes)\n", "    return interpolation.splines.eval_linear(grid, values, points_interp)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def interp3d_scipy(axes, values, points_interp):\n", "    interpolator = scipy.interpolate.RegularGridInterpolator(axes, values)\n", "    return interpolator(points_interp)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def interp3d_pymedphys_skip_checks(axes, values, points_interp):\n", "    return pymedphys.interpolate.interp(\n", "        axes, values, points_interp=points_interp, skip_checks=True\n", "    )\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["implementations = {\n", "    \"PyMedPhys default\": pymedphys.interpolate.interp,\n", "    \"PyMedPhys skip checks\": interp3d_pymedphys_skip_checks,\n", "    \"PyMedPhys low-level API\": pymedphys.interp_linear_3d,\n", "    \"EconForge\": interp3d_econforge,\n", "    \"Scipy\": interp3d_scipy,\n", "}\n", "\n", "\n", "def benchmark(\n", "    n_values: Sequence[int],\n", "    interpolation_multiples: Sequence[int],\n", "    fixed_n: int,\n", "    fixed_multiple: int,\n", "    implementations: dict,\n", "):\n", "    results = {\n", "        \"Performance with varying N\": {\n", "            \"N values\": n_values,\n", "            \"times\": {name: [] for name in implementations.keys()},\n", "            \"fixed multiple\": fixed_multiple,\n", "        },\n", "        \"Performance with varying interpolation multiple\": {\n", "            \"interpolation multiples\": interpolation_multiples,\n", "            \"times\": {name: [] for name in implementations.keys()},\n", "            \"fixed n\": fixed_n,\n", "        },\n", "    }\n", "\n", "    for n in n_values:\n", "        run_combo(\n", "            n, fixed_multiple, implementations, results[\"Performance with varying N\"]\n", "        )\n", "\n", "    for multiple in interpolation_multiples:\n", "        run_combo(\n", "            fixed_n,\n", "            multiple,\n", "            implementations,\n", "            results[\"Performance with varying interpolation multiple\"],\n", "        )\n", "\n", "    return results\n", "\n", "\n", "def run_combo(n, multiple, implementations, result):\n", "    x = y = z = np.linspace(0, 1, n, dtype=np.float64)\n", "    values = np.random.rand(n, n, n)\n", "\n", "    xi = yi = zi = np.linspace(0, 1, n * multiple)\n", "    points = np.column_stack(\n", "        [mgrid.ravel() for mgrid in np.meshgrid(xi, yi, zi, indexing=\"ij\")]\n", "    )\n", "\n", "    for name, f_interp in implementations.items():\n", "        time = (\n", "            timeit.timeit(\n", "                lambda: f_interp(tuple((x, y, z)), values, points_interp=points),\n", "                number=3,\n", "            )\n", "            / 3\n", "        )\n", "        result[\"times\"][name].append(time)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def plot_results(results):\n", "    _, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "    # Left plot: Performance vs n for fixed multiple\n", "    for name, times in results[\"Performance with varying N\"][\"times\"].items():\n", "        ax1.plot(\n", "            np.array(results[\"Performance with varying N\"][\"N values\"], dtype=np.int32)\n", "            ** 3,\n", "            np.array(times),\n", "            label=name,\n", "            marker=\"o\",\n", "        )\n", "\n", "    ax1.set_xlabel(\"Number of known points (N)\")\n", "    ax1.set_ylabel(\"Time (seconds)\")\n", "    ax1.set_title(\n", "        f\"Performance vs. Number of Known Points (multiple={results['Performance with varying N']['fixed multiple']})\"\n", "    )\n", "    ax1.legend()\n", "    ax1.set_xscale(\"log\")\n", "    ax1.set_yscale(\"log\")\n", "\n", "    # Left plot: Performance vs n for fixed multiple\n", "    for name, times in results[\"Performance with varying interpolation multiple\"][\n", "        \"times\"\n", "    ].items():\n", "        ax2.plot(\n", "            np.array(\n", "                results[\"Performance with varying interpolation multiple\"][\n", "                    \"interpolation multiples\"\n", "                ],\n", "                dtype=np.int32,\n", "            ),\n", "            np.array(times),\n", "            label=name,\n", "            marker=\"o\",\n", "        )\n", "\n", "    ax2.set_xlabel(\"Interpolation Multiple\")\n", "    ax2.set_ylabel(\"Time (seconds)\")\n", "    ax2.set_title(\n", "        f\"Performance vs. Interpolation Multiple (n={results['Performance with varying interpolation multiple']['fixed n']**3:,})\"\n", "    )\n", "    ax2.legend()\n", "    plt.xticks(\n", "        results[\"Performance with varying interpolation multiple\"][\n", "            \"interpolation multiples\"\n", "        ]\n", "    )\n", "    ax2.set_yscale(\"log\")\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run the benchmark\n", "n_values = [10, 30, 100, 150]\n", "interpolation_multiples = [2, 3, 4]\n", "results = benchmark(\n", "    n_values,\n", "    interpolation_multiples,\n", "    fixed_n=100,\n", "    fixed_multiple=3,\n", "    implementations=implementations,\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot the results\n", "plot_results(results)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}