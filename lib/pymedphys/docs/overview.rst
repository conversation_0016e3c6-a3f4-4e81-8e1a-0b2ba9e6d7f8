======================
Overview
======================

Resources to assist users of the PyMedPhys library. If you're experienced with
Python but brand new to PyMedPhys, start
:doc:`here<users/get-started/quick-start>`.

The **Library Users Guide** is separated into following sub-categories:

- **Tutorials (coming!)**

  - Lessons that take you through complete projects and illustrate what you can
    achieve by writing PyMedPhys-based Python software.
  - These are aimed at you if you don't yet know what you can achieve with
    PyMedPhys but you would like to learn.

|

- :doc:`How-to Guides<users/howto/index>`

  - Guides and recipes for common problems and tasks. These are aimed for you
    if you already know how to use Python and PyMedPhys and are looking for
    direction on a specific task you are trying to solve.

|

- :doc:`Reference<users/ref/index>`

  - Technical reference for the PyMedPhys library (modules, functions and
    classes). This is where you will find listed information for the exposed
    functionality of the PyMedPhys library.
  - This is aimed at you if you know exactly the feature you would like to use,
    you just want to see what inputs it requires and what outputs it gives.

|

- :doc:`Background<users/background/index>`

  - Explanations and discussions of key topics and concepts. This is aimed at
    you if you are looking to be able to think about PyMedPhys and its
    implementations at a higher level and understand more about them.
