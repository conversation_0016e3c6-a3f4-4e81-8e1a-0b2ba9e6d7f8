================================
Setting up OpenSSH on Windows 10
================================

If you want to be able to run git clone with SSH, instead of having to type a
password every time you can either use git's ssh manager, but this will not
work within your standard command prompt or within other tools such as VSCode.

For now, a series of links have been provided within
https://github.com/pymedphys/pymedphys/issues/649. Use those links to determine
how to set up git with OpenSSH on Windows.
