{"alphanumeric-machine-id-icom.zip": "719ea5f103f5af50d3d068498106e87972d353fa", "app_build.zip": "58c7eb4dda91091466cf792399a7aa7ceb11911a", "auto-segmentation-dicom/mappings.zip": "86f9f838546789924b1942991f4a9fd461c98968", "auto-segmentation/00.zip": "9fa919ec36750a304b7c3aebf0c9e1c60029a97e", "auto-segmentation/01.zip": "405e0a16f6937846e01374dd39cbb433be77eb5c", "auto-segmentation/02.zip": "0395a6b7049d8491bcec36906762ab2a121e8dc3", "auto-segmentation/03.zip": "f5fa940bedb7cce3fda978c7d6960cb19c31d6ee", "auto-segmentation/04.zip": "79298dc9474b79caf47425ccbad3ab103d7a219f", "auto-segmentation/05.zip": "b1e3cead20a2c5ee33b0ab17d3a1e59ca04c4148", "auto-segmentation/06.zip": "c5e911acdc71531adecabe3cd4cb434d16903b19", "auto-segmentation/07.zip": "7f9f23fe015744fc88d986830fcfd2ee05556392", "auto-segmentation/08.zip": "68eac4dba91f85aa91ce2411ee26f88f9aba668e", "auto-segmentation/09.zip": "bd666d76ad1c395876c5427af13b4226d6d62e27", "auto-segmentation/10.zip": "8fb313f7e06b0ad9e2a77d209c0ac62224ec5213", "auto-segmentation/11.zip": "b66267764aeba2478e09842d0b1e731b984f9109", "auto-segmentation/12.zip": "e89a078371de02c62f3f18320c9f56c2f9517881", "auto-segmentation/13.zip": "c523fcaf2d90c8e3c10f42a93bf2b05a53673326", "auto-segmentation/14.zip": "f315e962e3517e3a14ea857de1d5f4dd933f3ae9", "auto-segmentation/15.zip": "5b3b2668440bbf267046810037f0b680b40fb608", "auto-segmentation/16.zip": "c0907db20c595e38408af083ca9bc37bcf9e31da", "auto-segmentation/17.zip": "72749260ac7fa140ecdcd7bbba7bfca4c85f9861", "auto-segmentation/18.zip": "f310b53a1cd47552db1193d206d28f311fcd92b4", "auto-segmentation/19.zip": "1f9a232e610d74ce612729e3542c07639101fa10", "auto-segmentation/20.zip": "44766700bd341c7377fdee1f47f018c6b94c1d23", "auto-segmentation/21.zip": "00a7437bd357a05749cc485fc03719e18b4f4ac7", "auto-segmentation/22.zip": "fdc88d56d69979a08aa932f5401beb2e6be754f9", "auto-segmentation/23.zip": "a35f8e06d0f99f273c606c600afd6d1dd05be32f", "auto-segmentation/24.zip": "b8a4b4d2bc83f08dd0ca1bd3487c1aaf9b491e90", "auto-segmentation/25.zip": "b737310c515df30978b1c0dd1ac207f9562a1fba", "auto-segmentation/26.zip": "9de47f26e117ce7cc45a34dda4cf98a520a0efb1", "auto-segmentation/27.zip": "9b9398b314558f24d8b95280025a2fa8cb2e6307", "auto-segmentation/28.zip": "bb1f307032d9a35b1b23ccba7317ff4da1c7629f", "auto-segmentation/29.zip": "70cbc5cc381f2119bcc165de1e11e5a13d353a90", "auto-segmentation/30.zip": "5d0bf703e0add7e04a149485355341fa0cec58c3", "auto-segmentation/31.zip": "f265b02f9ec6b78043bcd8d478827ef768304c71", "auto-segmentation/32.zip": "1550e0fcf70bdd7be4bd3588989054e4886b18e6", "auto-segmentation/33.zip": "bd876b3128c4545904e083db5f836132bcbb3d7d", "auto-segmentation/34.zip": "02dedc3a467f4ca0ca6a23c5febb1b025fb1d6e8", "auto-segmentation/35.zip": "03fbc0adc7d4f97c2160111c817aa95d7a88b1ab", "auto-segmentation/36.zip": "18b1f64b9ec1793498850da6d91b0bf1219d0cb7", "auto-segmentation/37.zip": "ed5a4409cf81e33c867a20db449101febc7df35c", "auto-segmentation/38.zip": "496551dab7839d9cf5f84f7518ed7e7804c7b037", "auto-segmentation/39.zip": "0a3c2c43cd431aa6f1c9db6e8d91e7fa41d97cec", "auto-segmentation/40.zip": "2fd826b3946f0c67095c3e26e07b6e55a467311d", "auto-segmentation/41.zip": "f69f0b02c9e66cf8f6827eb0a0f72d45df289a50", "auto-segmentation/42.zip": "35fb12e827d47530d798fde708d82b5d17bf77d9", "auto-segmentation/43.zip": "c3ea0aa8a6ef511cd80f24ed8b4265c9db382452", "auto-segmentation/44.zip": "a07f1a2b968c80c2055cb2358e07d575fb87682a", "auto-segmentation/name_mappings.zip": "ed00fb75efe7ce16d26f6e6f3acbdad3ac6a13ab", "baseline_dicom_dictionary.zip": "5e80d030e0185ff31ef0356cbd47884f2a40bf65", "ct_uids_by_training_type.zip": "b7e39f7db52fa4fee740b676b2ed7d5e29a70129", "delivery_test_data.zip": "295ef091be192524e04e1ccc08b1beb7214aad38", "denis_wlutz_images.zip": "bb2e559fc7325e54622748257bd72424401e631b", "dicom-to-delivery-issue-#1047.zip": "7529c6ac1c5ca1b5b42daaaa4bf24fc5814f2196", "dicom-trf-pairs.zip": "b6c681b371ed573cbedf6536cdc95be0311a5ae8", "dicom_dose_test_data.zip": "e09f71ebf4d98a58bc3c00e7dd9f59e91e9f8bb6", "difficult_wlutz_image.ppm": "7ec9d26ec5c02f050c887e8bdadde131e3748501", "dummy-ct-and-struct.zip": "0b2d1fcaa76a97bec82aa838be7ff58c4d41a8fd", "example_structures.dcm": "3600a63d8f29f2b6b42dff37f280f3d45aff2a32", "gamma_test_data.zip": "037bbdb8fcba33a04b4b738b1f1907d312ff77ac", "get-pip.py": "8d1f5dd30fb9e8b418b25e8c53ba79b2bb8886b5", "iview-images-where-wlutz-should-fail-fast.zip": "29e5b40fed5ff57ac3433baa287bcbc941a762be", "iviewDB_examples.zip": "90f5f23f49cdb06cf32225f8ef2e23999faa650d", "jpeg": "1fae083a9f864d325467a34fbbf03d7d35560c8a", "jpeg.exe": "e846e7f5830cd08ce29839299de3e0926177fb78", "lab_build.zip": "239bdb7f642b1b06dd1d9a685f9fb16be145efd8", "logfile_dose_beam_4.dcm": "ad2fc7f24c4e37582f75eaff9b1c0477d5324673", "lossless_jpeg_test.zip": "cbc86c68d1d3c4f8cb1cadce3d90a73b23352b3d", "lossless_jpeg_test/input.jpg": "93f46ee62f52ec4d86bc9dec476ac7d2d47007db", "lossless_jpeg_test/output.png": "cdcc2c422010ec8e685e419ce9c7c5545286d3b4", "lossless_jpeg_test/output.ppm": "ead2221d4796c6133374b7a918eff1e1420f68a6", "metersetmap-gui-e2e-data.zip": "02b87a43a201bce746d8e780352f7c469250aef5", "metersetmap_example_arrays.npz": "2863ed56d5be2d6d7385fe05a1c6aeb89d4c0743", "miniconda.exe": "12eb8a3a82b294e00b8e4d3b95a5cd3b77a9ecaf", "negative-metersetmap.trf": "34b39522022bd68fbdec5896a164391fc7656ee1", "original_dose_beam_4.dcm": "dea5e9206a5423d9388046f902e83f620b59686f", "paulking_test_data.zip": "057f8be65e80073e6e8c16c2b3e827d00aca79af", "pinnacle_test_data.zip": "df70e80834af84a8096d5a278e526b4777bbdc0b", "previously_failing_iview_images.zip": "dc79545030b453c82e733ad54d18b99b1eceebba", "profiler_test_data.zip": "18c682c1f49e2acb30c60d1fc04340ce276f903a", "pylinac_offset.png": "54bd0035bc70703360d146658d66cb1d48c09579", "pylinac_wlutz_regression_image.jpg": "8f46084c588d5474fe11c483025bca66078e88a5", "python-windows-64-embedded.zip": "e837b2de3a03112e6283e32ca1ab72d0c3bfc04f", "rtdose_non_square_pixels.dcm": "9d59aafe2f2f2d3b706c092e62ff90bc33430e14", "rtplan-anonymisation.zip": "0e2659596f7ddeee5d1d8d24b54b6dfc5981d9d2", "saturated-images.zip": "3807803907d39256cf5d65fe21b672919d49245b", "structure-deduplication.zip": "b4509f520c38f864969dac98000443e2218ff5ab", "tel-dicom-pairs.zip": "caeb055669ad913c9d737fc9625261fe4b558da2", "tomo_mapcheck_test.txt": "93375b1b1a3eeee5f4810762545f42055f6b3b37", "tps_compare_dicom_files.zip": "d0d0a9676ae8ff8fca080611944fdafab5b6e83d", "treatment-record-anonymisation.zip": "274f3e84af33887645a12f4256b96d35f1380909", "trf-references-and-baselines.zip": "3b674c937c2bd0fbbd673063676a3a4f85cb814f", "washed_out_bb.png": "06c7c32a1a4be6d2ebade915f28a998e3039c3a4", "water_phantom.zip": "b93ea8d89cf4030524ace0e67fa08082efd88776", "wlutz-demo-files.zip": "7d0fe2fb18924e566d0b70692e32ce186dc902fa", "wlutz_arc_session.zip": "5db4f8becdaed134010b83a03fbd3a638745521c", "wlutz_image.png": "f8cf1ab7f2b6925feac909ced0518a987fedf925", "wlutz_images.zip": "2c94fbde3e5600056eaf4ce238dd70d518a09398", "wlutz_tensorflow_training_data/0.zip": "a86a92a53a65458b8269819708dd18bc77f0e14f", "wlutz_tensorflow_training_data/1.zip": "29b6385591e7e75e36fe6a0cb7ad76881beec6ef", "wlutz_tensorflow_training_data/2.zip": "8f29dc4261f2705cad7f5353ff6c1d77c0cff895", "wlutz_tensorflow_training_data/3.zip": "bea29b6b00e36be0dd8c5015a733e39594ced796", "wlutz_tensorflow_training_data/4.zip": "736f056748c68a5f0e4f860a70de2e1950a89513", "wlutz_tensorflow_training_data/5.zip": "c0fb4485839c9d120db416e7265eadd60d80bc1b", "wlutz_tensorflow_training_data/6.zip": "6f9f0e7c590b92ae685bd6986604c2dd41c5ac10", "wlutz_tensorflow_training_data/7.zip": "acdc423a5128b8803f03eba1de6c724f94499447", "wlutz_tensorflow_training_data/8.zip": "2d6607e0ce38d59eabfaf7565e0cff5c4aeb74fe", "wlutz_tensorflow_training_data/9.zip": "51a6c2a348de418196ab8987aec322ed8bc84cfe", "wlutz_tensorflow_training_data/a.zip": "7267241dec6c0d8dcb2201e2bb1a582275fa12dd", "wlutz_tensorflow_training_data/b.zip": "6f90fe2b0a2a70a42b29f1f9f95e9174a475dbd3", "wlutz_tensorflow_training_data/c.zip": "62a830304f212f86daaa7cd80c4f52141b019b11", "wlutz_tensorflow_training_data/d.zip": "26cce651d013d60c1a961d0ab3d6b5ed5c782311", "wlutz_tensorflow_training_data/e.zip": "d3e62c27c18ea04fec45702821630692446974dd", "wlutz_tensorflow_training_data/f.zip": "c86457b6c0faa38b4203d099f46d7d80c55b06dc", "wlutz_tensorflow_training_data/labels.zip": "bb000511106f7232f35259a82a04aaf35807d2d7"}