# Copyright (C) 2021 <PERSON>

# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at

#     http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.


from pymedphys._imports import pymssql, pytest

from pymedphys._mosaiq.mock import utilities


@pytest.mark.mosaiqdb
def test_can_we_db():
    conn = pymssql.connect(
        utilities.MSQ_SERVER,
        port=utilities.MSQ_PORT,
        user=utilities.SA_USER,
        password=utilities.SA_PASSWORD,
    )
    cursor = conn.cursor()
    cursor.execute("select * from sys.databases")

    # should print the four system databases
    databases = list(cursor)
    print(databases)

    # and check that the correct number are present
    assert len(databases) >= 4
