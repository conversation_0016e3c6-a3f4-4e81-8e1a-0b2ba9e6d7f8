["AccessionNumber", "AcquisitionComments", "AcquisitionContextSequence", "AcquisitionDate", "AcquisitionDateTime", "AcquisitionDeviceProcessingDescription", "AcquisitionTime", "ActualHumanPerformersSequence", "AdditionalPatientHistory", "AdmissionID", "AdmittingDate", "AdmittingDiagnosesCodeSequence", "AdmittingDiagnosesDescription", "AdmittingTime", "Allergies", "Arbitrary", "AuthorObserverSequence", "BranchOfService", "CassetteID", "CommentsOnThePerformedProcedureStep", "ConfidentialityConstraintOnPatientDataDescription", "ContentCreatorName", "ContentDate", "ContentSequence", "ContentTime", "ContrastBolusAgent", "ContributionDescription", "CountryOfResidence", "CurrentPatientLocation", "CurveDate", "CurveTime", "CustodialOrganizationSequence", "DataSetTrailingPadding", "Date", "DateTime", "DerivationDescription", "DetectorID", "DeviceSerialNumber", "DigitalSignaturesSequence", "DischargeDiagnosisDescription", "DistributionAddress", "DistributionName", "EthnicGroup", "FillerOrderNumberImagingServiceRequest", "FrameComments", "GantryID", "GeneratorID", "GraphicAnnotationSequence", "HumanPerformerName", "HumanPerformerOrganization", "IconImageSequence", "IdentifyingComments", "ImageComments", "ImagePresentationComments", "ImagingServiceRequestComments", "Impressions", "InstanceCreationDate", "InstanceCreationTime", "InstitutionAddress", "InstitutionCodeSequence", "InstitutionalDepartmentName", "InstitutionName", "InsurancePlanIdentification", "IntendedRecipientsOfResultsIdentificationSequence", "InterpretationApproverSequence", "InterpretationAuthor", "InterpretationDiagnosisDescription", "InterpretationIDIssuer", "InterpretationRecorder", "InterpretationText", "InterpretationTranscriber", "IssuerOfAdmissionID", "IssuerOfPatientID", "IssuerOfServiceEpisodeID", "LastMenstrualDate", "MAC", "MedicalAlerts", "MedicalRecordLocator", "MilitaryRank", "ModifiedAttributesSequence", "ModifiedImageDescription", "ModifyingDeviceID", "ModifyingDeviceManufacturer", "NameOfPhysiciansReadingStudy", "NamesOfIntendedRecipientsOfResults", "Occupation", "OperatorIdentificationSequence", "OperatorsName", "OriginalAttributesSequence", "OrderCallbackPhoneNumber", "OrderEnteredBy", "OrderEntererLocation", "OtherPatientIDs", "OtherPatientIDsSequence", "OtherPatientNames", "OverlayDate", "OverlayTime", "ParticipantSequence", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PatientAge", "PatientBirthDate", "PatientBirthName", "PatientBirthTime", "PatientComments", "PatientID", "PatientInstitutionResidence", "PatientInsurancePlanCodeSequence", "PatientMotherBirthName", "PatientName", "PatientPrimaryLanguageCodeSequence", "PatientPrimaryLanguageModifierCodeSequence", "PatientReligiousPreference", "PatientSex", "PatientSexNeutered", "PatientSize", "PatientState", "PatientTelephoneNumbers", "PatientTransportArrangements", "PatientWeight", "PerformedLocation", "PerformedProcedureStepDescription", "PerformedProcedureStepID", "PerformedProcedureStepStartDate", "PerformedProcedureStepStartTime", "PerformedStationAETitle", "PerformedStationGeographicLocationCodeSequence", "PerformedStationName", "PerformedStationNameCodeSequence", "PerformingPhysicianIdentificationSequence", "PerformingPhysicianName", "<PERSON><PERSON><PERSON><PERSON>", "PersonIdentificationCodeSequence", "PersonName", "PersonTelephoneNumbers", "PhysicianApprovingInterpretation", "PhysiciansReadingStudyIdentificationSequence", "PhysiciansOfRecord", "PhysiciansOfRecordIdentificationSequence", "PhysiciansReadingStudyIdentificationSequence", "PlacerOrderNumberImagingServiceRequest", "PlateID", "PreMedication", "PregnancyStatus", "ProtocolName", "ReasonForTheImagingServiceRequest", "ReasonForStudy", "ReferencedDigitalSignatureSequence", "ReferencedImageSequence", "ReferencedPatientAliasSequence", "ReferencedPatientSequence", "ReferencedPerformedProcedureStepSequence", "ReferencedSOPInstanceMACSequence", "ReferencedStudySequence", "ReferringPhysicianAddress", "ReferringPhysicianIdentificationSequence", "ReferringPhysicianName", "ReferringPhysicianTelephoneNumbers", "RegionOfResidence", "RequestAttributesSequence", "RequestedContrastAgent", "RequestedProcedureComments", "RequestedProcedureDescription", "RequestedProcedureID", "RequestedProcedureLocation", "RequestingPhysician", "RequestingService", "ResponsibleOrganization", "Re<PERSON><PERSON><PERSON>", "ResultsComments", "ResultsDistributionListSequence", "ResultsIDIssuer", "ReviewerName", "ScheduledHumanPerformersSequence", "ScheduledPatientInstitutionResidence", "ScheduledPerformingPhysicianIdentificationSequence", "ScheduledPerformingPhysicianName", "ScheduledProcedureStepDescription", "ScheduledProcedureStepEndDate", "ScheduledProcedureStepEndTime", "ScheduledProcedureStepLocation", "ScheduledProcedureStepStartDate", "ScheduledProcedureStepStartTime", "ScheduledStationAETitle", "ScheduledStationGeographicLocationCodeSequence", "ScheduledStationName", "ScheduledStationNameCodeSequence", "ScheduledStudyLocation", "ScheduledStudyLocationAETitle", "SecondaryReviewerName", "SeriesDate", "SeriesDescription", "SeriesTime", "ServiceEpisodeDescription", "ServiceEpisodeID", "SmokingStatus", "SourceImageSequence", "SpecialNeeds", "StationName", "StudyComments", "StudyDate", "StudyDescription", "StudyID", "StudyID<PERSON>ssuer", "StudyTime", "TextComments", "TextString", "Time", "TimezoneOffsetFromUTC", "TopicAuthor", "TopicKeywords", "TopicSubject", "TopicTitle", "VerifyingObserverIdentificationCodeSequence", "VerifyingObserverName", "VerifyingObserverSequence", "VerifyingOrganization", "VisitComments"]