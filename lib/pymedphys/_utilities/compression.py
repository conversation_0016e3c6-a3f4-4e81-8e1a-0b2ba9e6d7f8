# Copyright (C) 2018 <PERSON>

# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at

#     http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.


import lzma
from glob import glob


def compress_test_file(filepath):
    with open(filepath, "rb") as load_file:
        with lzma.open("{}.xz".format(filepath), "w") as save_file:
            save_file.write(load_file.read())


def compress_test_files(glob_string, exclude_xz_files=True):
    files_to_compress = glob(glob_string, recursive=True)

    for filepath in files_to_compress:
        if not filepath.endswith(".xz") or not exclude_xz_files:
            compress_test_file(filepath)


def decompress_test_files():
    pass
