"""
Tests for the ImageInfoReader class.
"""

import pytest
from pathlib import Path
from pinnacle_io.models import ImageInfo
from pinnacle_io.readers.image_info_reader import ImageInfoReader
from pinnacle_io.services.file_reader import FileReader


def test_read_image_info_from_file():
    """Tests reading ImageInfo from a .ImageInfo file using direct path (backward compatibility)."""
    image_info_list = ImageInfoReader.read_from_path(image_info_path=str(Path(__file__).parent.parent / "test_data/01/Institution_1/Mount_0/Patient_1/ImageSet_0.ImageInfo"))
    assert len(image_info_list) > 0
    image_info = image_info_list[0]
    assert isinstance(image_info, ImageInfo)
    # Check known values from the test data
    assert image_info.table_position == -8.75
    assert image_info.couch_pos == 8.75
    assert image_info.slice_number == 1
    assert image_info.series_uid == "1.2.840.113619.2.55.3.**********.111.**********.1"
    assert image_info.study_instance_uid == "1.2.840.113619.2.55.3.**********.111.**********.1"
    assert image_info.frame_uid == "1.2.840.113619.2.55.3.**********.111.**********.2"
    assert image_info.class_uid == "1.2.840.10008.5.1.4.1.1.2"
    assert image_info.instance_uid == "1.2.840.113619.2.55.3.**********.111.**********.3.1"
    assert image_info.dicom_file_name == "CT_1.2.840.113619.2.55.3.**********.111.**********.3.1.dcm"
    assert image_info.acquisition_time == "120000"
    assert image_info.image_time == "120000"


def test_read_image_info_from_file_with_service():
    """Tests reading ImageInfo from a .ImageInfo file using file service."""
    test_data_root = Path(__file__).parent.parent / "test_data/01"
    file_service = FileReader(str(test_data_root))
    image_info_path = "Institution_1/Mount_0/Patient_1"
    image_info_list = ImageInfoReader.read_from_path(image_info_path, image_index=0, file_service=file_service)

    assert len(image_info_list) > 0
    image_info = image_info_list[0]
    assert isinstance(image_info, ImageInfo)
    # Check known values from the test data
    assert image_info.table_position == -8.75


# New tests for ID-based loading methods

def test_read_from_ids():
    """Tests reading ImageInfo using ID-based method."""
    test_data_root = Path(__file__).parent.parent / "test_data/01"
    file_service = FileReader(str(test_data_root))
    image_info_list = ImageInfoReader.read_from_ids(institution_id=1, patient_id=1, image_set_id=0, mount_id=0, file_service=file_service)
    
    assert len(image_info_list) > 0
    image_info = image_info_list[0]
    assert isinstance(image_info, ImageInfo)
    # Check known values from the test data
    assert image_info.table_position == -8.75
    assert image_info.couch_pos == 8.75
    assert image_info.slice_number == 1
    assert image_info.series_uid == "1.2.840.113619.2.55.3.**********.111.**********.1"
    assert image_info.dicom_file_name == "CT_1.2.840.113619.2.55.3.**********.111.**********.3.1.dcm"


def test_read_from_ids_with_file_service():
    """Tests reading ImageInfo using ID-based method with file service."""
    test_data_root = Path(__file__).parent.parent / "test_data/01"
    file_service = FileReader(str(test_data_root))
    
    image_info_list = ImageInfoReader.read_from_ids(
        institution_id=1, patient_id=1, image_set_id=0, mount_id=0, file_service=file_service
    )
    
    assert len(image_info_list) > 0
    image_info = image_info_list[0]
    assert isinstance(image_info, ImageInfo)
    assert image_info.table_position == -8.75


def test_read_from_ids_missing_file():
    """Tests that read_from_ids raises FileNotFoundError for missing files."""
    test_data_root = Path(__file__).parent.parent / "test_data/01"
    file_service = FileReader(str(test_data_root))
    with pytest.raises(FileNotFoundError):
        ImageInfoReader.read_from_ids(institution_id=99, patient_id=99, image_set_id=0, file_service=file_service)


def test_read_from_ids_defaults():
    """Tests that read_from_ids uses default mount_id=0."""
    test_data_root = Path(__file__).parent.parent / "test_data/01"
    file_service = FileReader(str(test_data_root))
    # Should work the same with explicit mount_id=0 vs default
    image_info_list1 = ImageInfoReader.read_from_ids(institution_id=1, patient_id=1, image_set_id=0, mount_id=0, file_service=file_service)
    image_info_list2 = ImageInfoReader.read_from_ids(institution_id=1, patient_id=1, image_set_id=0, file_service=file_service)
    
    assert len(image_info_list1) == len(image_info_list2)
    assert image_info_list1[0].table_position == image_info_list2[0].table_position