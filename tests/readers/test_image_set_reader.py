"""
Tests for the ImageSetReader class (extracted from test_image_set.py and test_image_info.py).
"""

import io
import os
import numpy as np
import pytest
from pathlib import Path
from unittest import mock

from pinnacle_io.models import ImageSet, ImageInfo
from pinnacle_io.readers.image_set_reader import ImageSetReader
from pinnacle_io.services.file_reader import FileReader

TEST_DATA_DIR = Path(__file__).parent.parent / "test_data/01/Institution_1/Mount_0/Patient_1"


def test_read_image_set_file():
    """Tests reading a valid ImageSet file using direct path (backward compatibility)."""
    image_set = ImageSetReader.read_header(
        str(Path(__file__).parent.parent / "test_data/01/Institution_1/Mount_0/Patient_1/ImageSet_0.header")
    )

    assert isinstance(image_set, ImageSet)
    assert image_set.series_uid == "1.2.840.113619.2.55.3.**********.000.**********.000"
    assert image_set.study_uid is None
    assert image_set.series_description == "HEAD"
    assert image_set.modality == "CT"
    assert image_set.x_dim == 512
    assert image_set.y_dim == 512
    assert image_set.z_dim == 101
    assert image_set.x_pixdim == 0.097656
    assert image_set.y_pixdim == 0.097656
    assert image_set.z_pixdim == 0.25

    assert len(image_set.image_info_list) > 2
    assert image_set.image_info_list[0].table_position == -8.75
    assert image_set.image_info_list[0].slice_number == 1
    assert image_set.image_info_list[0].dicom_file_name == "CT_1.2.840.113619.2.55.3.**********.111.**********.3.1.dcm"
    assert image_set.image_info_list[1].table_position == -8.5
    assert image_set.image_info_list[1].slice_number == 2
    assert image_set.image_info_list[1].dicom_file_name == "CT_1.2.840.113619.2.55.3.**********.111.**********.3.2.dcm"


def test_read_image_set_img_file():
    """Tests reading a valid ImageSet .img file (pixel data)."""
    img_path = TEST_DATA_DIR / "ImageSet_0.img"
    image_set = ImageSetReader.read_from_path(str(img_path))
    assert isinstance(image_set, ImageSet)
    assert hasattr(image_set, "pixel_data")
    # Pixel data should be numpy array with correct shape
    z, y, x = image_set.z_dim, image_set.y_dim, image_set.x_dim
    assert image_set.pixel_data is not None
    assert isinstance(image_set.pixel_data, np.ndarray)
    assert image_set.pixel_data.shape == (z, y, x)
    assert image_set.pixel_data.dtype == np.uint16


def test_read_image_set_header_file():
    """Tests reading a valid ImageSet .header file via read_from_path()."""
    header_path = TEST_DATA_DIR / "ImageSet_0.header"
    image_set = ImageSetReader.read_from_path(str(header_path))
    assert isinstance(image_set, ImageSet)
    assert image_set.series_description == "HEAD"


def test_read_image_set_invalid_extension():
    """Tests read_from_path() with an invalid file extension raises ValueError."""
    # ImageSetReader.read_from_path doesn't validate extensions - it delegates to read_header for non-.img files
    # This test should actually pass since it will try to read as header
    image_set = ImageSetReader.read_from_path(str(TEST_DATA_DIR / "ImageSet_0.header"))
    assert isinstance(image_set, ImageSet)


def test_read_header_missing_file():
    """Tests read_header() with a missing file raises FileNotFoundError."""
    with pytest.raises(FileNotFoundError):
        ImageSetReader.read_header(str(TEST_DATA_DIR / "does_not_exist.header"))


def test_read_image_set_missing_img():
    """Tests read_image_set() with a missing .img file raises FileNotFoundError."""
    with pytest.raises(FileNotFoundError):
        ImageSetReader.read_image_set(str(TEST_DATA_DIR / "does_not_exist.img"))


def test_read_image_set_with_provided_image_set():
    """Tests read_image_set() with a provided image_header argument."""
    header_path = TEST_DATA_DIR / "ImageSet_0.header"
    image_set = ImageSetReader.read_header(str(header_path))
    img_path = TEST_DATA_DIR / "ImageSet_0.img"
    result = ImageSetReader.read_image_set(str(img_path), image_header=image_set)
    assert result is image_set
    assert hasattr(result, "pixel_data")


def test_parse_valid_content():
    """Tests parse() with valid header content."""
    header_path = TEST_DATA_DIR / "ImageSet_0.header"
    with open(header_path, "r", encoding="latin1", errors="ignore") as f:
        lines = f.readlines()
    image_set = ImageSetReader.parse(lines)
    assert isinstance(image_set, ImageSet)
    assert image_set.series_description == "HEAD"


def test_read_image_set_defaults_for_missing_dims():
    """Tests read_image_set() defaults to 1 for missing x/y/z dims."""
    # Mock ImageSet with None for dims
    dummy = ImageSet(x_dim=None, y_dim=None, z_dim=None)
    img_path = TEST_DATA_DIR / "ImageSet_0.img"
    # Patch open to return enough bytes for 1*1*1 uint16
    with mock.patch("builtins.open", mock.mock_open(read_data=(1).to_bytes(2, "little"))):
        result = ImageSetReader.read_image_set(str(img_path), image_header=dummy)
        assert isinstance(result.pixel_data, np.ndarray)
        assert result.pixel_data.shape == (1, 1, 1)
        assert result.pixel_data.dtype == np.uint16


def test_read_image_set_file_with_service():
    """Tests reading a valid ImageSet file using file service."""
    test_data_dir = Path(__file__).parent.parent / "test_data/01/Institution_1/Mount_0/Patient_1"
    file_service = FileReader(str(test_data_dir))
    image_set = ImageSetReader.read_from_path(image_path="", image_index=0, file_service=file_service)

    assert isinstance(image_set, ImageSet)
    assert image_set.series_uid == "1.2.840.113619.2.55.3.**********.000.**********.000"
    assert image_set.modality == "CT"
    assert image_set.x_dim == 512


def test_read_image_set_pixel_data_as_numpy_array():
    """Tests that ImageSetReader sets pixel_data as numpy array, not bytes."""
    # Test reading .img file
    img_path = TEST_DATA_DIR / "ImageSet_0.img"
    image_set = ImageSetReader.read_image_set(str(img_path))

    # Verify pixel_data is a numpy array
    assert image_set.pixel_data is not None
    assert isinstance(image_set.pixel_data, np.ndarray)
    assert image_set.pixel_data.dtype == np.uint16

    # Verify dimensions match header
    expected_shape = (image_set.z_dim, image_set.y_dim, image_set.x_dim)
    assert image_set.pixel_data.shape == expected_shape

    # Test slice access methods work correctly
    slice_data = image_set.get_slice_data(50)  # Middle slice
    assert slice_data is not None
    assert slice_data.shape == (image_set.y_dim, image_set.x_dim)
    assert isinstance(slice_data, np.ndarray)


def test_read_image_set_with_file_service_pixel_data():
    """Tests that pixel_data is numpy array when using file service."""
    test_data_dir = Path(__file__).parent.parent / "test_data/01/Institution_1/Mount_0/Patient_1"
    file_service = FileReader(str(test_data_dir))
    image_set = ImageSetReader.read_image_set("", image_index=0, file_service=file_service)

    # Verify pixel_data is a numpy array
    assert image_set.pixel_data is not None
    assert isinstance(image_set.pixel_data, np.ndarray)
    assert image_set.pixel_data.dtype == np.uint16

    # Verify dimensions
    expected_shape = (image_set.z_dim, image_set.y_dim, image_set.x_dim)
    assert image_set.pixel_data.shape == expected_shape


def test_read_image_slice_valid_slice_extraction():
    """Tests read_image_slice() extracts correct slice data from different positions."""
    # Read header to get image metadata
    header_path = TEST_DATA_DIR / "ImageSet_0.header"
    image_header = ImageSetReader.read_header(str(header_path))
    img_path = TEST_DATA_DIR / "ImageSet_0.img"

    # Test first slice (index 0)
    first_slice = ImageSetReader.read_image_slice(str(img_path), 0, image_header)
    assert isinstance(first_slice, np.ndarray)
    assert first_slice.shape == (image_header.y_dim, image_header.x_dim)
    assert first_slice.dtype == np.uint16

    # Test middle slice
    middle_index = image_header.z_dim // 2
    middle_slice = ImageSetReader.read_image_slice(str(img_path), middle_index, image_header)
    assert isinstance(middle_slice, np.ndarray)
    assert middle_slice.shape == (image_header.y_dim, image_header.x_dim)
    assert middle_slice.dtype == np.uint16

    # Test last slice
    last_index = image_header.z_dim - 1
    last_slice = ImageSetReader.read_image_slice(str(img_path), last_index, image_header)
    assert isinstance(last_slice, np.ndarray)
    assert last_slice.shape == (image_header.y_dim, image_header.x_dim)
    assert last_slice.dtype == np.uint16

    # Verify slices are different (they should contain different data)
    assert not np.array_equal(first_slice, middle_slice)
    assert not np.array_equal(middle_slice, last_slice)


def test_read_image_slice_consistency_with_full_read():
    """Tests that read_image_slice() returns same data as extracting from full image."""
    # Read header and full image
    header_path = TEST_DATA_DIR / "ImageSet_0.header"
    image_header = ImageSetReader.read_header(str(header_path))
    img_path = TEST_DATA_DIR / "ImageSet_0.img"
    full_image_set = ImageSetReader.read_image_set(str(img_path), image_header=image_header)

    # Test several slice indices
    test_indices = [0, 25, 50, image_header.z_dim - 1]

    for slice_index in test_indices:
        if slice_index < image_header.z_dim:
            # Get slice using new method
            slice_from_method = ImageSetReader.read_image_slice(str(img_path), slice_index, image_header)

            # Get slice from full image
            slice_from_full = full_image_set.get_slice_data(slice_index)

            # They should be identical
            assert slice_from_full is not None
            assert np.array_equal(slice_from_method, slice_from_full)


def test_read_image_slice_error_handling_invalid_slice_index():
    """Tests read_image_slice() error handling for out-of-bounds slice indices."""
    header_path = TEST_DATA_DIR / "ImageSet_0.header"
    image_header = ImageSetReader.read_header(str(header_path))
    img_path = TEST_DATA_DIR / "ImageSet_0.img"

    # Test negative index
    with pytest.raises(ValueError, match="Slice index -1 is out of bounds"):
        ImageSetReader.read_image_slice(str(img_path), -1, image_header)

    # Test index equal to z_dim (should be out of bounds)
    with pytest.raises(ValueError, match=f"Slice index {image_header.z_dim} is out of bounds"):
        ImageSetReader.read_image_slice(str(img_path), image_header.z_dim, image_header)

    # Test index greater than z_dim
    with pytest.raises(ValueError, match=f"Slice index {image_header.z_dim + 10} is out of bounds"):
        ImageSetReader.read_image_slice(str(img_path), image_header.z_dim + 10, image_header)


def test_read_image_slice_error_handling_missing_file():
    """Tests read_image_slice() error handling for missing image file."""
    header_path = TEST_DATA_DIR / "ImageSet_0.header"
    image_header = ImageSetReader.read_header(str(header_path))
    missing_img_path = TEST_DATA_DIR / "does_not_exist.img"

    with pytest.raises(FileNotFoundError):
        ImageSetReader.read_image_slice(str(missing_img_path), 0, image_header)


def test_read_image_slice_with_none_dimensions():
    """Tests read_image_slice() handles None dimensions by defaulting to 1."""
    # Create a mock ImageSet with None dimensions
    mock_header = ImageSet(x_dim=None, y_dim=None, z_dim=None)
    img_path = TEST_DATA_DIR / "ImageSet_0.img"

    # Create a minimal test file with 2 bytes (1 pixel of uint16)
    import tempfile

    with tempfile.NamedTemporaryFile(suffix=".img", delete=False) as temp_file:
        temp_file.write((42).to_bytes(2, "little"))  # Single uint16 pixel
        temp_path = temp_file.name

    try:
        # Should work with slice_index 0 (the only valid slice for 1x1x1 image)
        slice_data = ImageSetReader.read_image_slice(temp_path, 0, mock_header)
        assert isinstance(slice_data, np.ndarray)
        assert slice_data.shape == (1, 1)  # y_dim=1, x_dim=1
        assert slice_data.dtype == np.uint16
        assert slice_data[0, 0] == 42

        # Should fail with slice_index 1 (out of bounds for z_dim=1)
        with pytest.raises(ValueError, match="Slice index 1 is out of bounds"):
            ImageSetReader.read_image_slice(temp_path, 1, mock_header)
    finally:
        os.unlink(temp_path)


def test_read_image_slice_performance_validation():
    """Tests that read_image_slice() only reads partial file data."""
    header_path = TEST_DATA_DIR / "ImageSet_0.header"
    image_header = ImageSetReader.read_header(str(header_path))
    img_path = TEST_DATA_DIR / "ImageSet_0.img"

    # Get file size
    file_size = os.path.getsize(img_path)

    # Calculate expected slice size
    bytes_per_pixel = 2  # np.uint16
    slice_size_bytes = image_header.y_dim * image_header.x_dim * bytes_per_pixel

    # Verify slice size is much smaller than total file
    assert slice_size_bytes < file_size, "Slice should be smaller than full file"

    # Mock the built-in open function to track file operations
    original_open = open
    file_operations = []

    def mock_open(*args, **kwargs):
        file_obj = original_open(*args, **kwargs)

        # Wrap the file object to track read operations
        class FileWrapper:
            def __init__(self, file_obj):
                self._file = file_obj

            def __enter__(self):
                return self

            def __exit__(self, *args):
                return self._file.__exit__(*args)

            def seek(self, pos):
                file_operations.append(("seek", pos))
                return self._file.seek(pos)

            def read(self, size=-1):
                result = self._file.read(size)
                file_operations.append(("read", size, len(result)))
                return result

            def __getattr__(self, name):
                return getattr(self._file, name)

        return FileWrapper(file_obj)

    with mock.patch("builtins.open", mock_open):
        slice_data = ImageSetReader.read_image_slice(str(img_path), 0, image_header)

        # Verify we performed the expected file operations
        assert len(file_operations) >= 2  # At least seek and read

        # Find the read operation
        read_ops = [op for op in file_operations if op[0] == "read"]
        assert len(read_ops) == 1
        assert read_ops[0][1] == slice_size_bytes  # Read size should match slice size
        assert read_ops[0][2] == slice_size_bytes  # Actual bytes read should match

        # Verify the slice data is correct
        assert isinstance(slice_data, np.ndarray)
        assert slice_data.shape == (image_header.y_dim, image_header.x_dim)


def test_read_image_slice_data_integrity():
    """Tests that read_image_slice() maintains data integrity and correct data types."""
    header_path = TEST_DATA_DIR / "ImageSet_0.header"
    image_header = ImageSetReader.read_header(str(header_path))
    img_path = TEST_DATA_DIR / "ImageSet_0.img"

    # Read a slice
    slice_data = ImageSetReader.read_image_slice(str(img_path), 10, image_header)

    # Verify data type and properties
    assert slice_data.dtype == np.uint16
    assert slice_data.shape == (image_header.y_dim, image_header.x_dim)
    assert slice_data.size == image_header.y_dim * image_header.x_dim

    # Verify data is reasonable (medical image pixel values)
    assert slice_data.min() >= 0  # uint16 should be non-negative
    assert slice_data.max() <= 65535  # uint16 max value

    # Verify slice contains actual data (not all zeros)
    assert not np.all(slice_data == 0), "Slice should contain non-zero pixel data"


def test_read_image_slice_byte_order_consistency():
    """Tests that read_image_slice() handles byte order correctly."""
    header_path = TEST_DATA_DIR / "ImageSet_0.header"
    image_header = ImageSetReader.read_header(str(header_path))
    img_path = TEST_DATA_DIR / "ImageSet_0.img"

    # Read the same slice multiple times to ensure consistency
    slice1 = ImageSetReader.read_image_slice(str(img_path), 5, image_header)
    slice2 = ImageSetReader.read_image_slice(str(img_path), 5, image_header)

    # Should be identical
    assert np.array_equal(slice1, slice2)

    # Compare with different slice to ensure they're actually different
    slice_different = ImageSetReader.read_image_slice(str(img_path), 6, image_header)
    assert not np.array_equal(slice1, slice_different)


def test_read_image_slice_flexible_path_handling():
    """Tests that read_image_slice() handles different path formats like read_image_set()."""
    header_path = TEST_DATA_DIR / "ImageSet_0.header"
    image_header = ImageSetReader.read_header(str(header_path))

    # Test with full .img path
    img_path = TEST_DATA_DIR / "ImageSet_0.img"
    slice1 = ImageSetReader.read_image_slice(str(img_path), 10, image_header)
    assert isinstance(slice1, np.ndarray)
    assert slice1.shape == (image_header.y_dim, image_header.x_dim)

    # Test with path without .img extension
    base_path = TEST_DATA_DIR / "ImageSet_0"
    slice2 = ImageSetReader.read_image_slice(str(base_path), 10, image_header)
    assert isinstance(slice2, np.ndarray)
    assert slice2.shape == (image_header.y_dim, image_header.x_dim)

    # Both should return identical data
    assert np.array_equal(slice1, slice2)

    # Test with directory path and image_index
    slice3 = ImageSetReader.read_image_slice(str(TEST_DATA_DIR), 10, image_header, image_index=0)
    assert isinstance(slice3, np.ndarray)
    assert slice3.shape == (image_header.y_dim, image_header.x_dim)

    # All three should return identical data
    assert np.array_equal(slice1, slice3)


def test_read_image_slice_path_validation_errors():
    """Tests read_image_slice() path validation and error handling."""
    header_path = TEST_DATA_DIR / "ImageSet_0.header"
    image_header = ImageSetReader.read_header(str(header_path))

    # Test missing image_index when using directory path
    with pytest.raises(ValueError, match="The image index must either be specified"):
        ImageSetReader.read_image_slice(str(TEST_DATA_DIR), 0, image_header)

    # Test with non-existent directory
    with pytest.raises(FileNotFoundError):
        ImageSetReader.read_image_slice("/non/existent/path", 0, image_header, image_index=0)

    # Test with non-existent file using .img extension
    missing_img = TEST_DATA_DIR / "NonExistent_0.img"
    with pytest.raises(FileNotFoundError):
        ImageSetReader.read_image_slice(str(missing_img), 0, image_header)


def test_read_image_slice_automatic_header_loading():
    """Tests that read_image_slice() automatically loads header when image_header is None."""
    img_path = TEST_DATA_DIR / "ImageSet_0.img"

    # Test with automatic header loading (image_header=None)
    slice_data_auto = ImageSetReader.read_image_slice(str(img_path), 10)
    assert isinstance(slice_data_auto, np.ndarray)
    assert slice_data_auto.dtype == np.uint16

    # Compare with explicit header loading to ensure same result
    header_path = TEST_DATA_DIR / "ImageSet_0.header"
    image_header = ImageSetReader.read_header(str(header_path))
    slice_data_explicit = ImageSetReader.read_image_slice(str(img_path), 10, image_header)

    # Should be identical
    assert np.array_equal(slice_data_auto, slice_data_explicit)
    assert slice_data_auto.shape == slice_data_explicit.shape
    assert slice_data_auto.shape == (image_header.y_dim, image_header.x_dim)


def test_read_image_slice_automatic_header_loading_different_paths():
    """Tests automatic header loading with different path formats."""
    # Test with .img extension
    img_path = TEST_DATA_DIR / "ImageSet_0.img"
    slice1 = ImageSetReader.read_image_slice(str(img_path), 5)
    assert isinstance(slice1, np.ndarray)

    # Test without .img extension
    base_path = TEST_DATA_DIR / "ImageSet_0"
    slice2 = ImageSetReader.read_image_slice(str(base_path), 5)
    assert isinstance(slice2, np.ndarray)

    # Should be identical
    assert np.array_equal(slice1, slice2)

    # Test with directory path and image_index
    slice3 = ImageSetReader.read_image_slice(str(TEST_DATA_DIR), 5, image_index=0)
    assert isinstance(slice3, np.ndarray)

    # Should be identical to the others
    assert np.array_equal(slice1, slice3)


def test_read_image_slice_automatic_header_loading_missing_header():
    """Tests that automatic header loading fails gracefully when header file is missing."""
    import tempfile
    import os

    # Create a temporary .img file without corresponding .header file
    with tempfile.NamedTemporaryFile(suffix=".img", delete=False) as temp_file:
        temp_file.write(b"dummy data")
        temp_path = temp_file.name

    try:
        # Should raise FileNotFoundError when trying to load non-existent header
        with pytest.raises(FileNotFoundError, match="ImageSet header file not found"):
            ImageSetReader.read_image_slice(temp_path, 0)
    finally:
        os.unlink(temp_path)


def test_read_image_slice_backward_compatibility():
    """Tests that the new signature maintains backward compatibility."""
    header_path = TEST_DATA_DIR / "ImageSet_0.header"
    image_header = ImageSetReader.read_header(str(header_path))
    img_path = TEST_DATA_DIR / "ImageSet_0.img"

    # Test old-style call with explicit header (should still work)
    slice_explicit = ImageSetReader.read_image_slice(str(img_path), 10, image_header)

    # Test new-style call with automatic header loading
    slice_auto = ImageSetReader.read_image_slice(str(img_path), 10)

    # Test new-style call with explicit None header (should auto-load)
    slice_none = ImageSetReader.read_image_slice(str(img_path), 10, None)

    # All should produce identical results
    assert np.array_equal(slice_explicit, slice_auto)
    assert np.array_equal(slice_explicit, slice_none)
    assert slice_explicit.shape == slice_auto.shape == slice_none.shape


# New tests for ID-based loading methods

def test_read_from_ids_header():
    """Tests reading a valid ImageSet header using ID-based method."""
    test_data_root = Path(__file__).parent.parent / "test_data/01"
    file_service = FileReader(str(test_data_root))
    image_set = ImageSetReader.read_from_ids(institution_id=1, patient_id=1, image_set_id=0, mount_id=0, file_service=file_service)
    
    assert isinstance(image_set, ImageSet)
    assert image_set.series_uid == "1.2.840.113619.2.55.3.**********.000.**********.000"
    assert image_set.study_uid is None
    assert image_set.series_description == "HEAD"
    assert image_set.modality == "CT"
    assert image_set.x_dim == 512
    assert image_set.y_dim == 512
    assert image_set.z_dim == 101
    assert image_set.x_pixdim == 0.097656
    assert image_set.y_pixdim == 0.097656
    assert image_set.z_pixdim == 0.25

    # Verify ImageInfo list is loaded
    assert len(image_set.image_info_list) > 2
    assert image_set.image_info_list[0].table_position == -8.75
    assert image_set.image_info_list[0].slice_number == 1


def test_read_from_ids_with_file_service():
    """Tests reading ImageSet using ID-based method with file service."""
    test_data_root = Path(__file__).parent.parent / "test_data/01"
    file_service = FileReader(str(test_data_root))
    
    image_set = ImageSetReader.read_from_ids(
        institution_id=1, patient_id=1, image_set_id=0, mount_id=0, file_service=file_service
    )
    
    assert isinstance(image_set, ImageSet)
    assert image_set.series_uid == "1.2.840.113619.2.55.3.**********.000.**********.000"
    assert image_set.modality == "CT"
    assert image_set.x_dim == 512


def test_read_from_ids_missing_file():
    """Tests that read_from_ids raises FileNotFoundError for missing files."""
    test_data_root = Path(__file__).parent.parent / "test_data/01"
    file_service = FileReader(str(test_data_root))
    with pytest.raises(FileNotFoundError):
        ImageSetReader.read_from_ids(institution_id=99, patient_id=99, image_set_id=0, file_service=file_service)


def test_read_from_ids_defaults():
    """Tests that read_from_ids uses default mount_id=0."""
    test_data_root = Path(__file__).parent.parent / "test_data/01"
    file_service = FileReader(str(test_data_root))
    # Should work the same with explicit mount_id=0 vs default
    image_set1 = ImageSetReader.read_from_ids(institution_id=1, patient_id=1, image_set_id=0, mount_id=0, file_service=file_service)
    image_set2 = ImageSetReader.read_from_ids(institution_id=1, patient_id=1, image_set_id=0, file_service=file_service)
    
    assert image_set1.series_uid == image_set2.series_uid
    assert image_set1.modality == image_set2.modality
