"""
Tests for the PlanReader (extracted from test_plan.py).
"""

from pathlib import Path
import pytest

from pinnacle_io.readers.plan_reader import PlanReader
from pinnacle_io.utils.patient_enum import PatientSetupEnum
from pinnacle_io.models import Plan
from pinnacle_io.services.file_reader import <PERSON><PERSON><PERSON><PERSON>


def test_read_plan_file():
    """Tests reading a valid Plan file using direct path (backward compatibility)."""
    # The PlanReader reads from a Patient file, not directly from a Plan file
    test_data_dir = Path(__file__).parent.parent / "test_data/01/Institution_1/Mount_0/Patient_1"
    plans = PlanReader.read(patient_path=str(test_data_dir))

    assert isinstance(plans, list)
    assert len(plans) > 0

    plan = plans[0]
    assert isinstance(plan, Plan)

    # Check basic plan properties
    assert plan.plan_id == 0
    assert plan.name == "BRAIN"
    assert plan.tool_type == "Pinnacle^3"
    assert plan.comment == ""
    assert plan.physicist == "MAX WELLDOSE PHD"
    assert plan.dosimetrist == "DOSEY CALC CMD"
    assert plan.primary_ct_image_set_id == 0
    assert plan.primary_image_type == "Images"
    assert plan.pinnacle_version_description == "Pinnacle 16.0"
    assert plan.is_new_plan_prefix == 1
    assert plan.plan_is_locked == 1
    assert plan.ok_for_syntegra_in_launchpad == 0
    # assert plan.fusion_id_array == [] # TODO: Not yet implemented

    # Check version information
    assert plan.write_version == "Launch Pad: 16.2"
    assert plan.create_version == "Launch Pad: 16.0"
    assert plan.version_login_name == "candor01"
    assert plan.create_time_stamp.strftime("%Y-%m-%d %H:%M:%S") == "2020-01-01 10:00:00"
    assert plan.write_time_stamp.strftime("%Y-%m-%d %H:%M:%S") == "2020-01-01 10:00:00"
    assert plan.last_modified_time_stamp.strftime("%Y-%m-%d %H:%M:%S") == "2020-01-01 10:00:00"

    # Check relationships
    assert hasattr(plan, "trial_list")
    assert isinstance(plan.trial_list, list)
    assert len(plan.trial_list) == 0

    # Check patient position
    assert hasattr(plan, "patient_position")
    assert plan.patient_position == PatientSetupEnum.HFS


def test_read_plan_file_with_service():
    """Tests reading a valid Plan file using file service."""
    test_data_dir = Path(__file__).parent.parent / "test_data/01/Institution_1/Mount_0/Patient_1"
    file_service = FileReader(str(test_data_dir))
    plans = PlanReader.read(patient_path="", file_service=file_service)

    assert isinstance(plans, list)
    assert len(plans) > 0

    plan = plans[0]
    assert isinstance(plan, Plan)
    assert plan.plan_id == 0
    assert plan.name == "BRAIN"


def test_read_from_path():
    """Tests reading plans using path-based method."""
    test_data_dir = Path(__file__).parent.parent / "test_data/01/Institution_1/Mount_0/Patient_1"
    plans = PlanReader.read_from_path(patient_path=str(test_data_dir))

    assert isinstance(plans, list)
    assert len(plans) > 0

    plan = plans[0]
    assert isinstance(plan, Plan)
    assert plan.plan_id == 0
    assert plan.name == "BRAIN"


def test_read_from_ids():
    """Tests reading plans using ID-based method."""
    test_data_root = Path(__file__).parent.parent / "test_data/01"
    file_service = FileReader(str(test_data_root))
    plans = PlanReader.read_from_ids(institution_id=1, patient_id=1, mount_id=0, file_service=file_service)

    assert isinstance(plans, list)
    assert len(plans) > 0

    plan = plans[0]
    assert isinstance(plan, Plan)
    assert plan.plan_id == 0
    assert plan.name == "BRAIN"
