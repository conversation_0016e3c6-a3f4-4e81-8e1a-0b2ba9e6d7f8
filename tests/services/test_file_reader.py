import pytest
import os
import tempfile
from pathlib import Path
from pinnacle_io.services.file_reader import FileReader


class TestFileReader:
    """Test cases for FileReader service."""

    @pytest.fixture
    def test_data_path(self):
        """Get path to test data folder."""
        test_dir = Path(__file__).parent.parent
        return test_dir / "test_data" / "01"

    @pytest.fixture
    def file_reader(self, test_data_path):
        """Create FileReader instance with test data."""
        return FileReader(str(test_data_path))

    @pytest.fixture
    def temp_test_files(self):
        """Create temporary test files for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create test files with different content
            files = {
                "Institution": 'Name = "Test Institution";\nAddress = "123 Test St";\n',
                "Patient": 'Name = "Test Patient";\nMedicalRecordNumber = "12345";\n',
                "plan.Trial": 'Trial = {\n  Name = "Test Trial";\n};\n',
                "subdir/nested_file.txt": "Nested file content\n",
            }

            # Create subdirectory
            os.makedirs(os.path.join(temp_dir, "subdir"), exist_ok=True)

            # Write test files
            for filename, content in files.items():
                file_path = os.path.join(temp_dir, filename)
                with open(file_path, "w") as f:
                    f.write(content)

            yield temp_dir, files

    def test_init(self, test_data_path):
        """Test FileReader initialization."""
        reader = FileReader(str(test_data_path))
        assert reader.root_path == str(test_data_path)

    def test_open_file_existing(self, temp_test_files):
        """Test opening existing files."""
        temp_dir, files = temp_test_files
        reader = FileReader(temp_dir)

        with reader.open_file("", "Institution", "r") as f:
            content = f.read()
            assert content == files["Institution"]

    def test_open_file_binary_mode(self, temp_test_files):
        """Test opening files in binary mode."""
        temp_dir, files = temp_test_files
        reader = FileReader(temp_dir)

        with reader.open_file("", "Institution", "rb") as f:
            content = f.read().replace(b"\r\n", b"\n")
            assert content == files["Institution"].encode()

    def test_open_file_not_found(self, temp_test_files):
        """Test FileNotFoundError when file doesn't exist."""
        temp_dir, _ = temp_test_files
        reader = FileReader(temp_dir)

        with pytest.raises(FileNotFoundError) as exc_info:
            reader.open_file("", "nonexistent_file.txt", "r")

        assert "nonexistent_file.txt not found in directory" in str(exc_info.value)

    def test_open_file_nested_path(self, temp_test_files):
        """Test opening files in nested directories."""
        temp_dir, files = temp_test_files
        reader = FileReader(temp_dir)

        with reader.open_file("subdir", "nested_file.txt", "r") as f:
            content = f.read()
            assert content == files["subdir/nested_file.txt"]

    def test_exists_file_found(self, temp_test_files):
        """Test exists method returns True for existing files."""
        temp_dir, _ = temp_test_files
        reader = FileReader(temp_dir)

        assert reader.exists("", "Institution") is True
        assert reader.exists("", "Patient") is True
        assert reader.exists("", "plan.Trial") is True
        assert reader.exists("subdir", "nested_file.txt") is True

    def test_exists_file_not_found(self, temp_test_files):
        """Test exists method returns False for non-existing files."""
        temp_dir, _ = temp_test_files
        reader = FileReader(temp_dir)

        assert reader.exists("", "nonexistent_file.txt") is False
        assert reader.exists("subdir", "nonexistent.txt") is False

    def test_windows_path_format(self, temp_test_files):
        """Test with Windows-style path separators."""
        temp_dir, _ = temp_test_files
        reader = FileReader(temp_dir)

        # Test with Windows-style path separator
        # With the new API, we can test Windows-style paths by using filepath parameter
        if os.name == "nt":
            assert reader.exists("subdir\\nested_file.txt") is True
            with reader.open_file("subdir\\nested_file.txt", mode="r") as f:
                content = f.read()
                assert "Nested file content" in content

    def test_linux_path_format(self, temp_test_files):
        """Test with Linux-style path separators."""
        temp_dir, _ = temp_test_files
        reader = FileReader(temp_dir)

        # Test with Linux-style path separator (should work on all platforms)
        assert reader.exists("subdir/nested_file.txt") is True
        with reader.open_file("subdir/nested_file.txt", mode="r") as f:
            content = f.read()
            assert "Nested file content" in content

    def test_path_normalization(self, temp_test_files):
        """Test that different path formats point to the same file."""
        temp_dir, _ = temp_test_files
        reader = FileReader(temp_dir)

        # These should all point to the same file
        paths = [
            "subdir/nested_file.txt",
            "./subdir/nested_file.txt",
        ]

        if os.name == "nt":
            paths.append("subdir\\nested_file.txt")

        for path in paths:
            if reader.exists(path):
                with reader.open_file(path, mode="r") as f:
                    content = f.read()
                    assert "Nested file content" in content

    def test_integration_with_real_test_data(self, file_reader):
        """Test with actual test data files."""
        # Test that we can check for existence of real test files
        test_files = [
            "Institution",
            "Institution_1/Mount_0/Patient_1/Patient",
            "Institution_1/Mount_0/Patient_1/ImageSet_0.header",
            "Institution_1/Mount_0/Patient_1/Plan_0/plan.Trial",
        ]

        for filename in test_files:
            if file_reader.exists(filename):
                # If file exists, we should be able to open it
                with file_reader.open_file(filename, mode="r") as f:
                    content = f.read()
                    assert isinstance(content, str)
                    assert len(content) > 0

    def test_error_message_includes_path(self, temp_test_files):
        """Test that error messages include helpful path information."""
        temp_dir, _ = temp_test_files
        reader = FileReader(temp_dir)

        with pytest.raises(FileNotFoundError) as exc_info:
            reader.open_file("missing_file.txt", mode="r")

        error_msg = str(exc_info.value)
        assert "missing_file.txt" in error_msg
        assert temp_dir in error_msg

    def test_file_modes(self, temp_test_files):
        """Test different file opening modes."""
        temp_dir, files = temp_test_files
        reader = FileReader(temp_dir)

        # Test text mode
        with reader.open_file("", "Institution", "r") as f:
            content = f.read()
            assert isinstance(content, str)

        # Test binary mode
        with reader.open_file("", "Institution", "rb") as f:
            content = f.read()
            assert isinstance(content, bytes)

        # Test write mode (if supported by open_file)
        try:
            with reader.open_file("", "test_write.txt", "w") as f:
                f.write("test content")

            # Verify we can read it back
            with reader.open_file("", "test_write.txt", "r") as f:
                content = f.read()
                assert content == "test content"
        except (OSError, IOError):
            # Write mode might not be supported, that's okay
            pass

    def test_empty_root_path(self):
        """Test FileReader with empty root path."""
        reader = FileReader("")

        # Should work with current directory
        assert reader.root_path == ""

        # Test that exists works (though files may not exist)
        exists_result = reader.exists("", "nonexistent.txt")
        assert isinstance(exists_result, bool)

    def test_new_api_filepath_filename(self, temp_test_files):
        """Test the new API with separate filepath and filename parameters."""
        temp_dir, files = temp_test_files
        reader = FileReader(temp_dir)

        # Test with separate filepath and filename
        assert reader.exists("", "Institution") is True
        assert reader.exists("subdir", "nested_file.txt") is True

        with reader.open_file("", "Institution", "r") as f:
            content = f.read()
            assert content == files["Institution"]

        with reader.open_file("subdir", "nested_file.txt", "r") as f:
            content = f.read()
            assert content == files["subdir/nested_file.txt"]

    def test_new_api_full_path(self, temp_test_files):
        """Test the new API with full path as filepath parameter."""
        temp_dir, files = temp_test_files
        reader = FileReader(temp_dir)

        # Test with full path as filepath (filename=None)
        assert reader.exists("Institution") is True
        assert reader.exists("subdir/nested_file.txt") is True

        with reader.open_file("Institution", mode="r") as f:
            content = f.read()
            assert content == files["Institution"]

        with reader.open_file("subdir/nested_file.txt", mode="r") as f:
            content = f.read()
            assert content == files["subdir/nested_file.txt"]


class TestFileReaderAdvancedErrorHandling:
    """Advanced error handling tests for FileReader service."""

    @pytest.fixture
    def temp_test_files(self):
        """Create temporary test files with various edge cases."""
        with tempfile.TemporaryDirectory() as temp_dir:
            files = {
                "empty_file.txt": "",
                "binary_file.bin": b"\x00\x01\x02\x03\xff\xfe",
                "unicode_file.txt": "Ñiño café résumé 中文 🎉",
                "large_file.txt": "A" * 10000 + "\n" * 1000,
                "special_chars.txt": "!@#$%^&*()[]{}|\\:\";'<>?,./",
            }

            for filename, content in files.items():
                file_path = os.path.join(temp_dir, filename)
                if isinstance(content, bytes):
                    with open(file_path, "wb") as f:
                        f.write(content)
                else:
                    with open(file_path, "w", encoding="utf-8") as f:
                        f.write(content)

            # Create a directory with the same name as a file (name conflict)
            conflict_dir = os.path.join(temp_dir, "conflict")
            os.makedirs(conflict_dir, exist_ok=True)
            with open(os.path.join(temp_dir, "conflict.txt"), "w") as f:
                f.write("conflict content")

            yield temp_dir, files

    def test_empty_file_handling(self, temp_test_files):
        """Test handling of empty files."""
        temp_dir, _ = temp_test_files
        reader = FileReader(temp_dir)

        assert reader.exists("", "empty_file.txt") is True

        with reader.open_file("", "empty_file.txt", "r") as f:
            content = f.read()
            assert content == ""

    def test_binary_file_handling(self, temp_test_files):
        """Test handling of binary files."""
        temp_dir, files = temp_test_files
        reader = FileReader(temp_dir)

        with reader.open_file("", "binary_file.bin", "rb") as f:
            content = f.read()
            assert content == files["binary_file.bin"]

    def test_unicode_file_handling(self, temp_test_files):
        """Test handling of Unicode files."""
        temp_dir, files = temp_test_files
        reader = FileReader(temp_dir)

        with reader.open_file("", "unicode_file.txt", "r", encoding="utf-8") as f:
            content = f.read()
            assert content == files["unicode_file.txt"]

    def test_large_file_handling(self, temp_test_files):
        """Test handling of large files."""
        temp_dir, files = temp_test_files
        reader = FileReader(temp_dir)

        with reader.open_file("", "large_file.txt", "r") as f:
            content = f.read()
            assert content == files["large_file.txt"]
            assert len(content) > 10000

    def test_special_characters_in_content(self, temp_test_files):
        """Test handling of special characters in file content."""
        temp_dir, files = temp_test_files
        reader = FileReader(temp_dir)

        with reader.open_file("", "special_chars.txt", "r") as f:
            content = f.read()
            assert content == files["special_chars.txt"]

    def test_invalid_file_mode(self, temp_test_files):
        """Test error handling for invalid file modes."""
        temp_dir, _ = temp_test_files
        reader = FileReader(temp_dir)

        with pytest.raises(ValueError):
            reader.open_file("", "empty_file.txt", "invalid_mode")

    def test_permission_denied_simulation(self, temp_test_files):
        """Test handling of permission errors (simulated)."""
        temp_dir, _ = temp_test_files
        reader = FileReader(temp_dir)

        # Try to open a directory as a file (should cause an error)
        with pytest.raises((IsADirectoryError, PermissionError, OSError)):
            reader.open_file("", ".", "r")

    def test_invalid_root_path(self):
        """Test FileReader with invalid root path."""
        reader = FileReader("/nonexistent/path/that/should/not/exist")
        assert not reader.exists("", "any_file.txt")

    def test_path_traversal_protection(self, temp_test_files):
        """Test protection against path traversal attacks."""
        temp_dir, _ = temp_test_files
        reader = FileReader(temp_dir)

        # These should not allow access outside the root directory
        dangerous_paths = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            "/etc/passwd",
            "C:\\windows\\system32\\config\\sam",
        ]

        for dangerous_path in dangerous_paths:
            # The file reader should either reject these paths or safely contain them
            try:
                result = reader.exists("", dangerous_path)
                # If it doesn't throw an error, it should return False for security
                if result is not False:
                    # Only allow if the resolved path is within the temp directory
                    pass  # This test is about ensuring no access outside temp_dir
            except (ValueError, FileNotFoundError, OSError):
                # These exceptions are acceptable for security
                pass

    def test_case_sensitivity_handling(self, temp_test_files):
        """Test case sensitivity behavior."""
        temp_dir, _ = temp_test_files
        reader = FileReader(temp_dir)

        # Create a file with known case
        test_file = "CaseSensitive.txt"
        with open(os.path.join(temp_dir, test_file), "w") as f:
            f.write("case test")

        # Test exact case
        assert reader.exists("", test_file) is True

        # Test different cases (behavior may vary by OS)
        case_variants = ["casesensitive.txt", "CASESENSITIVE.TXT", "CaseSensitive.TXT"]
        for variant in case_variants:
            exists = reader.exists("", variant)
            # On case-insensitive filesystems (Windows, macOS), this might be True
            # On case-sensitive filesystems (Linux), this should be False
            assert isinstance(exists, bool)

    def test_concurrent_file_access(self, temp_test_files):
        """Test concurrent access to the same file."""
        temp_dir, _ = temp_test_files
        reader = FileReader(temp_dir)

        # Open the same file multiple times
        files = []
        try:
            for _ in range(3):
                f = reader.open_file("", "empty_file.txt", "r")
                files.append(f)

            # All should be able to read
            for f in files:
                content = f.read()
                assert content == ""
        finally:
            # Clean up
            for f in files:
                try:
                    f.close()
                except:
                    pass

    def test_filesystem_errors(self, temp_test_files):
        """Test various filesystem error conditions."""
        temp_dir, _ = temp_test_files
        reader = FileReader(temp_dir)

        # Test with extremely long filename (may cause OSError on some systems)
        long_filename = "a" * 300 + ".txt"
        exists = reader.exists("", long_filename)
        assert isinstance(exists, bool)

        # Test with null bytes in filename (should be rejected)
        with pytest.raises((ValueError, OSError)):
            reader.exists("", "file\x00name.txt")

        # Test with null bytes in filepath (should be rejected)
        with pytest.raises((ValueError, OSError)):
            reader.exists("path\x00with\x00nulls", "file.txt")

        # Test with null bytes in both filepath and filename
        with pytest.raises((ValueError, OSError)):
            reader.exists("bad\x00path", "bad\x00file.txt")

        # Test open_file with null bytes (should also be rejected)
        with pytest.raises((ValueError, OSError)):
            reader.open_file("", "file\x00name.txt")

        with pytest.raises((ValueError, OSError)):
            reader.open_file("path\x00with\x00nulls", "file.txt")

        # Test with single null byte characters
        with pytest.raises((ValueError, OSError)):
            reader.exists("", "\x00")

        with pytest.raises((ValueError, OSError)):
            reader.exists("\x00", "file.txt")

        # Test with multiple null bytes
        with pytest.raises((ValueError, OSError)):
            reader.exists("", "file\x00\x00\x00name.txt")

        # Test with null bytes at start and end
        with pytest.raises((ValueError, OSError)):
            reader.exists("", "\x00filename\x00")

        with pytest.raises((ValueError, OSError)):
            reader.exists("\x00path\x00", "filename")

    def test_encoding_errors(self, temp_test_files):
        """Test handling of encoding errors."""
        temp_dir, _ = temp_test_files
        reader = FileReader(temp_dir)

        # Create a file with non-UTF8 bytes
        bad_encoding_file = os.path.join(temp_dir, "bad_encoding.txt")
        with open(bad_encoding_file, "wb") as f:
            f.write(b"\xff\xfe\x00\x80invalid utf8")

        # Reading with UTF-8 should handle encoding errors gracefully
        try:
            with reader.open_file(
                "", "bad_encoding.txt", "r", encoding="utf-8", errors="replace"
            ) as f:
                content = f.read()
                # Should not crash, may contain replacement characters
                assert isinstance(content, str)
        except UnicodeDecodeError:
            # This is also acceptable behavior
            pass

    def test_symlink_handling(self, temp_test_files):
        """Test handling of symbolic links (if supported by OS)."""
        temp_dir, _ = temp_test_files
        reader = FileReader(temp_dir)

        try:
            # Create a symlink to an existing file
            target_file = os.path.join(temp_dir, "empty_file.txt")
            symlink_file = os.path.join(temp_dir, "symlink.txt")
            os.symlink(target_file, symlink_file)

            # Should be able to access through symlink
            assert reader.exists("", "symlink.txt") is True

            with reader.open_file("", "symlink.txt", "r") as f:
                content = f.read()
                assert content == ""

        except (OSError, NotImplementedError):
            # Symlinks might not be supported on this platform
            pytest.skip("Symbolic links not supported on this platform")

    def test_broken_symlink_handling(self, temp_test_files):
        """Test handling of broken symbolic links."""
        temp_dir, _ = temp_test_files
        reader = FileReader(temp_dir)

        try:
            # Create a broken symlink
            nonexistent_target = os.path.join(temp_dir, "nonexistent.txt")
            broken_symlink = os.path.join(temp_dir, "broken_link.txt")
            os.symlink(nonexistent_target, broken_symlink)

            # Should handle broken symlinks gracefully
            exists = reader.exists("", "broken_link.txt")
            # Behavior may vary - either False or exception is acceptable
            assert isinstance(exists, bool) or exists is None

        except (OSError, NotImplementedError):
            # Symlinks might not be supported on this platform
            pytest.skip("Symbolic links not supported on this platform")
