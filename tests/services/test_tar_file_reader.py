import pytest
import tarfile
import tempfile
import os
import io
from pathlib import Path
from pinnacle_io.services.tar_file_reader import TarFileReader


class TestTarFileReader:
    """Test cases for TarFileReader service."""

    @pytest.fixture
    def test_tar_path(self):
        """Get path to test tar file."""
        test_dir = Path(__file__).parent.parent
        return test_dir / "test_data" / "01.tar.gz"

    @pytest.fixture
    def tar_reader(self, test_tar_path):
        """Create TarFileReader instance with test data."""
        if test_tar_path.exists():
            return TarFileReader(str(test_tar_path))
        else:
            pytest.skip(f"Test tar file not found: {test_tar_path}")

    @pytest.fixture
    def temp_tar_file(self):
        """Create a temporary tar file for testing."""
        with tempfile.NamedTemporaryFile(suffix='.tar.gz', delete=False) as temp_tar:
            temp_tar_path = temp_tar.name
        
        try:
            # Create tar file with test content
            with tarfile.open(temp_tar_path, 'w:gz') as tar:
                # Add test files with different path formats
                test_files = {
                    'Institution': 'Name = "Test Institution";\nAddress = "123 Test St";\n',
                    'Patient': 'Name = "Test Patient";\nMedicalRecordNumber = "12345";\n',
                    'plan.Trial': 'Trial = {\n  Name = "Test Trial";\n};\n',
                    'Institution_1/Mount_0/Patient_1/Patient': 'Name = "Nested Patient";\n',
                    'Institution_1/Mount_0/Patient_1/Plan_0/plan.roi': 'ROI = {\n  Name = "Test ROI";\n};\n',
                    # Test with Linux-style paths (common in tar files)
                    'linux/style/path.txt': 'Linux style path content\n',
                    # Test with special characters
                    'special-file_name.txt': 'Special filename content\n'
                }
                
                for filename, content in test_files.items():
                    # Create tarinfo
                    tarinfo = tarfile.TarInfo(name=filename)
                    tarinfo.size = len(content.encode())
                    
                    # Add to tar
                    import io
                    tar.addfile(tarinfo, io.BytesIO(content.encode()))
            
            yield temp_tar_path, test_files
        finally:
            # Clean up with retry logic for Windows
            self._cleanup_temp_file(temp_tar_path)
    
    def _cleanup_temp_file(self, temp_path):
        """Helper method to clean up temporary files with retry logic for Windows."""
        import time
        max_retries = 3
        for attempt in range(max_retries):
            try:
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                break
            except PermissionError:
                if attempt < max_retries - 1:
                    time.sleep(0.1)  # Brief wait before retry
                else:
                    # If we can't delete, just log it - don't fail the test
                    pass

    def test_init(self, temp_tar_file):
        """Test TarFileReader initialization."""
        temp_tar_path, _ = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        assert reader.tar_path == temp_tar_path
        assert reader._tarfile is not None

    def test_open_file_existing(self, temp_tar_file):
        """Test opening existing files from tar."""
        temp_tar_path, test_files = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        with reader.open_file('', 'Institution', 'r') as f:
            content = f.read()
            assert content == test_files['Institution']

    def test_open_file_binary_mode(self, temp_tar_file):
        """Test opening files in binary mode."""
        temp_tar_path, test_files = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        with reader.open_file('', 'Institution', 'rb') as f:
            content = f.read().replace(b'\r\n', b'\n')
            assert content == test_files['Institution'].encode()

    def test_open_file_not_found(self, temp_tar_file):
        """Test FileNotFoundError when file doesn't exist in tar."""
        temp_tar_path, _ = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        with pytest.raises(FileNotFoundError) as exc_info:
            reader.open_file('', 'nonexistent_file.txt', 'r')
        
        assert "nonexistent_file.txt not found in tar archive" in str(exc_info.value)

    def test_open_file_nested_path(self, temp_tar_file):
        """Test opening files in nested directories within tar."""
        temp_tar_path, test_files = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        nested_file = 'Institution_1/Mount_0/Patient_1/Patient'
        with reader.open_file('Institution_1/Mount_0/Patient_1', 'Patient', 'r') as f:
            content = f.read()
            assert content == test_files[nested_file]

    def test_exists_file_found(self, temp_tar_file):
        """Test exists method returns True for existing files."""
        temp_tar_path, _ = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        assert reader.exists('', 'Institution') is True
        assert reader.exists('', 'Patient') is True
        assert reader.exists('', 'plan.Trial') is True
        assert reader.exists('Institution_1/Mount_0/Patient_1', 'Patient') is True

    def test_exists_file_not_found(self, temp_tar_file):
        """Test exists method returns False for non-existing files."""
        temp_tar_path, _ = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        assert reader.exists('', 'nonexistent_file.txt') is False
        assert reader.exists('Institution_1/Mount_0', 'nonexistent.txt') is False

    def test_linux_path_format_in_tar(self, temp_tar_file):
        """Test with Linux-style paths (common in tar files)."""
        temp_tar_path, test_files = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        # Tar files typically use Linux-style paths regardless of host OS
        linux_path = 'linux/style/path.txt'
        assert reader.exists(linux_path) is True
        
        with reader.open_file('linux/style', 'path.txt', 'r') as f:
            content = f.read()
            assert content == test_files[linux_path]

    def test_windows_to_linux_path_conversion(self, temp_tar_file):
        """Test that Windows-style paths are handled appropriately."""
        temp_tar_path, _ = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        # Note: In tar files, paths are always stored with forward slashes
        # Windows-style backslashes would be part of the filename itself
        # So this test confirms that Windows-style paths don't match Linux-style ones
        nested_path_linux = 'Institution_1/Mount_0/Patient_1/Patient'
        nested_path_windows = 'Institution_1\\Mount_0\\Patient_1\\Patient'
        
        assert reader.exists(nested_path_linux) is True
        # This should be False because tar stores paths with forward slashes
        assert reader.exists(nested_path_windows) is False

    def test_special_filenames(self, temp_tar_file):
        """Test files with special characters in names."""
        temp_tar_path, test_files = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        special_file = 'special-file_name.txt'
        assert reader.exists('', special_file) is True
        
        with reader.open_file('', special_file, 'r') as f:
            content = f.read()
            assert content == test_files[special_file]

    def test_integration_with_real_test_data(self, tar_reader):
        """Test with actual test tar file if available."""
        if tar_reader is None:
            pytest.skip("Real test tar file not available")
        
        # Test that we can check for existence of files that might be in the real tar
        potential_files = [
            '01/Institution',
            '01/Institution_1/Mount_0/Patient_1/Patient',
            '01/Institution_1/Mount_0/Patient_1/ImageSet_0.header',
            '01/Institution_1/Mount_0/Patient_1/Plan_0/plan.Trial'
        ]
        
        for filename in potential_files:
            if tar_reader.exists(filename):
                # If file exists, we should be able to open it
                with tar_reader.open_file(filename, mode='r') as f:
                    content = f.read()
                    assert isinstance(content, str)
                    assert len(content) > 0

    def test_error_message_includes_tar_path(self, temp_tar_file):
        """Test that error messages include helpful tar path information."""
        temp_tar_path, _ = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        with pytest.raises(FileNotFoundError) as exc_info:
            reader.open_file('', 'missing_file.txt', 'r')
        
        error_msg = str(exc_info.value)
        assert 'missing_file.txt' in error_msg
        assert 'tar archive' in error_msg

    def test_file_modes(self, temp_tar_file):
        """Test different file opening modes."""
        temp_tar_path, test_files = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        # Test text mode
        with reader.open_file('', 'Institution', 'r') as f:
            content = f.read()
            assert isinstance(content, str)
        
        # Test binary mode
        with reader.open_file('', 'Institution', 'rb') as f:
            content = f.read()
            assert isinstance(content, bytes)

    def test_text_wrapper_encoding(self, temp_tar_file):
        """Test that text mode uses latin1 encoding as specified."""
        temp_tar_path, _ = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        with reader.open_file('', 'Institution', 'r') as f:
            # Check that we have a TextIOWrapper
            assert hasattr(f, 'encoding')
            # The encoding should be latin1 as specified in the implementation
            assert f.encoding == 'latin1'

    def test_tar_file_cleanup(self, temp_tar_file):
        """Test that tar file resources are properly managed."""
        temp_tar_path, _ = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        # Access the internal tar file
        tar_file = reader._tarfile
        assert tar_file is not None
        
        # Use the reader
        assert reader.exists('', 'Institution') is True
        
        # The tar file should still be accessible
        assert not tar_file.closed

    def test_nonexistent_tar_file(self):
        """Test behavior when tar file doesn't exist."""
        with pytest.raises((FileNotFoundError, tarfile.ReadError)):
            TarFileReader('/nonexistent/path/to/file.tar.gz')

    def test_invalid_tar_file(self):
        """Test behavior with invalid tar file."""
        # Create a non-tar file
        with tempfile.NamedTemporaryFile(suffix='.tar.gz', delete=False) as temp_file:
            temp_file.write(b'This is not a tar file')
            temp_path = temp_file.name
        
        try:
            with pytest.raises(tarfile.ReadError):
                TarFileReader(temp_path)
        finally:
            self._cleanup_temp_file(temp_path)

    def test_new_api_filepath_filename(self, temp_tar_file):
        """Test the new API with separate filepath and filename parameters."""
        temp_tar_path, test_files = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        # Test with separate filepath and filename
        assert reader.exists('', 'Institution') is True
        assert reader.exists('Institution_1/Mount_0/Patient_1', 'Patient') is True
        
        with reader.open_file('', 'Institution', 'r') as f:
            content = f.read()
            assert content == test_files['Institution']
        
        with reader.open_file('Institution_1/Mount_0/Patient_1', 'Patient', 'r') as f:
            content = f.read()
            assert content == test_files['Institution_1/Mount_0/Patient_1/Patient']

    def test_new_api_full_path(self, temp_tar_file):
        """Test the new API with full path as filepath parameter."""
        temp_tar_path, test_files = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        # Test with full path as filepath (filename=None)
        assert reader.exists('Institution') is True
        assert reader.exists('Institution_1/Mount_0/Patient_1/Patient') is True
        
        with reader.open_file('Institution', mode='r') as f:
            content = f.read()
            assert content == test_files['Institution']
        
        with reader.open_file('Institution_1/Mount_0/Patient_1/Patient', mode='r') as f:
            content = f.read()
            assert content == test_files['Institution_1/Mount_0/Patient_1/Patient']

    def test_pattern_matching(self, temp_tar_file):
        """Test that the tar reader can find files using pattern matching."""
        temp_tar_path, test_files = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        # Test pattern matching - should find files that start with filepath and end with filename
        assert reader.exists('Institution_1/Mount_0/Patient_1', 'Patient') is True
        assert reader.exists('Institution_1/Mount_0/Patient_1/Plan_0', 'plan.roi') is True
        
        with reader.open_file('Institution_1/Mount_0/Patient_1', 'Patient', 'r') as f:
            content = f.read()
            assert content == test_files['Institution_1/Mount_0/Patient_1/Patient']
        
        with reader.open_file('Institution_1/Mount_0/Patient_1/Plan_0', 'plan.roi', 'r') as f:
            content = f.read()
            assert content == test_files['Institution_1/Mount_0/Patient_1/Plan_0/plan.roi']


class TestTarFileReaderAdvancedErrorHandling:
    """Advanced error handling tests for TarFileReader service."""

    @pytest.fixture
    def temp_corrupted_tar_files(self):
        """Create various corrupted/edge-case tar files for testing."""
        test_files = {}
        
        # 1. Empty tar file
        with tempfile.NamedTemporaryFile(suffix='.tar.gz', delete=False) as empty_tar:
            empty_tar_path = empty_tar.name
            with tarfile.open(empty_tar_path, 'w:gz') as tar:
                pass  # Create empty tar
            test_files['empty'] = empty_tar_path
        
        # 2. Tar with empty files
        with tempfile.NamedTemporaryFile(suffix='.tar.gz', delete=False) as empty_content_tar:
            empty_content_path = empty_content_tar.name
            with tarfile.open(empty_content_path, 'w:gz') as tar:
                # Add empty file
                tarinfo = tarfile.TarInfo(name='empty_file.txt')
                tarinfo.size = 0
                tar.addfile(tarinfo, io.BytesIO(b''))
            test_files['empty_content'] = empty_content_path
        
        # 3. Tar with binary content
        with tempfile.NamedTemporaryFile(suffix='.tar.gz', delete=False) as binary_tar:
            binary_path = binary_tar.name
            with tarfile.open(binary_path, 'w:gz') as tar:
                # Add binary file
                binary_content = b'\x00\x01\x02\x03\xFF\xFE\x80\x90'
                tarinfo = tarfile.TarInfo(name='binary_file.bin')
                tarinfo.size = len(binary_content)
                tar.addfile(tarinfo, io.BytesIO(binary_content))
            test_files['binary'] = binary_path
        
        # 4. Tar with Unicode filenames and content
        with tempfile.NamedTemporaryFile(suffix='.tar.gz', delete=False) as unicode_tar:
            unicode_path = unicode_tar.name
            with tarfile.open(unicode_path, 'w:gz') as tar:
                # Add file with Unicode content
                unicode_content = 'Ñiño café résumé 中文 🎉'
                tarinfo = tarfile.TarInfo(name='unicode_content.txt')
                tarinfo.size = len(unicode_content.encode('utf-8'))
                tar.addfile(tarinfo, io.BytesIO(unicode_content.encode('utf-8')))
                
                # Add file with Unicode filename (if supported)
                try:
                    unicode_filename = 'fichier_café.txt'
                    tarinfo = tarfile.TarInfo(name=unicode_filename)
                    content = 'Unicode filename test'
                    tarinfo.size = len(content.encode('utf-8'))
                    tar.addfile(tarinfo, io.BytesIO(content.encode('utf-8')))
                except:
                    pass  # Skip if Unicode filenames not supported
            test_files['unicode'] = unicode_path
        
        # 5. Partially corrupted tar (truncated)
        with tempfile.NamedTemporaryFile(suffix='.tar.gz', delete=False) as truncated_tar:
            truncated_path = truncated_tar.name
            # First create a valid tar
            with tarfile.open(truncated_path, 'w:gz') as tar:
                content = 'This file will be truncated'
                tarinfo = tarfile.TarInfo(name='truncated_file.txt')
                tarinfo.size = len(content.encode())
                tar.addfile(tarinfo, io.BytesIO(content.encode()))
            
            # Then truncate it
            with open(truncated_path, 'r+b') as f:
                f.seek(100)  # Seek to middle
                f.truncate()  # Truncate the file
            test_files['truncated'] = truncated_path
        
        try:
            yield test_files
        finally:
            # Clean up all test files
            for file_path in test_files.values():
                self._cleanup_temp_file(file_path)
    
    def _cleanup_temp_file(self, temp_path):
        """Helper method to clean up temporary files with retry logic for Windows."""
        import time
        max_retries = 3
        for attempt in range(max_retries):
            try:
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                break
            except PermissionError:
                if attempt < max_retries - 1:
                    time.sleep(0.1)
                else:
                    pass

    def test_empty_tar_file(self, temp_corrupted_tar_files):
        """Test handling of empty tar files."""
        test_files = temp_corrupted_tar_files
        reader = TarFileReader(test_files['empty'])
        
        # Should be able to create reader with empty tar
        assert reader._tarfile is not None
        
        # No files should exist
        assert reader.exists('', 'any_file.txt') is False

    def test_empty_file_content(self, temp_corrupted_tar_files):
        """Test handling of files with empty content."""
        test_files = temp_corrupted_tar_files
        reader = TarFileReader(test_files['empty_content'])
        
        assert reader.exists('', 'empty_file.txt') is True
        
        with reader.open_file('', 'empty_file.txt', 'r') as f:
            content = f.read()
            assert content == ''

    def test_binary_content_in_text_mode(self, temp_corrupted_tar_files):
        """Test reading binary content in text mode."""
        test_files = temp_corrupted_tar_files
        reader = TarFileReader(test_files['binary'])
        
        assert reader.exists('', 'binary_file.bin') is True
        
        # Reading binary content in text mode might cause issues or work with latin1
        try:
            with reader.open_file('', 'binary_file.bin', 'r') as f:
                content = f.read()
                assert isinstance(content, str)
        except UnicodeDecodeError:
            # This is acceptable behavior for binary content in text mode
            pass

    def test_binary_content_in_binary_mode(self, temp_corrupted_tar_files):
        """Test reading binary content in binary mode."""
        test_files = temp_corrupted_tar_files
        reader = TarFileReader(test_files['binary'])
        
        with reader.open_file('', 'binary_file.bin', 'rb') as f:
            content = f.read()
            assert isinstance(content, bytes)
            assert len(content) == 8
            assert content == b'\x00\x01\x02\x03\xFF\xFE\x80\x90'

    def test_unicode_content_handling(self, temp_corrupted_tar_files):
        """Test handling of Unicode content."""
        test_files = temp_corrupted_tar_files
        reader = TarFileReader(test_files['unicode'])
        
        assert reader.exists('', 'unicode_content.txt') is True
        
        # Reading Unicode content should work with latin1 encoding
        with reader.open_file('', 'unicode_content.txt', 'r') as f:
            content = f.read()
            assert isinstance(content, str)
            # Content might be garbled due to latin1 encoding, but should not crash

    def test_unicode_filename_handling(self, temp_corrupted_tar_files):
        """Test handling of Unicode filenames."""
        test_files = temp_corrupted_tar_files
        reader = TarFileReader(test_files['unicode'])
        
        # Try to access file with Unicode filename
        try:
            exists = reader.exists('', 'fichier_café.txt')
            assert isinstance(exists, bool)
            
            if exists:
                with reader.open_file('', 'fichier_café.txt', 'r') as f:
                    content = f.read()
                    assert isinstance(content, str)
        except (UnicodeError, KeyError):
            # Unicode filename issues are acceptable
            pass

    def test_truncated_tar_file(self, temp_corrupted_tar_files):
        """Test handling of truncated/corrupted tar files."""
        test_files = temp_corrupted_tar_files
        
        # Opening truncated tar might fail or succeed depending on corruption level
        try:
            reader = TarFileReader(test_files['truncated'])
            # If it opens, some operations might still fail
            try:
                exists = reader.exists('', 'truncated_file.txt')
                assert isinstance(exists, bool)
            except (tarfile.ReadError, OSError):
                # These errors are acceptable for corrupted files
                pass
        except (tarfile.ReadError, OSError):
            # Cannot open corrupted tar - this is acceptable
            pass

    def test_very_large_file_simulation(self):
        """Test handling of very large files in tar (simulated)."""
        # Create a tar with a file that claims to be very large
        with tempfile.NamedTemporaryFile(suffix='.tar.gz', delete=False) as large_tar:
            large_path = large_tar.name
            
            try:
                with tarfile.open(large_path, 'w:gz') as tar:
                    # Create a file with normal content but large reported size
                    content = b'Small content'
                    tarinfo = tarfile.TarInfo(name='large_file.txt')
                    tarinfo.size = len(content)  # Keep actual size reasonable for test
                    tar.addfile(tarinfo, io.BytesIO(content))
                
                reader = TarFileReader(large_path)
                assert reader.exists('', 'large_file.txt') is True
                
                with reader.open_file('', 'large_file.txt', 'rb') as f:
                    content = f.read()
                    assert content == b'Small content'
                    
            finally:
                self._cleanup_temp_file(large_path)

    def test_invalid_file_modes(self, temp_corrupted_tar_files):
        """Test invalid file opening modes."""
        test_files = temp_corrupted_tar_files
        reader = TarFileReader(test_files['empty_content'])
        
        # Test invalid modes
        invalid_modes = ['invalid', 'x', 'a', 'w']
        for mode in invalid_modes:
            with pytest.raises((ValueError, OSError)):
                reader.open_file('', 'empty_file.txt', mode)

    def test_file_not_found_error_message(self, temp_corrupted_tar_files):
        """Test detailed error messages for missing files."""
        test_files = temp_corrupted_tar_files
        reader = TarFileReader(test_files['empty'])
        
        with pytest.raises(FileNotFoundError) as exc_info:
            reader.open_file('', 'nonexistent.txt', 'r')
        
        error_msg = str(exc_info.value)
        assert 'nonexistent.txt' in error_msg
        assert 'tar archive' in error_msg

    def test_tar_file_access_after_error(self, temp_corrupted_tar_files):
        """Test that tar file remains accessible after errors."""
        test_files = temp_corrupted_tar_files
        reader = TarFileReader(test_files['empty_content'])
        
        # Cause an error
        try:
            reader.open_file('', 'nonexistent.txt', 'r')
        except FileNotFoundError:
            pass
        
        # Should still be able to access existing files
        assert reader.exists('', 'empty_file.txt') is True

    def test_concurrent_tar_access(self, temp_corrupted_tar_files):
        """Test concurrent access to tar files."""
        test_files = temp_corrupted_tar_files
        reader = TarFileReader(test_files['empty_content'])
        
        # Open multiple file handles
        handles = []
        try:
            for _ in range(3):
                f = reader.open_file('', 'empty_file.txt', 'r')
                handles.append(f)
                content = f.read()
                assert content == ''
        finally:
            # Clean up handles
            for handle in handles:
                try:
                    handle.close()
                except:
                    pass

    def test_path_normalization_in_tar(self, temp_corrupted_tar_files):
        """Test path normalization for tar entries."""
        test_files = temp_corrupted_tar_files
        reader = TarFileReader(test_files['empty_content'])
        
        # Test various path formats for the same file
        path_variants = [
            ('', 'empty_file.txt'),
            ('.', 'empty_file.txt'),
            ('', './empty_file.txt'),
        ]
        
        for filepath, filename in path_variants:
            try:
                if filename:
                    exists = reader.exists(filepath, filename)
                else:
                    exists = reader.exists(filepath)
                assert isinstance(exists, bool)
            except (ValueError, KeyError):
                # Some path formats might not be supported
                pass

    def test_tar_resource_cleanup(self, temp_corrupted_tar_files):
        """Test that tar file resources are properly cleaned up."""
        test_files = temp_corrupted_tar_files
        reader = TarFileReader(test_files['empty_content'])
        
        # Access the tar file
        tar_file = reader._tarfile
        assert tar_file is not None
        assert not tar_file.closed
        
        # Use the reader
        reader.exists('', 'empty_file.txt')
        
        # Tar file should still be open for reuse
        assert not tar_file.closed


class TestTarFileReaderMissingCoverage:
    """Test cases to cover missing lines in TarFileReader."""

    def test_exact_filepath_match(self):
        """Test exact filepath matching in find_member (line 70)."""
        # Test with real test data if available
        test_tar_path = Path(__file__).parent.parent / "test_data" / "01.tar.gz"
        if not test_tar_path.exists():
            pytest.skip("Test data not available")
        
        reader = TarFileReader(str(test_tar_path))
        
        # Test exact filepath matching (no filename parameter)
        # This should trigger line 70: matching_members.append(member)
        exists = reader.exists('Institution')
        assert exists is True
        
        # Test with a file that should match exactly
        with reader.open_file('Institution') as f:
            content = f.read()
            assert content and len(content) > 0

    def test_filepath_with_filename_match(self):
        """Test filepath with filename matching (line 75)."""
        # Create a tar file with nested structure for testing
        with tempfile.NamedTemporaryFile(suffix='.tar.gz', delete=False) as tmp:
            tmp_path = tmp.name
            
        try:
            # Create tar with nested structure
            with tarfile.open(tmp_path, 'w:gz') as tar:
                # Add file in subdirectory
                info = tarfile.TarInfo('subdir/nested_file.txt')
                info.size = 14
                tar.addfile(info, io.BytesIO(b'nested content'))
                
            nested_reader = TarFileReader(tmp_path)
            
            # Test filepath with filename matching (should trigger line 75)
            exists = nested_reader.exists('subdir', 'nested_file.txt')
            assert exists is True
            
            # Test opening the file
            with nested_reader.open_file('subdir', 'nested_file.txt') as f:
                content = f.read()
                assert content == 'nested content'
                
        finally:
            os.unlink(tmp_path)

    def test_empty_filepath_with_filename_match(self):
        """Test empty filepath with filename matching (line 78)."""
        # Test with real test data if available
        test_tar_path = Path(__file__).parent.parent / "test_data" / "01.tar.gz"
        if not test_tar_path.exists():
            pytest.skip("Test data not available")
        
        reader = TarFileReader(str(test_tar_path))
        
        # Test empty filepath with filename (should trigger line 78)
        exists = reader.exists('', 'Institution')
        assert exists is True
        
        with reader.open_file('', 'Institution') as f:
            content = f.read()
            assert content and len(content) > 0

    def test_multiple_files_match_error(self):
        """Test multiple files matching error (lines 84-86)."""
        with tempfile.NamedTemporaryFile(suffix='.tar.gz', delete=False) as tmp:
            tmp_path = tmp.name
            
        try:
            # Create tar with multiple files that could match
            with tarfile.open(tmp_path, 'w:gz') as tar:
                # Add multiple files with same name in different directories
                info1 = tarfile.TarInfo('dir1/config.txt')
                info1.size = 8
                tar.addfile(info1, io.BytesIO(b'config 1'))
                
                info2 = tarfile.TarInfo('dir2/config.txt')
                info2.size = 8
                tar.addfile(info2, io.BytesIO(b'config 2'))
                
            reader = TarFileReader(tmp_path)
            
            # This should trigger the multiple files error (lines 84-86)
            with pytest.raises(FileNotFoundError, match="Multiple files match"):
                reader.open_file('', 'config.txt')
                
        finally:
            os.unlink(tmp_path)

    def test_extractfile_returns_none(self):
        """Test when extractfile returns None (lines 113-114)."""
        with tempfile.NamedTemporaryFile(suffix='.tar.gz', delete=False) as tmp:
            tmp_path = tmp.name
            
        try:
            # Create tar with a directory entry (extractfile returns None for directories)
            with tarfile.open(tmp_path, 'w:gz') as tar:
                # Add a directory entry
                info = tarfile.TarInfo('test_dir')
                info.type = tarfile.DIRTYPE
                tar.addfile(info)
                
            reader = TarFileReader(tmp_path)
            
            # This should trigger the extractfile None error (lines 113-114)
            with pytest.raises(FileNotFoundError, match="File test_dir not found in tar archive"):
                reader.open_file('test_dir')
                
        finally:
            os.unlink(tmp_path)

    def test_find_member_with_exact_match_behavior(self):
        """Test find_member exact match behavior more thoroughly."""
        # Test with real test data if available
        test_tar_path = Path(__file__).parent.parent / "test_data" / "01.tar.gz"
        if not test_tar_path.exists():
            pytest.skip("Test data not available")
        
        reader = TarFileReader(str(test_tar_path))
        
        # Test internal member finding through exists method (covers line 89)
        # This tests the internal logic without calling private methods
        exists = reader.exists('Institution')
        assert exists is True
        
        # Test with non-existent file - should cover error handling paths
        exists = reader.exists('nonexistent.txt')
        assert exists is False