import pytest
from dicom_converter.utils.uid_manager import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def test_initialization_and_study_uid():
    manager1 = DicomUidManager()
    manager2 = DicomUidManager("1.2.3.4.5")
    assert isinstance(manager1.get_study_instance_uid(), str)
    assert manager2.get_study_instance_uid() == "1.2.3.4.5"
    assert manager1.get_study_instance_uid() != manager2.get_study_instance_uid()


def test_frame_of_reference_uid():
    manager = DicomUidManager()
    uid1 = manager.get_frame_of_reference_uid()
    uid2 = manager.get_frame_of_reference_uid()
    assert isinstance(uid1, str)
    assert uid1 == uid2  # Should always return the same value


def test_series_instance_uid_generation():
    manager = DicomUidManager()
    ct_uid_0 = manager.get_series_instance_uid("CT")
    ct_uid_1 = manager.get_series_instance_uid("CT", 1)
    struct_uid_0 = manager.get_series_instance_uid("RTSTRUCT")
    assert ct_uid_0 != ct_uid_1
    assert ct_uid_0 != struct_uid_0
    # Repeated calls should return the same UID
    assert manager.get_series_instance_uid("CT") == ct_uid_0
    assert manager.get_series_instance_uid("CT", 1) == ct_uid_1


def test_sop_instance_uid_generation():
    manager = DicomUidManager()
    sop_ct_0 = manager.get_sop_instance_uid("CT", 0)
    sop_ct_1 = manager.get_sop_instance_uid("CT", 1)
    sop_struct_0 = manager.get_sop_instance_uid("RTSTRUCT", 0)
    assert sop_ct_0 != sop_ct_1
    assert sop_ct_0 != sop_struct_0
    # Repeated calls should return the same UID
    assert manager.get_sop_instance_uid("CT", 0) == sop_ct_0
    assert manager.get_sop_instance_uid("CT", 1) == sop_ct_1


def test_reset_only_clears_series_and_sop_uids():
    manager = DicomUidManager()
    study_uid = manager.get_study_instance_uid()
    frame_uid = manager.get_frame_of_reference_uid()
    ct_uid = manager.get_series_instance_uid("CT")
    sop_uid = manager.get_sop_instance_uid("CT", 0)
    manager.reset()
    # Study and frame UIDs should persist
    assert manager.get_study_instance_uid() == study_uid
    assert manager.get_frame_of_reference_uid() == frame_uid
    # Series and SOP UIDs should be regenerated
    assert manager.get_series_instance_uid("CT") != ct_uid
    assert manager.get_sop_instance_uid("CT", 0) != sop_uid


def test_edge_case_unknown_modality():
    manager = DicomUidManager()
    # Should generate a UID for any string
    uid1 = manager.get_series_instance_uid("UNKNOWNMOD")
    uid2 = manager.get_sop_instance_uid("UNKNOWNMOD", 0)
    assert isinstance(uid1, str)
    assert isinstance(uid2, str)
    # Should be consistent for repeated calls
    assert manager.get_series_instance_uid("UNKNOWNMOD") == uid1
    assert manager.get_sop_instance_uid("UNKNOWNMOD", 0) == uid2


def test_multiple_modalities_and_indices():
    manager = DicomUidManager()
    uids = set()
    for modality in ["CT", "RTSTRUCT", "RTPLAN", "RTDOSE"]:
        for idx in range(3):
            uids.add(manager.get_series_instance_uid(modality, idx))
            uids.add(manager.get_sop_instance_uid(modality, idx))
    # All UIDs should be unique
    assert len(uids) == 4 * 3 * 2
