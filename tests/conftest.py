"""
Pytest configuration file for SimpleDicomConverter tests.

This module contains fixtures and configuration for pytest.
"""

import os
import sys
import pytest
import tempfile
import shutil

# Add the parent directory to the path so we can import the package
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))


@pytest.fixture
def temp_dir():
    """Create a temporary directory for test files."""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir)


@pytest.fixture
def test_data_dir():
    """Return the path to the test data directory."""
    return os.path.join(os.path.dirname(__file__), 'data')


@pytest.fixture
def sample_patient_data():
    """Return sample patient data for testing."""
    return {
        'Name': 'Test Patient',
        'MedicalRecordNumber': '12345',
        'Gender': 'M',
        'Weight': 70.0,
        'Height': 1.75,
        'ReferringPhysician': 'Dr. Test',
        'StudyID': 'Study001',
        'StudyDescription': 'Test Study'
    }


@pytest.fixture
def sample_image_data():
    """Return sample image data for testing."""
    return {
        'Modality': 'CT',
        'XDim': 512,
        'YDim': 512,
        'ZDim': 10,
        'XPixelSize': 1.0,
        'YPixelSize': 1.0,
        'SliceThickness': 2.0,
        'SeriesDescription': 'Test Series',
        'SeriesNumber': 1
    }


@pytest.fixture
def sample_roi_data():
    """Return sample ROI data for testing."""
    return {
        'Name': 'Test ROI',
        'Description': 'Test ROI Description',
        'Type': 'STRUCTURE',
        'StructureType': 'ORGAN',
        'Color_R': 255,
        'Color_G': 0,
        'Color_B': 0
    }


@pytest.fixture
def sample_plan_data():
    """Return sample plan data for testing."""
    return {
        'Name': 'Test Plan',
        'Description': 'Test Plan Description',
        'ApprovalStatus': 'UNAPPROVED',
        'PatientPosition': 'HFS'
    }
