"""
Tests for the Patient model, reader, and writer.
"""

from pathlib import Path
from datetime import date
import pytest

from pinnacle_io.models import Patient
from pinnacle_io.readers.patient_reader import PatientReader
from pinnacle_io.writers.patient_writer import PatientWriter


def test_patient_initialization():
    """Test creating a Patient directly with kwargs."""
    # Test with minimal data
    patient = Patient(
        patient_id=123,
        first_name="<PERSON>",
        middle_name="A",
        last_name="<PERSON><PERSON>",
        gender="Male",
        medical_record_number="MRN12345",
        diagnosis="Healthy",
        comment="Test patient",
        date_of_birth="1980-01-15 00:00:00",
    )

    assert patient.patient_id == 123
    assert patient.first_name == "<PERSON>"
    assert patient.middle_name == "A"
    assert patient.last_name == "Doe"
    assert patient.gender == "Male"
    assert patient.medical_record_number == "MRN12345"
    assert patient.diagnosis == "Healthy"
    assert patient.comment == "Test patient"
    assert patient.date_of_birth is not None
    assert patient.date_of_birth.strftime("%Y-%m-%d %H:%M:%S") == "1980-01-15 00:00:00"


def test_patient_name_methods():
    """Test the patient name formatting methods and properties."""
    patient = Patient(first_name="<PERSON>", middle_name="<PERSON>", last_name="Doe")

    # Test full name properties
    assert patient.full_name == "<PERSON> <PERSON> Doe"
    assert patient.name == "<PERSON> <PERSON> Doe"  # Backward compatibility
    assert patient.first_and_last_name == "<PERSON> Doe"
    assert patient.last_and_first_name == "Doe, <PERSON>"
    assert patient.dicom_name == "Doe^<PERSON>^<PERSON>"

    # Test with missing middle name
    patient2 = Patient(first_name="Jane", last_name="Smith")
    assert patient2.full_name == "Jane Smith"
    assert patient2.name == "Jane Smith"
    assert patient2.first_and_last_name == "Jane Smith"
    assert patient2.last_and_first_name == "Smith, Jane"
    assert patient2.dicom_name == "Smith^Jane^"

    # Test with only last name
    patient3 = Patient(last_name="Johnson")
    assert patient3.full_name == "Johnson"
    assert patient3.name == "Johnson"
    assert patient3.first_and_last_name == "Johnson"
    assert patient3.last_and_first_name == "Johnson"
    assert patient3.dicom_name == "Johnson^^"


def test_patient_age_calculation():
    """Test the patient age calculation method."""
    # Test with date string
    patient1 = Patient(date_of_birth="1980-01-15")
    age1 = patient1.get_age(reference_date=date(2025, 5, 21))
    assert age1 == 45

    # Test with date before birthday in reference year
    patient2 = Patient(date_of_birth="1980-06-15")
    age2 = patient2.get_age(reference_date=date(2025, 5, 21))
    assert age2 == 44  # Birthday hasn't occurred yet in 2025

    # Test with different date format
    patient3 = Patient(date_of_birth="01/15/1980")
    age3 = patient3.get_age(reference_date=date(2025, 5, 21))
    assert age3 == 45

    # Test with no birth date
    patient4 = Patient()
    assert patient4.get_age() is None

    # Test with invalid date format
    patient5 = Patient(date_of_birth="invalid-date")
    assert patient5.get_age() is None


def test_write_patient_file(tmp_path):
    """Tests writing a Patient file."""
    patient = Patient()
    with pytest.raises(NotImplementedError):
        PatientWriter.write(patient, tmp_path / "Patient_1")


def test_patient_relationships():
    """Test Patient relationships with Institution, Plan, and ImageSet."""
    from pinnacle_io.models import Institution

    # Create an institution
    institution = Institution(institution_id=1, name="Test Hospital", institution_path="/test/path")

    # Create a patient with relationship to institution
    patient = Patient(patient_id=123, first_name="John", last_name="Doe", institution=institution)

    # Verify institution relationship
    assert patient.institution is institution
    assert patient.institution_id == institution.id
    assert patient in institution.patient_list

    # Test adding plans through constructor
    patient_with_plans = Patient(
        patient_id=456,
        first_name="Jane",
        last_name="Smith",
        plan_list=[
            {"plan_id": 1, "plan_name": "Test Plan 1"},
            {"plan_id": 2, "plan_name": "Test Plan 2"},
        ],
    )

    # Verify plans relationship
    assert len(patient_with_plans.plan_list) == 2
    assert patient_with_plans.plan_list[0].plan_id == 1
    assert patient_with_plans.plan_list[0].name == "Test Plan 1"
    assert patient_with_plans.plan_list[1].plan_id == 2
    assert patient_with_plans.plan_list[1].name == "Test Plan 2"
    assert all(plan.patient is patient_with_plans for plan in patient_with_plans.plan_list)

    # Test adding image sets through constructor
    patient_with_image_sets = Patient(
        patient_id=789,
        first_name="Robert",
        last_name="Johnson",
        image_set_list=[
            {"image_set_id": 1, "modality": "CT", "image_name": "Test Image 1"},
            {"image_set_id": 2, "modality": "MR", "image_name": "Test Image 2"},
        ],
    )

    # Verify image sets relationship
    assert len(patient_with_image_sets.image_set_list) == 2
    assert patient_with_image_sets.image_set_list[0].modality == "CT"
    assert patient_with_image_sets.image_set_list[0].image_name == "Test Image 1"
    assert patient_with_image_sets.image_set_list[1].modality == "MR"
    assert patient_with_image_sets.image_set_list[1].image_name == "Test Image 2"
    assert all(image_set.patient is patient_with_image_sets for image_set in patient_with_image_sets.image_set_list)

    # Test adding both plans and image sets
    complete_patient = Patient(
        patient_id=101,
        first_name="Complete",
        last_name="Patient",
        institution=institution,
        plan_list=[{"plan_id": 3, "plan_name": "Complete Plan"}],
        image_set_list=[{"image_set_id": 3, "modality": "PT", "image_name": "Complete Image"}],
    )

    # Verify all relationships
    assert complete_patient.institution is institution
    assert len(complete_patient.plan_list) == 1
    assert complete_patient.plan_list[0].name == "Complete Plan"
    assert len(complete_patient.image_set_list) == 1
    assert complete_patient.image_set_list[0].image_name == "Complete Image"


def test_patient_dicom_name_property():
    """Test patient DICOM name property."""
    # Test with all name components
    patient = Patient(first_name="John", middle_name="Q", last_name="Doe")
    assert patient.dicom_name == "Doe^John^Q"

    # Test with missing components
    patient_partial = Patient(first_name="Jane", last_name="Smith")
    assert patient_partial.dicom_name == "Smith^Jane^"

    # Test with only last name
    patient_last_only = Patient(last_name="Johnson")
    assert patient_last_only.dicom_name == "Johnson^^"

    # Test with empty names
    patient_empty = Patient()
    assert patient_empty.dicom_name == "^^"


def test_patient_plan_management():
    """Test patient plan management methods."""
    from pinnacle_io.models import Plan

    patient = Patient(first_name="Test", last_name="Patient")

    # Test adding plan
    plan1 = Plan(plan_id=1, name="Plan 1")
    patient.add_plan(plan1)
    assert plan1 in patient.plan_list
    assert plan1.patient is patient

    # Test adding duplicate plan should raise error
    try:
        patient.add_plan(plan1)
        assert False, "Should raise ValueError for duplicate plan"
    except ValueError as e:
        assert "already associated" in str(e)

    # Add more plans for testing lookup methods
    plan2 = Plan(plan_id=2, name="Plan 2")
    plan3 = Plan(plan_id=3, name="Plan 1")  # Same name as plan1
    patient.add_plan(plan2)
    patient.add_plan(plan3)

    # Test get_plan_by_id
    found_plan = patient.get_plan_by_id(1)
    assert found_plan is plan1

    found_plan = patient.get_plan_by_id("2")  # Test string ID
    assert found_plan is plan2

    not_found = patient.get_plan_by_id(999)
    assert not_found is None

    # Test get_plan_by_name
    found_plan = patient.get_plan_by_name("Plan 2")
    assert found_plan is plan2

    not_found = patient.get_plan_by_name("Nonexistent Plan")
    assert not_found is None

    # Test get_plans_by_name (multiple plans with same name)
    plans_with_name = patient.get_plans_by_name("Plan 1")
    assert len(plans_with_name) == 2
    assert plan1 in plans_with_name
    assert plan3 in plans_with_name

    empty_list = patient.get_plans_by_name("Nonexistent Plan")
    assert len(empty_list) == 0


def test_patient_image_set_management():
    """Test patient image set management methods."""
    from pinnacle_io.models import ImageSet

    patient = Patient(first_name="Test", last_name="Patient")

    # Test adding image set
    image_set1 = ImageSet(image_set_id=1, image_name="CT Sim")
    patient.add_image_set(image_set1)
    assert image_set1 in patient.image_set_list
    assert image_set1.patient is patient

    # Test adding duplicate image set should raise error
    try:
        patient.add_image_set(image_set1)
        assert False, "Should raise ValueError for duplicate image set"
    except ValueError as e:
        assert "already associated" in str(e)

    # Add more image sets for testing lookup methods
    image_set2 = ImageSet(image_set_id=2, image_name="MR T1")
    image_set3 = ImageSet(image_set_id=3, image_name="CT Sim")  # Same image_name as image_set1
    patient.add_image_set(image_set2)
    patient.add_image_set(image_set3)

    # Test get_image_set_by_id
    found_image_set = patient.get_image_set_by_id(1)
    assert found_image_set is image_set1

    found_image_set = patient.get_image_set_by_id("2")  # Test string ID
    assert found_image_set is image_set2

    not_found = patient.get_image_set_by_id(999)
    assert not_found is None

    # Test get_image_set_by_name
    found_image_set = patient.get_image_set_by_name("MR T1")
    assert found_image_set is image_set2

    not_found = patient.get_image_set_by_name("Nonexistent Image Set")
    assert not_found is None

    # Test get_image_sets_by_name (multiple image sets with same name)
    image_sets_with_name = patient.get_image_sets_by_name("CT Sim")
    assert len(image_sets_with_name) == 2
    assert image_set1 in image_sets_with_name
    assert image_set3 in image_sets_with_name

    empty_list = patient.get_image_sets_by_name("Nonexistent Image Set")
    assert len(empty_list) == 0


def test_patient_age_edge_cases():
    """Test patient age calculation edge cases."""
    from datetime import date

    # Test with invalid date string
    patient_invalid = Patient(date_of_birth="invalid-date")
    assert patient_invalid.get_age() is None

    # Test with None date_of_birth
    patient_none = Patient()
    assert patient_none.get_age() is None
    assert patient_none.age is None

    # Test calculate_age alias
    patient_valid = Patient(date_of_birth="1980-01-15")
    age1 = patient_valid.get_age(date(2025, 5, 21))
    age2 = patient_valid.calculate_age(date(2025, 5, 21))
    assert age1 == age2

    # Test leap year edge case
    patient_leap = Patient(date_of_birth="2000-02-29")
    age_leap = patient_leap.get_age(date(2025, 2, 28))
    assert age_leap == 24  # Birthday hasn't occurred yet

    age_leap_after = patient_leap.get_age(date(2025, 3, 1))
    assert age_leap_after == 25  # Birthday has occurred


def test_patient_name_properties():
    """Test patient name properties and backward compatibility."""
    # Test full_name property
    patient = Patient(first_name="John", middle_name="Q", last_name="Doe")
    assert patient.full_name == "John Q Doe"
    assert patient.name == "John Q Doe"  # Backward compatibility

    # Test with missing middle name
    patient_no_middle = Patient(first_name="Jane", last_name="Smith")
    assert patient_no_middle.full_name == "Jane Smith"

    # Test with only first name
    patient_first_only = Patient(first_name="Bob")
    assert patient_first_only.full_name == "Bob"

    # Test with only last name
    patient_last_only = Patient(last_name="Johnson")
    assert patient_last_only.full_name == "Johnson"

    # Test with whitespace handling
    patient_whitespace = Patient(first_name="  John  ", middle_name="  Q  ", last_name="  Doe  ")
    assert patient_whitespace.full_name == "John Q Doe"

    # Test with empty strings
    patient_empty = Patient(first_name="", middle_name="", last_name="")
    assert patient_empty.full_name == ""

    # Test patient with very long names (will be truncated to 64 characters)
    long_name = "A" * 1000
    patient_long = Patient(first_name=long_name, last_name=long_name)
    # Names are truncated to 64 characters in the database
    assert patient_long.first_name is not None
    assert len(patient_long.first_name) == 64
    assert patient_long.last_name is not None
    assert len(patient_long.last_name) == 64
    assert patient_long.first_name == "A" * 64
    assert patient_long.last_name == "A" * 64

    # Test patient with special characters
    patient_special = Patient(first_name="José", middle_name="María", last_name="García-López")
    assert patient_special.full_name == "José María García-López"

    # Test patient with numeric strings
    patient_numeric = Patient(patient_id=999999999, medical_record_number="123456789012345")
    assert patient_numeric.patient_id == 999999999
    assert patient_numeric.medical_record_number == "123456789012345"

    # Test patient with None values
    patient_none = Patient()
    assert patient_none.patient_id is None
    assert patient_none.first_name is None
    assert patient_none.last_name is None
    assert patient_none.full_name == ""
