"""
Tests for the VersionedBase SQLAlchemy model.
"""

from datetime import datetime, timezone, timedelta
import pytest

from tests.conftest import _TestVersionedModel


def test_versioned_base_initialization():
    """Test initialization of VersionedBase model."""
    # Create a new instance
    now = datetime.now(timezone.utc).replace(tzinfo=None)
    model = _TestVersionedModel(
        name="Versioned Test",
        version_login_name="testuser",
        create_time_stamp=now,
        write_time_stamp=now,
        last_modified_time_stamp=now,
    )

    # Check attributes
    assert model.name == "Versioned Test"
    assert model.version_login_name == "testuser"
    assert model.create_time_stamp == now
    assert model.write_time_stamp == now
    assert model.last_modified_time_stamp == now


def test_versioned_base_defaults():
    """Test default values for version fields and timestamps."""
    model = _TestVersionedModel(name="Default Test")
    assert model.write_version is None
    assert model.create_version is None
    assert model.version_login_name is None
    assert isinstance(model.create_time_stamp, datetime)
    assert isinstance(model.write_time_stamp, datetime)
    assert isinstance(model.last_modified_time_stamp, datetime)
    # Should be timezone-aware and UTC
    assert model.create_time_stamp.tzinfo == timezone.utc
    assert model.write_time_stamp.tzinfo == timezone.utc
    assert model.last_modified_time_stamp.tzinfo == timezone.utc
    # Timestamps should be close to now
    now = datetime.now(timezone.utc)
    for ts in [model.create_time_stamp, model.write_time_stamp, model.last_modified_time_stamp]:
        assert abs((now - ts).total_seconds()) < 5


def test_versioned_base_object_version_dict():
    """Test initialization using object_version dict (lowercase keys)."""
    now = datetime.now(timezone.utc)
    obj_ver = {
        "WriteVersion": "3.1.2",
        "CreateVersion": "3.0.0",
        "LoginName": "DOMAIN\\user",
        "CreateTimeStamp": now,
        "WriteTimeStamp": now,
        "LastModifiedTimeStamp": now,
    }
    model = _TestVersionedModel(name="ObjVer Test", object_version=obj_ver)
    assert model.write_version == "3.1.2"
    assert model.create_version == "3.0.0"
    assert model.version_login_name == "DOMAIN\\user"
    assert model.create_time_stamp == now
    assert model.write_time_stamp == now
    assert model.last_modified_time_stamp == now


def test_versioned_base_object_version_dict_camelcase():
    """Test initialization using ObjectVersion dict (upper camel case key)."""
    now = datetime.now(timezone.utc)
    obj_ver = {
        "WriteVersion": "4.0.0",
        "LoginName": "camelUser",
    }
    model = _TestVersionedModel(name="Camel Test", ObjectVersion=obj_ver)
    assert model.write_version == "4.0.0"
    assert model.version_login_name == "camelUser"
    # Defaults for missing fields
    assert model.create_version is None
    assert isinstance(model.create_time_stamp, datetime)
    assert isinstance(model.write_time_stamp, datetime)
    assert isinstance(model.last_modified_time_stamp, datetime)


def test_versioned_base_object_version_overridden_by_kwargs():
    """Test that explicit kwargs override object_version dict values."""
    now = datetime.now(timezone.utc)
    obj_ver = {
        "WriteVersion": "should_not_appear",
        "LoginName": "should_not_appear",
    }
    model = _TestVersionedModel(
        name="Override Test",
        object_version=obj_ver,
        write_version="override_version",
        version_login_name="override_user",
    )
    assert model.write_version == "override_version"
    assert model.version_login_name == "override_user"


def test_versioned_base_partial_object_version():
    """Test partial object_version dict (only some fields present)."""
    obj_ver = {"WriteVersion": "5.0.0"}
    model = _TestVersionedModel(name="Partial Test", object_version=obj_ver)
    assert model.write_version == "5.0.0"
    assert model.create_version is None
    assert model.version_login_name is None


def test_versioned_base_timestamps_are_utc():
    """Test that all timestamps are timezone-aware and UTC."""
    model = _TestVersionedModel(name="TZ Test")
    for ts in [model.create_time_stamp, model.write_time_stamp, model.last_modified_time_stamp]:
        assert ts.tzinfo == timezone.utc


def test_versioned_base_string_truncation():
    """Test that string fields are truncated if over max length (simulate)."""
    # WriteVersion and CreateVersion max length is 50, VersionLoginName is 100
    long_str = "x" * 120
    model = _TestVersionedModel(
        name="Trunc Test",
        write_version=long_str,
        create_version=long_str,
        version_login_name=long_str,
    )
    assert len(model.write_version) <= 50
    assert len(model.create_version) <= 50
    assert len(model.version_login_name) <= 100
