"""
Comprehensive tests for the types module.

This module tests all custom SQLAlchemy types and spatial coordinate classes
including JsonList, VoxelSize, Coordinate, Dimension, Index, ContinuousIndex,
VolumeSize, and SpatialBase classes.
"""

import pytest
import math
from typing import Dict, List, Any

from pinnacle_io.models.types import JsonList, VoxelSize, Coordinate, Dimension, Index, ContinuousIndex, VolumeSize, SpatialBase


class TestJsonList:
    """Test the JsonList custom SQLAlchemy type."""

    def test_process_bind_param(self):
        """Test converting Python list to JSON string for database storage."""
        json_list = JsonList()

        # Test with normal list
        test_list = [1, 2, 3, "test", {"key": "value"}]
        result = json_list.process_bind_param(test_list, None)
        assert isinstance(result, str)
        assert result == str(test_list)

        # Test with None
        result = json_list.process_bind_param(None, None)
        assert result is None

        # Test with empty list
        result = json_list.process_bind_param([], None)
        assert result == "[]"

    def test_process_result_value(self):
        """Test converting JSON string from database back to Python list."""
        json_list = JsonList()

        # Test with valid list string
        test_str = "[1, 2, 3, 'test']"
        result = json_list.process_result_value(test_str, None)
        assert result == [1, 2, 3, "test"]

        # Test with None
        result = json_list.process_result_value(None, None)
        assert result is None

        # Test with empty string
        result = json_list.process_result_value("", None)
        assert result is None

        # Test with invalid JSON
        result = json_list.process_result_value("invalid json", None)
        assert result is None

        # Test with malformed string
        result = json_list.process_result_value("[1, 2, 3", None)
        assert result is None

    def test_round_trip(self):
        """Test round-trip conversion (list -> string -> list)."""
        json_list = JsonList()

        original_list = [1, 2.5, "test", True, None]
        json_str = json_list.process_bind_param(original_list, None)
        recovered_list = json_list.process_result_value(json_str, None)

        assert recovered_list == original_list


class TestSpatialBase:
    """Test the abstract SpatialBase class through concrete implementations."""

    def test_coordinate_initialization(self):
        """Test Coordinate initialization and basic properties."""
        coord = Coordinate(1.5, 2.5, 3.5)
        assert coord.x == 1.5
        assert coord.y == 2.5
        assert coord.z == 3.5

    def test_coordinate_to_dict(self):
        """Test converting coordinate to dictionary."""
        coord = Coordinate(1.0, 2.0, 3.0)
        result = coord.to_dict()
        expected = {"x": 1.0, "y": 2.0, "z": 3.0}
        assert result == expected

    def test_coordinate_to_list(self):
        """Test converting coordinate to list."""
        coord = Coordinate(1.0, 2.0, 3.0)
        result = coord.to_list()
        expected = [1.0, 2.0, 3.0]
        assert result == expected

    def test_coordinate_from_dict(self):
        """Test creating coordinate from dictionary."""
        data = {"x": 4.0, "y": 5.0, "z": 6.0}
        coord = Coordinate.from_dict(data)
        assert coord.x == 4.0
        assert coord.y == 5.0
        assert coord.z == 6.0

        # Test with missing keys (should default to 0)
        partial_data = {"x": 1.0, "z": 3.0}
        coord_partial = Coordinate.from_dict(partial_data)
        assert coord_partial.x == 1.0
        assert coord_partial.y is None
        assert coord_partial.z == 3.0

    def test_coordinate_equality(self):
        """Test coordinate equality comparison."""
        coord1 = Coordinate(1.0, 2.0, 3.0)
        coord2 = Coordinate(1.0, 2.0, 3.0)
        coord3 = Coordinate(1.0, 2.0, 3.1)

        assert coord1 == coord2
        assert coord1 != coord3
        assert coord1 != "not a coordinate"

    def test_coordinate_repr(self):
        """Test coordinate string representation."""
        coord = Coordinate(1.0, 2.0, 3.0)
        expected = "Coordinate(x=1.0, y=2.0, z=3.0)"
        assert repr(coord) == expected


class TestCoordinate:
    """Test the Coordinate class specifically."""

    def test_distance_calculation(self):
        """Test distance calculation between coordinates."""
        coord1 = Coordinate(0.0, 0.0, 0.0)
        coord2 = Coordinate(3.0, 4.0, 0.0)

        distance = coord1.distance_to(coord2)
        expected_distance = 5.0  # 3-4-5 triangle
        assert abs(distance - expected_distance) < 1e-10

        # Test distance to self
        assert coord1.distance_to(coord1) == 0.0

        # Test 3D distance
        coord3 = Coordinate(1.0, 1.0, 1.0)
        coord4 = Coordinate(2.0, 2.0, 2.0)
        distance_3d = coord3.distance_to(coord4)
        expected_3d = math.sqrt(3)  # sqrt(1^2 + 1^2 + 1^2)
        assert abs(distance_3d - expected_3d) < 1e-10

    def test_coordinate_addition(self):
        """Test coordinate addition."""
        coord1 = Coordinate(1.0, 2.0, 3.0)
        coord2 = Coordinate(4.0, 5.0, 6.0)

        result = coord1 + coord2
        assert result.x == 5.0
        assert result.y == 7.0
        assert result.z == 9.0

        # Test that original coordinates are unchanged
        assert coord1.x == 1.0
        assert coord2.x == 4.0

        # Test addition with non-coordinate should raise TypeError
        with pytest.raises(TypeError):
            coord1 + "not a coordinate"

    def test_coordinate_scalar_multiplication(self):
        """Test coordinate scalar multiplication."""
        coord = Coordinate(2.0, 3.0, 4.0)

        # Test left multiplication
        result = coord * 2.5
        assert result.x == 5.0
        assert result.y == 7.5
        assert result.z == 10.0

        # Test right multiplication
        result = 2.5 * coord
        assert result.x == 5.0
        assert result.y == 7.5
        assert result.z == 10.0

        # Test multiplication by zero
        result = coord * 0
        assert result.x == 0.0
        assert result.y == 0.0
        assert result.z == 0.0

        # Test multiplication with non-numeric should raise TypeError
        with pytest.raises(TypeError):
            coord * "not a number"

    def test_coordinate_validation(self):
        """Test coordinate validation (should accept any float)."""
        # Test with negative values
        coord_neg = Coordinate(-1.0, -2.0, -3.0)
        assert coord_neg.x == -1.0
        assert coord_neg.y == -2.0
        assert coord_neg.z == -3.0

        # Test with zero values
        coord_zero = Coordinate(0.0, 0.0, 0.0)
        assert coord_zero.x == 0.0
        assert coord_zero.y == 0.0
        assert coord_zero.z == 0.0

        # Test with very large values
        large_val = 1e10
        coord_large = Coordinate(large_val, large_val, large_val)
        assert coord_large.x == large_val


class TestVoxelSize:
    """Test the VoxelSize class."""

    def test_voxel_size_initialization(self):
        """Test VoxelSize initialization and validation."""
        # Test with positive values
        voxel_size = VoxelSize(1.0, 2.0, 3.0)
        assert voxel_size.x == 1.0
        assert voxel_size.y == 2.0
        assert voxel_size.z == 3.0

        # Test with zero values (should be allowed)
        voxel_size_zero = VoxelSize(0.0, 0.0, 0.0)
        assert voxel_size_zero.x == 0.0
        assert voxel_size_zero.y == 0.0
        assert voxel_size_zero.z == 0.0

        # Test with None values (should default to 0.0)
        voxel_size_none = VoxelSize(None, 1.0, 2.0)
        assert voxel_size_none.x is None
        assert voxel_size_none.y == 1.0
        assert voxel_size_none.z == 2.0

    def test_voxel_size_validation_errors(self):
        """Test VoxelSize validation errors."""
        # Test with negative values (should raise ValueError)
        with pytest.raises(ValueError, match="x must be non-negative"):
            VoxelSize(-1.0, 1.0, 1.0)

        with pytest.raises(ValueError, match="y must be non-negative"):
            VoxelSize(1.0, -1.0, 1.0)

        with pytest.raises(ValueError, match="z must be non-negative"):
            VoxelSize(1.0, 1.0, -1.0)

    def test_voxel_size_volume(self):
        """Test VoxelSize volume calculation."""
        voxel_size = VoxelSize(2.0, 3.0, 4.0)
        expected_volume = 2.0 * 3.0 * 4.0
        assert voxel_size.volume() == expected_volume

        # Test with zero dimension
        voxel_size_zero = VoxelSize(0.0, 1.0, 1.0)
        assert voxel_size_zero.volume() == 0.0


class TestVolumeSize:
    """Test the VolumeSize class."""

    def test_volume_size_initialization(self):
        """Test VolumeSize initialization and validation."""
        # Test with positive values
        volume_size = VolumeSize(10.0, 20.0, 30.0)
        assert volume_size.x == 10.0
        assert volume_size.y == 20.0
        assert volume_size.z == 30.0

        # Test with zero values (should be allowed)
        volume_size_zero = VolumeSize(0.0, 0.0, 0.0)
        assert volume_size_zero.x == 0.0
        assert volume_size_zero.y == 0.0
        assert volume_size_zero.z == 0.0

    def test_volume_size_validation_errors(self):
        """Test VolumeSize validation errors."""
        # Test with negative values (should raise ValueError)
        with pytest.raises(ValueError, match="x must be non-negative"):
            VolumeSize(-1.0, 1.0, 1.0)

        with pytest.raises(ValueError, match="y must be non-negative"):
            VolumeSize(1.0, -1.0, 1.0)

        with pytest.raises(ValueError, match="z must be non-negative"):
            VolumeSize(1.0, 1.0, -1.0)

    def test_volume_size_volume(self):
        """Test VolumeSize volume calculation."""
        volume_size = VolumeSize(5.0, 6.0, 7.0)
        expected_volume = 5.0 * 6.0 * 7.0
        assert volume_size.volume() == expected_volume


class TestDimension:
    """Test the Dimension class."""

    def test_dimension_initialization(self):
        """Test Dimension initialization and validation."""
        # Test with positive values
        dim = Dimension(10, 20, 30)
        assert dim.x == 10
        assert dim.y == 20
        assert dim.z == 30

    def test_dimension_validation_errors(self):
        """Test Dimension validation errors."""
        # Test with zero values (should raise ValueError)
        with pytest.raises(ValueError, match="x must be positive"):
            Dimension(0, 1, 1)

        with pytest.raises(ValueError, match="y must be positive"):
            Dimension(1, 0, 1)

        with pytest.raises(ValueError, match="z must be positive"):
            Dimension(1, 1, 0)

        # Test with negative values (should raise ValueError)
        with pytest.raises(ValueError, match="x must be positive"):
            Dimension(-1, 1, 1)

    def test_dimension_num_voxels(self):
        """Test Dimension num_voxels calculation."""
        dim = Dimension(5, 6, 7)
        expected_voxels = 5 * 6 * 7
        assert dim.num_voxels() == expected_voxels

    def test_dimension_to_volume_size(self):
        """Test Dimension to_volume_size conversion."""
        dim = Dimension(10, 20, 30)
        voxel_size = VoxelSize(1.5, 2.0, 2.5)

        volume_size = dim.to_volume_size(voxel_size)
        assert volume_size.x == 10 * 1.5
        assert volume_size.y == 20 * 2.0
        assert volume_size.z == 30 * 2.5

    def test_dimension_contains(self):
        """Test Dimension contains method."""
        dim = Dimension(10, 20, 30)

        # Test valid indices
        assert dim.contains(Index(0, 0, 0))
        assert dim.contains(Index(9, 19, 29))
        assert dim.contains(Index(5, 10, 15))

        # Test invalid indices
        assert not dim.contains(Index(10, 0, 0))  # x out of bounds
        assert not dim.contains(Index(0, 20, 0))  # y out of bounds
        assert not dim.contains(Index(0, 0, 30))  # z out of bounds

        # Test that negative indices can't be created (Index validates non-negative)
        with pytest.raises(ValueError):
            Index(-1, 0, 0)


class TestIndex:
    """Test the Index class."""

    def test_index_initialization(self):
        """Test Index initialization and validation."""
        # Test with positive values
        idx = Index(5, 10, 15)
        assert idx.x == 5
        assert idx.y == 10
        assert idx.z == 15

        # Test with zero values (should be allowed)
        idx_zero = Index(0, 0, 0)
        assert idx_zero.x == 0
        assert idx_zero.y == 0
        assert idx_zero.z == 0

    def test_index_validation_errors(self):
        """Test Index validation errors."""
        # Test with negative values (should raise ValueError)
        with pytest.raises(ValueError, match="x must be non-negative"):
            Index(-1, 0, 0)

        with pytest.raises(ValueError, match="y must be non-negative"):
            Index(0, -1, 0)

        with pytest.raises(ValueError, match="z must be non-negative"):
            Index(0, 0, -1)

    def test_index_to_continuous(self):
        """Test Index to_continuous conversion."""
        idx = Index(5, 10, 15)
        continuous_idx = idx.to_continuous()

        assert isinstance(continuous_idx, ContinuousIndex)
        assert continuous_idx.x == 5.0
        assert continuous_idx.y == 10.0
        assert continuous_idx.z == 15.0


class TestContinuousIndex:
    """Test the ContinuousIndex class."""

    def test_continuous_index_initialization(self):
        """Test ContinuousIndex initialization."""
        # Test with positive values
        cidx = ContinuousIndex(5.5, 10.7, 15.3)
        assert cidx.x == 5.5
        assert cidx.y == 10.7
        assert cidx.z == 15.3

        # Test with negative values (should be allowed)
        cidx_neg = ContinuousIndex(-1.5, -2.7, -3.3)
        assert cidx_neg.x == -1.5
        assert cidx_neg.y == -2.7
        assert cidx_neg.z == -3.3

    def test_continuous_index_to_index(self):
        """Test ContinuousIndex to_index conversion."""
        cidx = ContinuousIndex(5.7, 10.3, 15.9)
        idx = cidx.to_index()

        assert isinstance(idx, Index)
        assert idx.x == 5  # Floor of 5.7
        assert idx.y == 10  # Floor of 10.3
        assert idx.z == 15  # Floor of 15.9

        # Test with negative values (Index doesn't allow negative, so this should raise)
        cidx_neg = ContinuousIndex(-1.7, -2.3, -3.9)
        with pytest.raises(ValueError):
            cidx_neg.to_index()  # Should fail because Index requires non-negative values

    def test_continuous_index_round(self):
        """Test ContinuousIndex round method."""
        cidx = ContinuousIndex(5.4, 10.6, 15.5)
        rounded = cidx.rounded()
        assert rounded.x == 5
        assert rounded.y == 11
        assert rounded.z == 16


class TestSpatialTypeInteroperability:
    """Test interoperability between different spatial types."""

    def test_type_conversions(self):
        """Test conversions between spatial types."""
        # Index -> ContinuousIndex -> Index
        original_idx = Index(5, 10, 15)
        continuous_idx = original_idx.to_continuous()
        back_to_idx = continuous_idx.to_index()

        assert back_to_idx.x == original_idx.x
        assert back_to_idx.y == original_idx.y
        assert back_to_idx.z == original_idx.z

        # Dimension -> VolumeSize
        dim = Dimension(10, 20, 30)
        voxel_size = VoxelSize(1.0, 2.0, 3.0)
        volume_size = dim.to_volume_size(voxel_size)

        assert volume_size.x == 10.0
        assert volume_size.y == 40.0
        assert volume_size.z == 90.0

    def test_spatial_base_polymorphism(self):
        """Test that all spatial types inherit from SpatialBase."""
        coord = Coordinate(1.0, 2.0, 3.0)
        voxel_size = VoxelSize(1.0, 2.0, 3.0)
        volume_size = VolumeSize(1.0, 2.0, 3.0)
        dim = Dimension(1, 2, 3)
        idx = Index(1, 2, 3)
        cidx = ContinuousIndex(1.0, 2.0, 3.0)

        # All should be instances of SpatialBase
        assert isinstance(coord, SpatialBase)
        assert isinstance(voxel_size, SpatialBase)
        assert isinstance(volume_size, SpatialBase)
        assert isinstance(dim, SpatialBase)
        assert isinstance(idx, SpatialBase)
        assert isinstance(cidx, SpatialBase)

        # All should have the same interface
        for spatial_obj in [coord, voxel_size, volume_size, dim, idx, cidx]:
            assert hasattr(spatial_obj, "x")
            assert hasattr(spatial_obj, "y")
            assert hasattr(spatial_obj, "z")
            assert hasattr(spatial_obj, "to_dict")
            assert hasattr(spatial_obj, "to_list")
            assert hasattr(spatial_obj, "__eq__")
            assert hasattr(spatial_obj, "__repr__")
