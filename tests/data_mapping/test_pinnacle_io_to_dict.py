"""
Test script to understand pinnacle_io to_dict() format vs pymedphys expected format.
This test will help us implement Step 1: Data Model Analysis and Mapping.
"""

import sys
from pathlib import Path

# Add parent directories to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from pinnacle_io.api import PinnacleReader
import pytest
import json
from pprint import pprint


@pytest.fixture
def test_archive_path():
    """Path to test archive."""
    return Path(__file__).parent.parent / "test_data" / "archive_01"


def test_pinnacle_io_to_dict_format(test_archive_path):
    """Test the to_dict() format from pinnacle_io models."""
    if not test_archive_path.exists():
        pytest.skip(f"Test data not found at {test_archive_path}")
    
    # Load test data using PinnacleReader
    reader = PinnacleReader(test_archive_path)
    institution = reader.get_institution()
    
    assert institution is not None, "Should find institution"
    assert len(institution.patient_lite_list) > 0, "Should find at least one patient"
    
    # Get first patient
    patient_lite = institution.patient_lite_list[0] 
    patient = reader.get_patient(institution=institution, patient=patient_lite)
    
    assert patient is not None, "Should load patient"
    assert len(patient.plan_list) > 0, "Should find at least one plan"
    
    print(f"\n=== INSTITUTION to_dict() ===")
    institution_dict = institution.to_dict(include_relationships=False)
    pprint(institution_dict)
    
    print(f"\n=== PATIENT to_dict() ===")
    patient_dict = patient.to_dict(include_relationships=False)
    pprint(patient_dict)
    
    # Skip relationships for now due to incompatible to_dict() methods in related models
    print(f"\n=== PATIENT fields available ===")
    print(f"Patient attributes: {[attr for attr in dir(patient) if not attr.startswith('_')]}")
    
    # Show key patient properties for DICOM mapping
    print(f"Patient name fields: FirstName={patient.first_name}, LastName={patient.last_name}")
    print(f"Patient MRN: {patient.medical_record_number}")
    print(f"Patient DOB: {patient.date_of_birth}")
    print(f"Patient gender: {patient.gender}")
    print(f"Number of plans: {len(patient.plan_list)}")
    
    # Get first plan
    plan = patient.plan_list[0]
    print(f"\n=== PLAN to_dict() ===")
    try:
        plan_dict = plan.to_dict(include_relationships=False)
        pprint(plan_dict)
    except TypeError:
        # Fallback if to_dict doesn't support include_relationships parameter
        plan_dict = plan.to_dict()
        pprint(plan_dict)
    
    print(f"\n=== PLAN fields available ===")
    print(f"Plan attributes: {[attr for attr in dir(plan) if not attr.startswith('_')]}")
    print(f"Plan name: {plan.name}")
    if hasattr(plan, 'primary_ct_image_set_id'):
        print(f"Primary CT ImageSet ID: {plan.primary_ct_image_set_id}")
    if hasattr(plan, 'plan_id'):
        print(f"Plan ID: {plan.plan_id}")
    
    # Try to get trials
    trials = reader.get_trials(institution, patient, plan)
    if trials:
        trial = trials[0]
        print(f"\n=== TRIAL to_dict() ===")
        try:
            trial_dict = trial.to_dict(include_relationships=False)
            pprint(trial_dict)
        except TypeError:
            # Fallback if to_dict doesn't support include_relationships parameter
            trial_dict = trial.to_dict()
            pprint(trial_dict)
        
        print(f"\n=== TRIAL fields available ===")
        print(f"Trial attributes: {[attr for attr in dir(trial) if not attr.startswith('_')]}")
        if hasattr(trial, 'name'):
            print(f"Trial name: {trial.name}")
        if hasattr(trial, 'trial_id'):
            print(f"Trial ID: {trial.trial_id}")
        if hasattr(trial, 'beam_list') and trial.beam_list:
            print(f"Number of beams: {len(trial.beam_list)}")
        if hasattr(trial, 'dose_grid'):
            print(f"Has dose grid: {trial.dose_grid is not None}")
    
    # Try to get image sets (using plan's primary image set id)
    if hasattr(plan, 'primary_ct_image_set_id') and plan.primary_ct_image_set_id is not None:
        try:
            image_set = reader.get_image_set(institution, patient, plan.primary_ct_image_set_id)
            print(f"\n=== IMAGE_SET to_dict() ===")
            try:
                image_set_dict = image_set.to_dict(include_relationships=False)
                pprint(image_set_dict)
            except TypeError:
                # Fallback if to_dict doesn't support include_relationships parameter
                image_set_dict = image_set.to_dict()
                pprint(image_set_dict)
            
            print(f"\n=== IMAGE_SET fields available ===")
            print(f"ImageSet attributes: {[attr for attr in dir(image_set) if not attr.startswith('_')]}")
        except Exception as e:
            print(f"Could not load image set: {e}")
    
    # Try to get ROIs
    try:
        rois = reader.get_rois(institution, patient, plan)
        if rois and len(rois) > 0:
            roi = rois[0]
            print(f"\n=== ROI to_dict() ===")
            try:
                roi_dict = roi.to_dict(include_relationships=False)
                pprint(roi_dict)
            except TypeError:
                # Fallback if to_dict doesn't support include_relationships parameter
                roi_dict = roi.to_dict()
                pprint(roi_dict)
            
            print(f"\n=== ROI fields available ===")
            print(f"ROI attributes: {[attr for attr in dir(roi) if not attr.startswith('_')]}")
    except Exception as e:
        print(f"Could not load ROIs: {e}")


def test_compare_with_pymedphys_expected_format():
    """Compare pinnacle_io format with what pymedphys expects."""
    # This will be expanded once we understand the format differences
    # For now, just document what we observe
    
    expected_patient_fields = [
        "FirstName", "LastName", "FullName", "DOB", "MRN"
    ]
    
    expected_plan_fields = [
        "PlanName", "PrimaryCTImageSetID", "BeamList"
    ]
    
    expected_trial_fields = [
        "TrialName", "BeamList", "DoseGrid"
    ]
    
    print("\n=== Expected fields for pymedphys ===")
    print(f"Patient fields: {expected_patient_fields}")
    print(f"Plan fields: {expected_plan_fields}")
    print(f"Trial fields: {expected_trial_fields}")


if __name__ == "__main__":
    """Run test manually for development."""
    test_archive = Path(__file__).parent.parent / "test_data" / "archive_01"
    
    if test_archive.exists():
        test_pinnacle_io_to_dict_format(test_archive)
        test_compare_with_pymedphys_expected_format()
    else:
        print(f"Test archive not found at: {test_archive}")
        print("Available test data:")
        test_data_dir = Path(__file__).parent.parent / "test_data"
        if test_data_dir.exists():
            for item in test_data_dir.iterdir():
                print(f"  {item}")
        else:
            print("  No test_data directory found")
        print("Please ensure test data is available before running data mapping analysis.")