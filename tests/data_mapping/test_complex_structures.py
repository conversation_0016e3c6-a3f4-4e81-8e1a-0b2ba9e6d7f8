"""
Test complex nested structures that were marked as unclear in Step 1.

This test investigates the beam lists, dose grids, and contour sequences to verify
if the pinnacle_io to_dict() method properly handles these complex relationships.
"""

import sys
from pathlib import Path

# Add parent directories to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from pinnacle_io.api import PinnacleReader
import pytest
from pprint import pprint


@pytest.fixture
def test_archive_path():
    """Path to test archive."""
    return Path(__file__).parent.parent / "test_data" / "archive_01"


def test_complex_trial_beam_structures(test_archive_path):
    """Test complex beam list structures in trial data."""
    if not test_archive_path.exists():
        pytest.skip(f"Test data not found at {test_archive_path}")
    
    reader = PinnacleReader(test_archive_path)
    institution = reader.get_institution()
    patient_lite = institution.patient_lite_list[0]
    patient = reader.get_patient(institution=institution, patient=patient_lite)
    plan = patient.plan_list[0]
    trials = reader.get_trials(institution, patient, plan)
    
    if not trials:
        pytest.skip("No trials found in test data")
    
    trial = trials[0]
    
    print(f"\n=== TRIAL BEAM LIST ANALYSIS ===")
    print(f"Trial has beam_list attribute: {hasattr(trial, 'beam_list')}")
    
    if hasattr(trial, 'beam_list') and trial.beam_list:
        print(f"Number of beams: {len(trial.beam_list)}")
        
        # Test individual beam access
        beam = trial.beam_list[0]
        print(f"\n=== FIRST BEAM DETAILS ===")
        print(f"Beam attributes: {[attr for attr in dir(beam) if not attr.startswith('_')]}")
        
        # Try to convert beam to dict
        try:
            if hasattr(beam, 'to_dict'):
                beam_dict = beam.to_dict()
                print(f"\n=== BEAM to_dict() SUCCESS ===")
                print(f"Beam dict keys: {list(beam_dict.keys())}")
                print(f"Sample beam data:")
                pprint(dict(list(beam_dict.items())[:10]))  # First 10 items
            else:
                print("Beam does not have to_dict() method")
        except Exception as e:
            print(f"Error calling beam.to_dict(): {e}")
    
    # Test trial to_dict() with beam relationships
    print(f"\n=== TRIAL to_dict() WITH RELATIONSHIPS ===")
    try:
        trial_dict = trial.to_dict()
        
        # Look for beam-related keys
        beam_keys = [key for key in trial_dict.keys() if 'beam' in key.lower()]
        print(f"Beam-related keys in trial dict: {beam_keys}")
        
        for key in beam_keys:
            value = trial_dict[key]
            print(f"{key}: {type(value)} - {len(value) if isinstance(value, (list, dict)) else 'N/A'}")
            
    except Exception as e:
        print(f"Error with trial.to_dict(): {e}")


def test_complex_dose_grid_structures(test_archive_path):
    """Test complex dose grid structures in trial data."""
    if not test_archive_path.exists():
        pytest.skip(f"Test data not found at {test_archive_path}")
    
    reader = PinnacleReader(test_archive_path)
    institution = reader.get_institution()
    patient_lite = institution.patient_lite_list[0]
    patient = reader.get_patient(institution=institution, patient=patient_lite)
    plan = patient.plan_list[0]
    trials = reader.get_trials(institution, patient, plan)
    
    if not trials:
        pytest.skip("No trials found in test data")
    
    trial = trials[0]
    
    print(f"\n=== TRIAL DOSE GRID ANALYSIS ===")
    print(f"Trial has dose_grid attribute: {hasattr(trial, 'dose_grid')}")
    
    if hasattr(trial, 'dose_grid') and trial.dose_grid:
        dose_grid = trial.dose_grid
        print(f"Dose grid type: {type(dose_grid)}")
        print(f"Dose grid attributes: {[attr for attr in dir(dose_grid) if not attr.startswith('_')]}")
        
        # Try to convert dose grid to dict
        try:
            if hasattr(dose_grid, 'to_dict'):
                dose_dict = dose_grid.to_dict()
                print(f"\n=== DOSE GRID to_dict() SUCCESS ===")
                print(f"Dose dict keys: {list(dose_dict.keys())}")
                print(f"Sample dose data:")
                pprint(dict(list(dose_dict.items())[:10]))  # First 10 items
            else:
                print("Dose grid does not have to_dict() method")
        except Exception as e:
            print(f"Error calling dose_grid.to_dict(): {e}")
    
    # Look for dose-related data in trial dict
    print(f"\n=== TRIAL DOSE-RELATED DATA ===")
    try:
        trial_dict = trial.to_dict()
        
        # Look for dose-related keys
        dose_keys = [key for key in trial_dict.keys() if 'dose' in key.lower()]
        print(f"Dose-related keys in trial dict: {dose_keys}")
        
        for key in dose_keys:
            value = trial_dict[key]
            print(f"{key}: {type(value)} - {value if not isinstance(value, (list, dict)) else f'Length: {len(value)}'}")
            
    except Exception as e:
        print(f"Error with trial.to_dict(): {e}")


def test_complex_roi_contour_structures(test_archive_path):
    """Test complex contour structures in ROI data."""
    if not test_archive_path.exists():
        pytest.skip(f"Test data not found at {test_archive_path}")
    
    reader = PinnacleReader(test_archive_path)
    institution = reader.get_institution()
    patient_lite = institution.patient_lite_list[0]
    patient = reader.get_patient(institution=institution, patient=patient_lite)
    plan = patient.plan_list[0]
    
    try:
        rois = reader.get_rois(institution, patient, plan)
        if not rois:
            pytest.skip("No ROIs found in test data")
        
        roi = rois[0]
        
        print(f"\n=== ROI CONTOUR ANALYSIS ===")
        print(f"ROI has curve_list attribute: {hasattr(roi, 'curve_list')}")
        
        if hasattr(roi, 'curve_list') and roi.curve_list:
            print(f"Number of curves: {len(roi.curve_list)}")
            
            # Test individual curve access
            curve = roi.curve_list[0]
            print(f"Curve type: {type(curve)}")
            print(f"Curve attributes: {[attr for attr in dir(curve) if not attr.startswith('_')]}")
            
            # Try to convert curve to dict
            try:
                if hasattr(curve, 'to_dict'):
                    curve_dict = curve.to_dict()
                    print(f"\n=== CURVE to_dict() SUCCESS ===")
                    print(f"Curve dict keys: {list(curve_dict.keys())}")
                    print(f"Sample curve data:")
                    pprint(dict(list(curve_dict.items())[:10]))  # First 10 items
                else:
                    print("Curve does not have to_dict() method")
            except Exception as e:
                print(f"Error calling curve.to_dict(): {e}")
        
        # Test ROI to_dict() with curve relationships
        print(f"\n=== ROI to_dict() WITH RELATIONSHIPS ===")
        try:
            roi_dict = roi.to_dict()
            
            # Look for curve/contour-related keys
            contour_keys = [key for key in roi_dict.keys() if any(word in key.lower() for word in ['curve', 'contour', 'point'])]
            print(f"Contour-related keys in ROI dict: {contour_keys}")
            
            for key in contour_keys:
                value = roi_dict[key]
                print(f"{key}: {type(value)} - {len(value) if isinstance(value, (list, dict)) else 'N/A'}")
                
        except Exception as e:
            print(f"Error with roi.to_dict(): {e}")
    
    except Exception as e:
        print(f"Error loading ROIs: {e}")


def test_pymedphys_expected_structure_comparison():
    """Compare what we found vs what pymedphys expects."""
    
    print(f"\n=== PYMEDPHYS EXPECTED STRUCTURES ===")
    
    # Based on pymedphys documentation, these are the expected structures
    expected_beam_structure = {
        "BeamList": [
            {
                "Name": "beam_name",
                "Number": 1,
                "MachineType": "machine",
                "Energy": 6.0,
                "DoseRate": 600,
                "Gantry": 0.0,
                "Collimator": 0.0,
                "Couch": 0.0,
                # ... more beam parameters
            }
        ]
    }
    
    expected_dose_structure = {
        "DoseGrid": {
            "Dimensions": [100, 100, 50],
            "Spacing": [0.25, 0.25, 0.25],
            "Origin": [-12.5, -12.5, -6.25],
            "DoseArray": "numpy_array_or_binary_data"
        }
    }
    
    expected_contour_structure = {
        "ContourSequence": [
            {
                "ContourData": [[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]],  # List of [x,y,z] points
                "NumberOfContourPoints": 2,
                "ContourGeometricType": "CLOSED_PLANAR"
            }
        ]
    }
    
    print("Expected beam structure:")
    pprint(expected_beam_structure)
    print("\nExpected dose structure:")
    pprint(expected_dose_structure)
    print("\nExpected contour structure:")
    pprint(expected_contour_structure)


if __name__ == "__main__":
    """Run tests manually for development."""
    test_archive = Path(__file__).parent.parent / "test_data" / "archive_01"
    
    if test_archive.exists():
        print("Testing complex nested structures...")
        test_complex_trial_beam_structures(test_archive)
        test_complex_dose_grid_structures(test_archive)
        test_complex_roi_contour_structures(test_archive)
        test_pymedphys_expected_structure_comparison()
        print("\nComplex structure testing completed!")
    else:
        print(f"Test archive not found at: {test_archive}")
        print("Please ensure test data is available before running complex structure tests.")