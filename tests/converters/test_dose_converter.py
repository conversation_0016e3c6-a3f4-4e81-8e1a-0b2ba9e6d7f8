import os
import glob
import pytest
import subprocess
import sys
import numpy as np
from dicom_converter.converters.dose_converter import DoseConverter
from pinnacle_io.models import Patient, Plan, Dose, Trial, DoseGrid

from pinnacle_io.models.types import VoxelSize, VolumeSize, Coordinate
from pinnacle_io.models.patient_setup import PatientSetupEnum, PatientSetup


class DummyPlanConverter:
    sop_instance_uid = "1.2.3.4.5.6.7.8.9"


def make_dummy_patient():
    patient = Patient(patient_id=12345, first_name="<PERSON>", last_name="<PERSON>e")
    return patient


def make_dummy_plan():
    plan = Plan(plan_id=1, name="Test Plan")
    plan.primary_ct_image_set_id = 0
    return plan


def make_dummy_dose():
    dose = Dose(
        id=0,
        dose_type="PHYSICAL",
        dose_unit="CGY",
    )
    dose.pixel_data = np.ones((2, 2, 2))
    return dose


def make_dummy_trial():
    # Minimal dose grid with dimensions
    dose_grid = DoseGrid(
        voxel_size=VoxelSize(x=1.0, y=1.0, z=1.0),
        dimension=VolumeSize(x=2, y=2, z=2),
        origin=Coordinate(x=0.0, y=0.0, z=0.0),
        vol_rot_delta=Coordinate(x=0.0, y=0.0, z=0.0),
        display_2d=0,
        dose_summation_type=0,
    )
    # Minimal patient position
    patient_position = PatientSetup(patient_setup=PatientSetupEnum.HFS)
    # Create the trial
    trial = Trial()
    trial.name = "DummyTrial"
    trial.dose_grid = dose_grid
    trial._patient_position = patient_position
    trial.prescription_list = []
    trial.beam_list = []
    return trial


class TestDoseConverter:
    @pytest.fixture
    def dose_converter(self, tmp_path):
        # Setup test directories
        patient_folder = "Patient_1"

        # Create directory structure
        patient_path = tmp_path / patient_folder
        os.makedirs(patient_path, exist_ok=True)

        # Create temporary "Patient" file
        patient_file = patient_path / "Patient"
        with open(patient_file, "w") as f:
            f.write("PatientID = 12345;\n")
            f.write('LastName = "DOE";\n')
            f.write('FirstName = "JOHN";\n')

        # Initialize converter with patient path
        converter = DoseConverter.from_archive(patient_path=str(patient_path))
        return converter

    def test_dose_converter_initialization(self, dose_converter):
        """
        Test the initialization of the DoseConverter.

        Verifies that the DoseConverter is correctly initialized with a patient
        and checks that the patient's last name is "DOE".
        """
        assert dose_converter.patient is not None
        assert dose_converter.patient.last_name == "DOE"

    def test_dose_converter_for_test_patient(self):
        """
        Integration test for DoseConverter using real patient data.
        Uses tests/test_data/archive_01/Institution_1/Mount_0/Patient_1 as input,
        Plan_0 as the plan folder, and outputs to Patient_1/Dicom.
        Deletes any existing RT Dose DICOM files in the output folder before running.
        """
        # Skip this test if test data doesn't exist
        test_data_path = os.path.abspath(
            os.path.join(
                os.path.dirname(__file__),
                "..",
                "test_data",
                "archive_01",
                "Institution_1",
                "Mount_0",
                "Patient_1",
            )
        )
        if not os.path.exists(test_data_path):
            pytest.skip("Test data not available")

        patient_path = test_data_path
        plan_folder = "Plan_0"
        plan_path = os.path.join(patient_path, plan_folder)
        output_path = os.path.join(patient_path, "Dicom", plan_folder)

        # Remove existing RT Dose DICOM files
        if os.path.exists(output_path):
            for f in glob.glob(os.path.join(output_path, "RD*.dcm")):
                os.remove(f)

        # Instantiate DoseConverter
        dose_converter = DoseConverter.from_archive(patient_path)

        # Load trials
        trials = dose_converter.load_trials(plan_path)
        assert len(trials) > 0

        # Get the first trial
        trial = trials[0]

        # Load dose
        dose = dose_converter.load_dose(plan_path, trial)

        # Perform dose conversion
        dose_dataset = dose_converter.convert(trial, dose)

        # Save the dose files
        output_file = dose_converter.save_dataset(dose_dataset, output_path)

        # Assert at least one RT Dose DICOM file was created
        assert os.path.exists(output_file), f"Output file does not exist: {output_file}"

    def test_dose_converter_from_models(self, tmp_path):
        """
        Test the DoseConverter using models loaded from trial, plan, and patient.
        """
        # Create test models
        patient = make_dummy_patient()
        patient.patient_path = str(tmp_path)
        plan = make_dummy_plan()
        dose = make_dummy_dose()
        trial = make_dummy_trial()

        # Ensure plan directory exists
        plan_path = tmp_path / plan.plan_folder
        os.makedirs(plan_path, exist_ok=True)
        output_path = tmp_path / "output"
        os.makedirs(output_path, exist_ok=True)

        # Initialize converter with models
        converter = DoseConverter(patient)

        # Perform dose conversion
        dose_dataset = converter.convert(trial, dose)

        # Basic verification
        assert dose_dataset.Modality == "RTDOSE"
        assert hasattr(dose_dataset, "DoseUnits")
        assert dose_dataset.file_meta.MediaStorageSOPClassUID.name == "RT Dose Storage"

    def test_dose_converter_command_line_interface(self):
        """
        Test the command line interface for the DoseConverter.
        """
        patient_path = os.path.abspath(
            os.path.join(os.path.dirname(__file__), "..", "test_data", "archive_01", "Institution_1", "Mount_0", "Patient_1")
        )
        plan_folder = "Plan_0"
        plan_path = os.path.join(patient_path, plan_folder)
        trial_index = 0
        output_path = os.path.join(patient_path, "Dicom", plan_folder, f"Trial_{trial_index}")

        # Remove existing RT Dose DICOM files
        if os.path.exists(output_path):
            for f in glob.glob(os.path.join(output_path, "RD*.dcm")):
                os.remove(f)

        # Define the command to run the CLI
        command = [
            sys.executable,
            "-m",
            "dicom_converter.converters.dose_converter",
            "--plan-path",
            plan_path,
            "--trial-index",
            str(trial_index),
            "--output-path",
            output_path,
        ]

        # Run the command and capture output
        result = subprocess.run(command, capture_output=True, text=True)

        # Check if the command was successful
        assert result.returncode == 0, f"Command failed: {result.stderr}"
        assert len(glob.glob(os.path.join(output_path, "RD*.dcm"))) == 1, "Output DICOM file not found in expected location"
