import os
import glob
import pytest
from dicom_converter.converters.structure_converter import StructureConverter
from pinnacle_io.models import Patient, Plan, ImageSet, ROI, Point


def make_dummy_patient():
    patient = Patient(patient_id=1212345, first_name="<PERSON><PERSON><PERSON>", last_name="DOE")
    patient.patient_position = "HFS"
    return patient


def make_dummy_plan():
    plan = Plan(plan_id=1, name="Test Plan")
    plan.primary_ct_image_set_id = 0
    return plan


def make_dummy_image_set():
    image_set = ImageSet()
    image_set.x_dim = 128
    image_set.y_dim = 128
    image_set.z_dim = 64
    image_set.x_pixdim = 0.25
    image_set.y_pixdim = 0.25
    image_set.z_pixdim = 0.5
    image_set.x_start = 0.0
    image_set.y_start = 0.0
    image_set.z_start = 0.0
    image_set.series_uid = "1.2.3.4.5.6"
    return image_set


def make_dummy_structure_set():
    roi_list = []
    return roi_list


def make_dummy_point_set():
    poi_list = []
    return poi_list


class TestStructureConverter:
    @pytest.fixture
    def structure_converter(self, tmp_path):
        # Setup test directories
        patient_folder = "Patient_1"
        patient_path = tmp_path / patient_folder
        os.makedirs(patient_path, exist_ok=True)

        # Create temporary "Patient" file
        patient_file = patient_path / "Patient"
        with open(patient_file, "w") as f:
            f.write("PatientID = 12345;\n")
            f.write('LastName = "DOE";\n')
            f.write('FirstName = "JOHN";\n')

        # Initialize converter with patient path
        converter = StructureConverter.from_archive(patient_path=str(patient_path))
        return converter

    def test_structure_converter_initialization(self, structure_converter):
        assert structure_converter.patient is not None
        assert structure_converter.patient.last_name == "DOE"

    def test_structure_converter_for_test_patient(self, tmp_path):
        """
        Integration test for StructureConverter using real patient data.
        Uses tests/test_data/archive_01/Institution_1/Mount_0/Patient_1 as input,
        Plan_0 as the plan folder, and outputs to Patient_1/Dicom.
        Deletes any existing RT Structure DICOM files in the output folder before running.
        """
        # Skip this test if test data doesn't exist
        test_data_path = os.path.abspath(
            os.path.join(os.path.dirname(__file__), "..", "test_data", "archive_01", "Institution_1", "Mount_0", "Patient_1")
        )
        if not os.path.exists(test_data_path):
            pytest.skip("Test data not available")

        patient_path = test_data_path
        plan_folder = "Plan_0"
        plan_path = os.path.join(patient_path, plan_folder)
        output_path = os.path.join(patient_path, "Dicom", plan_folder)

        # Remove existing RT Structure DICOM files
        if os.path.exists(output_path):
            for f in glob.glob(os.path.join(output_path, "RS*.dcm")):
                os.remove(f)

        # Instantiate StructureConverter
        structure_converter = StructureConverter.from_archive(patient_path)

        # Load structure data
        structure_set = structure_converter.load_structure_set(plan_path)
        point_set = structure_converter.load_point_set(plan_path)
        planning_ct = structure_converter.load_planning_ct(plan_path)

        # Perform structure conversion
        structure_dataset = structure_converter.convert(structure_set, point_set, planning_ct)

        # Save the structure files
        output_file = structure_converter.save_dataset(structure_dataset, output_path)

        # Assert at least one RT Structure DICOM file was created
        assert os.path.exists(output_file), f"Output file does not exist: {output_file}"

    def test_structure_converter_from_models(self, tmp_path):
        # Create test models
        patient = make_dummy_patient()
        patient.patient_path = str(tmp_path)
        plan = make_dummy_plan()
        image_set = make_dummy_image_set()
        structure_set = make_dummy_structure_set()
        point_set = make_dummy_point_set()

        # Ensure plan directory exists
        plan_folder = f"Plan_{plan.plan_id if plan.plan_id is not None else 0}"
        plan_path = tmp_path / plan_folder
        os.makedirs(plan_path, exist_ok=True)

        # Initialize converter with models
        converter = StructureConverter(patient)

        # Convert and check results
        structure_dataset = converter.convert(structure_set, point_set, image_set)
        assert structure_dataset.Modality == "RTSTRUCT"
        assert hasattr(structure_dataset, "StructureSetLabel")
        assert structure_dataset.file_meta.MediaStorageSOPClassUID.name == "RT Structure Set Storage"

    def test_structure_converter_command_line_interface(self):
        """
        Test the command line interface (CLI) of the structure converter.
        """
        import subprocess
        import sys

        patient_path = os.path.abspath(
            os.path.join(os.path.dirname(__file__), "..", "test_data", "archive_01", "Institution_1", "Mount_0", "Patient_1")
        )
        plan_path = os.path.join(patient_path, "Plan_0")
        output_path = os.path.join(patient_path, "Dicom", "Plan_0", "Structures")

        # Remove existing RT Structure DICOM files
        if os.path.exists(output_path):
            for f in glob.glob(os.path.join(output_path, "RS*.dcm")):
                os.remove(f)

        # Define the command to run the CLI
        command = [
            sys.executable,
            "-m",
            "dicom_converter.converters.structure_converter",
            "--plan-path",
            plan_path,
            "--output-path",
            output_path,
        ]

        # Run the command and capture output
        result = subprocess.run(command, capture_output=True, text=True)

        # Check if the command was successful
        assert result.returncode == 0, f"Command failed: {result.stderr}"
        assert len(glob.glob(os.path.join(output_path, "RS*.dcm"))) == 1, "Output DICOM file not found in expected location"
