import os
import sys
import glob
import pytest
import subprocess
import numpy as np
from dicom_converter.converters.image_converter import ImageConverter
from pinnacle_io.models import Patient, Plan, ImageSet, ImageInfo


def make_dummy_patient():
    patient = Patient(
        patient_id=12345,
        first_name="<PERSON><PERSON><PERSON>",
        last_name="DOE"
    )
    patient.patient_position = "HFS"
    patient.patient_path = "."
    return patient


def make_dummy_plan():
    plan = Plan(
        plan_id=1,
        name="Test Plan"
    )
    plan.primary_ct_image_set_id = 0
    return plan


def make_dummy_image_set():
    """
    Create a dummy ImageSet for testing purposes.

    Returns:
        ImageSet: A dummy image set with test values.
    """
    image_set = ImageSet()
    image_set.id = 0
    image_set.series_uid = "1.2.3.4.5.6"
    image_set.study_uid = "1.2.3.4.5"
    image_set.series_number = 1
    image_set.acquisition_number = 1

    image_set.patient_id = 12345
    image_set.image_name = "Test CT"
    image_set.modality = "CT"
    image_set.number_of_images = 10
    image_set.patient_position = "HFS"

    # Dimensions
    image_set.x_dim = 128
    image_set.y_dim = 128
    image_set.z_dim = 10

    # Pixel dimensions
    image_set.x_pixdim = 0.25
    image_set.y_pixdim = 0.25
    image_set.z_pixdim = 0.5

    # Start positions
    image_set.x_start = -16.0
    image_set.y_start = -16.0
    image_set.z_start = -2.5

    # Create dummy pixel data (optional)
    image_set.pixel_data = np.zeros((image_set.z_dim, image_set.y_dim, image_set.x_dim), dtype=np.int16)

    # Create image info list
    image_set.image_info_list = []
    for i in range(image_set.number_of_images):
        image_info = ImageInfo(
            table_position=image_set.z_start + i * image_set.z_pixdim,
            couch_pos=0.0,
            slice_number=i,
            series_uid=image_set.series_uid,
            study_instance_uid=image_set.study_uid,
            frame_uid="1.2.3.4.5.6.7",
            class_uid="1.2.840.10008.5.1.4.1.1.2",  # CT Image Storage
            instance_uid=f"1.2.3.4.5.6.{i+1}",
            suv_scale=1.0,
            color_lut_scale=1.0,
            dicom_file_name=f"CT.{i+1}.dcm",
            acquisition_time="",
            image_time="",
        )
        image_set.image_info_list.append(image_info)

    return image_set


class TestImageConverter:
    @pytest.fixture
    def image_converter(self, tmp_path):
        # Setup test directories
        patient_folder = "Patient_1"
        patient_path = tmp_path / patient_folder
        os.makedirs(patient_path, exist_ok=True)

        # Create temporary "Patient" file
        patient_file = patient_path / "Patient"
        with open(patient_file, "w") as f:
            f.write("PatientID = 12345;\n")
            f.write('LastName = "DOE";\n')
            f.write('FirstName = "JOHN";\n')

        # Initialize converter with patient path
        converter = ImageConverter.from_archive(patient_path=str(patient_path))
        return converter

    def test_image_converter_initialization(self, image_converter):
        """
        Test initialization of ImageConverter class.

        Verifies that:
        - The patient object is properly initialized and not None
        - The patient last name is correctly set to "DOE"

        Args:
            plan_converter: Fixture providing initialized PlanConverter instance

        Returns:
            None
        """
        assert image_converter.patient is not None
        assert image_converter.patient.last_name == "DOE"

    def test_image_converter_for_test_patient(self, tmp_path):
        """
        Integration test for ImageConverter using test patient data.

        Tests:
        - Loading patient data from test archive
        - Converting plan data to DICOM RT Plan format
        - Saving DICOM file to output directory

        Uses:
        - Input: tests/test_data/archive_01/Institution_1/Mount_0/Patient_1
        - Plan: Plan_0
        - Output: Patient_1/Dicom/Plan_0

        Any existing RT Plan DICOM files in output folder are deleted before test.

        Args:
            tmp_path: Temporary path fixture from pytest

        Raises:
            AssertionError: If test data is missing or conversion fails
        """
        test_data_path = os.path.abspath(
            os.path.join(os.path.dirname(__file__), "..", "test_data", "archive_01", "Institution_1", "Mount_0", "Patient_1")
        )
        assert os.path.exists(test_data_path), "Test data not available"

        patient_path = test_data_path
        plan_folder = "Plan_0"
        plan_path = os.path.join(patient_path, plan_folder)

        image_converter = ImageConverter.from_archive(patient_path)
        image_set = image_converter.load_planning_ct(plan_path)

        image_datasets = image_converter.convert(image_set)
        assert len(image_datasets) > 0, "No image datasets converted"

    def test_image_converter_from_models(self, tmp_path):
        """Tests the conversion of image models to DICOM CT datasets.

        Tests the PlanConverter class functionality by creating dummy patient, plan and trial models,
        setting required UIDs, and verifying the converted DICOM RT Plan dataset contains all
        required attributes and sequences according to DICOM standards.

        Args:
            tmp_path (Path): Pytest fixture providing temporary directory path for test files

        Tests:
            - Creation of valid DICOM RT Plan dataset
            - Presence of required DICOM attributes and sequences:
        """
        # Create test models
        patient = make_dummy_patient()
        patient.patient_path = str(tmp_path)
        plan = make_dummy_plan()
        plan_path = tmp_path / plan.plan_folder
        os.makedirs(plan_path, exist_ok=True)
        converter = ImageConverter(patient)

        # Set required UIDs for DICOM dataset
        image_set = make_dummy_image_set()
        image_datasets = converter.convert(image_set)

        # Expanded asserts for DICOM CT dataset
        assert len(image_datasets) > 0, "No image datasets converted"
        assert image_datasets[0].Modality == "CT"

    def test_image_converter_command_line_interface(self):
        """Test the command line interface functionality of the image converter.
        This test verifies that the image converter can be successfully executed via command line,
        converting a Pinnacle image to DICOM format. It checks:
        1. The conversion process completes without errors
        2. The output DICOM CT file is created in the expected location
        Args: None
        Returns: None
        Raises:
            AssertionError: If the command execution fails or if the output DICOM file is not found
        """
        patient_path = os.path.abspath(
            os.path.join(os.path.dirname(__file__), "..", "test_data", "archive_01", "Institution_1", "Mount_0", "Patient_1")
        )
        plan_path = os.path.join(patient_path, "Plan_0")
        trial_index = 0
        output_path = os.path.join(patient_path, "Dicom", "Plan_0", f"Trial_{trial_index}")

        # Remove existing RT Plan DICOM files
        if os.path.exists(output_path):
            for f in glob.glob(os.path.join(output_path, "CT*.dcm")):
                os.remove(f)

        # Define the command to run the CLI
        command = [
            sys.executable,
            "-m",
            "dicom_converter.converters.image_converter",
            "--patient-path",
            patient_path,
            "--plan-path",
            plan_path,
            "--output-path",
            output_path,
        ]

        # Run the command and capture output
        result = subprocess.run(command, capture_output=True, text=True)

        # Check if the command was successful
        assert result.returncode == 0, f"Command failed: {result.stderr}"
        assert len(glob.glob(os.path.join(output_path, "CT*.dcm"))) > 0, "Output DICOM file not found in expected location"
