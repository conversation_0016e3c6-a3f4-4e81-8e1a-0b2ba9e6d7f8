// Region of Interest file
// Data set: LAST^FIRST^M
// File created: Wed Jan 01 10:00:00 2020

//
// Pinnacle Treatment Planning System Version 16.0 
// 16.0 
//

 file_stamp={
       write_date: Wed Jan 01 10:00:00 2020
    write_program: Pinnacle Treatment Planning System
    write_version: 16.0
  }; // End of file_stamp
//-----------------------------------------------------
//  Beginning of ROI: bb
//-----------------------------------------------------

 roi={
           name: bb
    volume_name: LAST^FIRST^M
stats_volume_name: LAST^FIRST^M
           author:   
           organ_name:   
           flags =          135168;
           roiinterpretedtype: ORGAN
           color:           red
           box_size =       5;
           line_2d_width =  2;
           line_3d_width =  1;
           paint_brush_radius =  0.4;
           paint_allow_curve_closing = 1;
           curve_min_area =  0.1;
           curve_overlap_min =  88;
           lower =          800;
           upper =          4096;
           radius =         0;
           density =        0;
           density_units:   g/cm^3
           override_data =  1;
           override_order =  1;
           override_material =  0;
           material:        None
           invert_density_loading =  0;
           volume =         0.953669;
           pixel_min =      0;
           pixel_max =      4071;
           pixel_mean =     750.617;
           pixel_std =      1072.4;
           bBEVDRROutline = 0;
           display_on_other_vols = 1;
           is_linked =      0;
           auto_update_contours =  0;
           UID: 1.3.46.670589.13.570038639.20200101090731.769536
           stoppingpower =        0;
           ctnumber =        0;
           is_created_by_atlasseg = 0;
           is_created_by_autoplanengine = 0;
           num_curve = 8;
//----------------------------------------------------
//  ROI: bb
//  Curve 1 of 8
//----------------------------------------------------
               curve={
                       flags =       131092;
                       block_size =  32;
                       num_points =  25;
points={
-1.07428 11.914 2
-0.976625 11.914 2
-0.878969 11.914 2
-0.781313 11.8163 2
-0.683657 11.8163 2
-0.683657 11.7187 2
-0.586 11.621 2
-0.586 11.5234 2
-0.586 11.4257 2
-0.683657 11.3281 2
-0.683657 11.2304 2
-0.781313 11.2304 2
-0.878969 11.1327 2
-0.976625 11.1327 2
-1.07428 11.1327 2
-1.17194 11.2304 2
-1.26959 11.2304 2
-1.26959 11.3281 2
-1.36725 11.4257 2
-1.36725 11.5234 2
-1.36725 11.621 2
-1.26959 11.7187 2
-1.26959 11.8163 2
-1.17194 11.8163 2
-1.07428 11.914 2
 };  // End of points for curve 1
}; // End of curve 1
//----------------------------------------------------
//  ROI: bb
//  Curve 2 of 8
//----------------------------------------------------
               curve={
                       flags =       131092;
                       block_size =  32;
                       num_points =  25;
points={
-10.0586 2.14839 2
-9.96098 2.14839 2
-9.86332 2.14839 2
-9.76567 2.05073 2
-9.66801 2.05073 2
-9.66801 1.95308 2
-9.57035 1.85542 2
-9.57035 1.75777 2
-9.57035 1.66011 2
-9.66801 1.56245 2
-9.66801 1.4648 2
-9.76567 1.4648 2
-9.86332 1.36714 2
-9.96098 1.36714 2
-10.0586 1.36714 2
-10.1563 1.4648 2
-10.2539 1.4648 2
-10.2539 1.56245 2
-10.3516 1.66011 2
-10.3516 1.75777 2
-10.3516 1.85542 2
-10.2539 1.95308 2
-10.2539 2.05073 2
-10.1563 2.05073 2
-10.0586 2.14839 2
 };  // End of points for curve 2
}; // End of curve 2
//----------------------------------------------------
//  ROI: bb
//  Curve 3 of 8
//----------------------------------------------------
               curve={
                       flags =       131092;
                       block_size =  32;
                       num_points =  25;
points={
7.12882 2.14839 2
7.22648 2.14839 2
7.32413 2.14839 2
7.42179 2.05073 2
7.51945 2.05073 2
7.51945 1.95308 2
7.6171 1.85542 2
7.6171 1.75777 2
7.6171 1.66011 2
7.51945 1.56245 2
7.51945 1.4648 2
7.42179 1.4648 2
7.32413 1.36714 2
7.22648 1.36714 2
7.12882 1.36714 2
7.03117 1.4648 2
6.93351 1.4648 2
6.93351 1.56245 2
6.83586 1.66011 2
6.83586 1.75777 2
6.83586 1.85542 2
6.93351 1.95308 2
6.93351 2.05073 2
7.03117 2.05073 2
7.12882 2.14839 2
 };  // End of points for curve 3
}; // End of curve 3
//----------------------------------------------------
//  ROI: bb
//  Curve 4 of 8
//----------------------------------------------------
               curve={
                       flags =       131092;
                       block_size =  32;
                       num_points =  25;
points={
-1.17194 12.0116 1.75
-1.07428 12.0116 1.75
-0.976625 12.0116 1.75
-0.878969 11.914 1.75
-0.781313 11.914 1.75
-0.781313 11.8163 1.75
-0.683657 11.7187 1.75
-0.683657 11.621 1.75
-0.683657 11.5234 1.75
-0.781313 11.4257 1.75
-0.781313 11.3281 1.75
-0.878969 11.3281 1.75
-0.976625 11.2304 1.75
-1.07428 11.2304 1.75
-1.17194 11.2304 1.75
-1.26959 11.3281 1.75
-1.36725 11.3281 1.75
-1.36725 11.4257 1.75
-1.4649 11.5234 1.75
-1.4649 11.621 1.75
-1.4649 11.7187 1.75
-1.36725 11.8163 1.75
-1.36725 11.914 1.75
-1.26959 11.914 1.75
-1.17194 12.0116 1.75
 };  // End of points for curve 4
}; // End of curve 4
//----------------------------------------------------
//  ROI: bb
//  Curve 5 of 8
//----------------------------------------------------
               curve={
                       flags =       131092;
                       block_size =  32;
                       num_points =  27;
points={
7.12882 2.3437 1.75
7.22648 2.3437 1.75
7.32413 2.3437 1.75
7.42179 2.24605 1.75
7.51945 2.24605 1.75
7.51945 2.14839 1.75
7.6171 2.05073 1.75
7.6171 1.95308 1.75
7.6171 1.85542 1.75
7.6171 1.75777 1.75
7.51945 1.66011 1.75
7.51945 1.56245 1.75
7.42179 1.56245 1.75
7.32413 1.4648 1.75
7.22648 1.4648 1.75
7.12882 1.4648 1.75
7.03117 1.56245 1.75
6.93351 1.56245 1.75
6.93351 1.66011 1.75
6.83586 1.75777 1.75
6.83586 1.85542 1.75
6.83586 1.95308 1.75
6.83586 2.05073 1.75
6.93351 2.14839 1.75
6.93351 2.24605 1.75
7.03117 2.24605 1.75
7.12882 2.3437 1.75
 };  // End of points for curve 5
}; // End of curve 5
//----------------------------------------------------
//  ROI: bb
//  Curve 6 of 8
//----------------------------------------------------
               curve={
                       flags =       131092;
                       block_size =  32;
                       num_points =  25;
points={
-10.2539 2.14839 1.75
-10.1563 2.14839 1.75
-10.0586 2.14839 1.75
-9.96098 2.05073 1.75
-9.86332 2.05073 1.75
-9.86332 1.95308 1.75
-9.76567 1.85542 1.75
-9.76567 1.75777 1.75
-9.76567 1.66011 1.75
-9.86332 1.56245 1.75
-9.86332 1.4648 1.75
-9.96098 1.4648 1.75
-10.0586 1.36714 1.75
-10.1563 1.36714 1.75
-10.2539 1.36714 1.75
-10.3516 1.4648 1.75
-10.4493 1.4648 1.75
-10.4493 1.56245 1.75
-10.5469 1.66011 1.75
-10.5469 1.75777 1.75
-10.5469 1.85542 1.75
-10.4493 1.95308 1.75
-10.4493 2.05073 1.75
-10.3516 2.05073 1.75
-10.2539 2.14839 1.75
 };  // End of points for curve 6
}; // End of curve 6
//----------------------------------------------------
//  ROI: bb
//  Curve 7 of 8
//----------------------------------------------------
               curve={
                       flags =       131092;
                       block_size =  32;
                       num_points =  25;
points={
-1.26959 11.914 2.25
-1.17194 11.914 2.25
-1.07428 11.914 2.25
-0.976625 11.8163 2.25
-0.878969 11.8163 2.25
-0.878969 11.7187 2.25
-0.781313 11.621 2.25
-0.781313 11.5234 2.25
-0.781313 11.4257 2.25
-0.878969 11.3281 2.25
-0.878969 11.2304 2.25
-0.976625 11.2304 2.25
-1.07428 11.1327 2.25
-1.17194 11.1327 2.25
-1.26959 11.1327 2.25
-1.36725 11.2304 2.25
-1.4649 11.2304 2.25
-1.4649 11.3281 2.25
-1.56256 11.4257 2.25
-1.56256 11.5234 2.25
-1.56256 11.621 2.25
-1.4649 11.7187 2.25
-1.4649 11.8163 2.25
-1.36725 11.8163 2.25
-1.26959 11.914 2.25
 };  // End of points for curve 7
}; // End of curve 7
//----------------------------------------------------
//  ROI: bb
//  Curve 8 of 8
//----------------------------------------------------
               curve={
                       flags =       131092;
                       block_size =  32;
                       num_points =  25;
points={
-10.1563 2.14839 2.25
-10.0586 2.14839 2.25
-9.96098 2.14839 2.25
-9.86332 2.05073 2.25
-9.76567 2.05073 2.25
-9.76567 1.95308 2.25
-9.66801 1.85542 2.25
-9.66801 1.75777 2.25
-9.66801 1.66011 2.25
-9.76567 1.56245 2.25
-9.76567 1.4648 2.25
-9.86332 1.4648 2.25
-9.96098 1.36714 2.25
-10.0586 1.36714 2.25
-10.1563 1.36714 2.25
-10.2539 1.4648 2.25
-10.3516 1.4648 2.25
-10.3516 1.56245 2.25
-10.4493 1.66011 2.25
-10.4493 1.75777 2.25
-10.4493 1.85542 2.25
-10.3516 1.95308 2.25
-10.3516 2.05073 2.25
-10.2539 2.05073 2.25
-10.1563 2.14839 2.25
 };  // End of points for curve 8
}; // End of curve 8
               surface_mesh={
                       allow_adaptation = 1;
                       replace_mean_mesh = 0;
                       auto_clean_contours = 1;
                       auto_smooth_mesh = 1;
                       internal_weight = -3.000000;
                       surface_weight = 1.000000;
                       distance_weight = 2.000000;
                       profile_length = 10;
                       limit_feature_search_string: Full
                       pixel_size = 1.000000;
                       feature_weight = 0.010000;
                       lower_CT_bound = 0.000000;
                       upper_CT_bound = 4095.000000;
                       gradient_clipping: Soft
                       gradient_direction: Unsigned
                       maximum_gradient = 100.000000;
                       avoid_CT_range: None
                       avoid_CT_threshold = 4095.000000;
                       number_of_modes = 0;
                       number_of_vertices = 0;
                       number_of_triangles = 0;
                       max_iterations = 10;
                       smooth_between_iterations = 0;
                       repair_between_iterations = 1;
                       translation_increment_cm = 0.500000;
                       rotation_increment = 20.000000;
                       magnify_increment = 0.200000;
                       sphere_tool_radius = 1.500000;
                       gaussian_tool_radius = 3.000000;
                       vertex_drag_curvature = 0.700000;
                       vertex_drag_neighbor_layers = 3;
                       vertices={
 };  // End of vertices for surface mesh
                       triangles={
 };  // End of triangles for surface mesh
}; // End of surface_mesh 
               mean_mesh={
                       samples = 1;
                       number_of_vertices = 0;
                       number_of_triangles = 0;
                       vertices={
 };  // End of vertices for mean mesh
                       triangles={
 };  // End of triangles for mean mesh
}; // End of mean_mesh 
        }; // End of ROI bb
//-----------------------------------------------------
//  Beginning of ROI: ROI_1
//-----------------------------------------------------

 roi={
           name: ROI_1
    volume_name: LAST^FIRST^M
stats_volume_name: LAST^FIRST^M
           author:   
           organ_name:   
           flags =          135168;
           roiinterpretedtype: ORGAN
           color:           green
           box_size =       5;
           line_2d_width =  2;
           line_3d_width =  1;
           paint_brush_radius =  0.4;
           paint_allow_curve_closing = 1;
           curve_min_area =  0.1;
           curve_overlap_min =  88;
           lower =          800;
           upper =          4096;
           radius =         0;
           density =        1;
           density_units:   g/cm^3
           override_data =  0;
           override_order =  0;
           override_material =  0;
           material:        None
           invert_density_loading =  0;
           volume =         0.0727173;
           pixel_min =      1011;
           pixel_max =      1065;
           pixel_mean =     1036.31;
           pixel_std =      13.8085;
           bBEVDRROutline = 0;
           display_on_other_vols = 1;
           is_linked =      0;
           auto_update_contours =  0;
           UID: 1.3.46.670589.13.570038639.20200101090804.897389
           stoppingpower =        0;
           ctnumber =        0;
           is_created_by_atlasseg = 0;
           is_created_by_autoplanengine = 0;
           num_curve = 2;
//----------------------------------------------------
//  ROI: ROI_1
//  Curve 1 of 2
//----------------------------------------------------
               curve={
                       flags =       20;
                       block_size =  32;
                       num_points =  51;
points={
-5.57238 9.63713 1.75
-5.60404 9.63713 1.75
-5.6357 9.63713 1.75
-5.66736 9.63713 1.75
-5.69902 9.63713 1.75
-5.73068 9.63713 1.75
-5.73068 9.60548 1.75
-5.76234 9.60548 1.75
-5.76234 9.57383 1.75
-5.794 9.57383 1.75
-5.82566 9.57383 1.75
-5.85732 9.54218 1.75
-5.88898 9.54218 1.75
-5.88898 9.51053 1.75
-5.92064 9.51053 1.75
-5.95231 9.51053 1.75
-5.95231 9.47888 1.75
-5.98397 9.47888 1.75
-5.98397 9.44724 1.75
-6.01563 9.41559 1.75
-6.01563 9.38394 1.75
-6.01563 9.35229 1.75
-5.98397 9.35229 1.75
-5.95231 9.35229 1.75
-5.92064 9.35229 1.75
-5.88898 9.35229 1.75
-5.85732 9.35229 1.75
-5.82566 9.35229 1.75
-5.794 9.35229 1.75
-5.76234 9.35229 1.75
-5.76234 9.38394 1.75
-5.73068 9.38394 1.75
-5.69902 9.38394 1.75
-5.69902 9.41559 1.75
-5.66736 9.41559 1.75
-5.66736 9.44724 1.75
-5.6357 9.44724 1.75
-5.6357 9.47888 1.75
-5.60404 9.47888 1.75
-5.60404 9.51053 1.75
-5.57238 9.51053 1.75
-5.57238 9.54218 1.75
-5.54071 9.54218 1.75
-5.54071 9.57383 1.75
-5.50905 9.60548 1.75
-5.50905 9.63713 1.75
-5.54071 9.63713 1.75
-5.57238 9.63713 1.75
-5.60404 9.63713 1.75
-5.6357 9.63713 1.75
-5.66736 9.63713 1.75
 };  // End of points for curve 1
}; // End of curve 1
//----------------------------------------------------
//  ROI: ROI_1
//  Curve 2 of 2
//----------------------------------------------------
               curve={
                       flags =       131092;
                       block_size =  32;
                       num_points =  56;
points={
-5.66736 9.63713 1.5
-5.69902 9.63713 1.5
-5.73068 9.63713 1.5
-5.76234 9.60548 1.5
-5.794 9.60548 1.5
-5.794 9.57383 1.5
-5.82566 9.57383 1.5
-5.82566 9.54218 1.5
-5.85732 9.54218 1.5
-5.88898 9.54218 1.5
-5.92064 9.54218 1.5
-5.92064 9.51053 1.5
-5.95231 9.51053 1.5
-5.95231 9.47888 1.5
-5.98397 9.47888 1.5
-6.01563 9.47888 1.5
-6.04729 9.44724 1.5
-6.04729 9.41559 1.5
-6.04729 9.38394 1.5
-6.04729 9.35229 1.5
-6.04729 9.32064 1.5
-6.01563 9.32064 1.5
-6.01563 9.28899 1.5
-5.98397 9.28899 1.5
-5.95231 9.28899 1.5
-5.92064 9.28899 1.5
-5.88898 9.28899 1.5
-5.85732 9.28899 1.5
-5.82566 9.28899 1.5
-5.794 9.28899 1.5
-5.76234 9.28899 1.5
-5.76234 9.32064 1.5
-5.73068 9.35229 1.5
-5.73068 9.38394 1.5
-5.69902 9.38394 1.5
-5.69902 9.41559 1.5
-5.66736 9.41559 1.5
-5.6357 9.44724 1.5
-5.60404 9.44724 1.5
-5.60404 9.47888 1.5
-5.57238 9.47888 1.5
-5.57238 9.51053 1.5
-5.54071 9.51053 1.5
-5.50905 9.51053 1.5
-5.50905 9.54218 1.5
-5.50905 9.57383 1.5
-5.50905 9.60548 1.5
-5.50905 9.63713 1.5
-5.54071 9.63713 1.5
-5.57238 9.63713 1.5
-5.57238 9.66878 1.5
-5.60404 9.66878 1.5
-5.6357 9.66878 1.5
-5.66736 9.66878 1.5
-5.69902 9.63713 1.5
-5.73068 9.63713 1.5
 };  // End of points for curve 2
}; // End of curve 2
               surface_mesh={
                       allow_adaptation = 1;
                       replace_mean_mesh = 0;
                       auto_clean_contours = 1;
                       auto_smooth_mesh = 1;
                       internal_weight = -3.000000;
                       surface_weight = 1.000000;
                       distance_weight = 2.000000;
                       profile_length = 10;
                       limit_feature_search_string: Full
                       pixel_size = 1.000000;
                       feature_weight = 0.010000;
                       lower_CT_bound = 0.000000;
                       upper_CT_bound = 4095.000000;
                       gradient_clipping: Soft
                       gradient_direction: Unsigned
                       maximum_gradient = 100.000000;
                       avoid_CT_range: None
                       avoid_CT_threshold = 4095.000000;
                       number_of_modes = 0;
                       number_of_vertices = 0;
                       number_of_triangles = 0;
                       max_iterations = 10;
                       smooth_between_iterations = 0;
                       repair_between_iterations = 1;
                       translation_increment_cm = 0.500000;
                       rotation_increment = 20.000000;
                       magnify_increment = 0.200000;
                       sphere_tool_radius = 1.500000;
                       gaussian_tool_radius = 3.000000;
                       vertex_drag_curvature = 0.700000;
                       vertex_drag_neighbor_layers = 3;
                       vertices={
 };  // End of vertices for surface mesh
                       triangles={
 };  // End of triangles for surface mesh
}; // End of surface_mesh 
               mean_mesh={
                       samples = 1;
                       number_of_vertices = 0;
                       number_of_triangles = 0;
                       vertices={
 };  // End of vertices for mean mesh
                       triangles={
 };  // End of triangles for mean mesh
}; // End of mean_mesh 
        }; // End of ROI ROI_1
//-----------------------------------------------------
//  Beginning of ROI: ROI_2
//-----------------------------------------------------

 roi={
           name: ROI_2
    volume_name: LAST^FIRST^M
stats_volume_name: LAST^FIRST^M
           author:   
           organ_name:   
           flags =          135168;
           roiinterpretedtype: ORGAN
           color:           blue
           box_size =       5;
           line_2d_width =  2;
           line_3d_width =  1;
           paint_brush_radius =  1;
           paint_allow_curve_closing = 1;
           curve_min_area =  0.1;
           curve_overlap_min =  88;
           lower =          800;
           upper =          4096;
           radius =         0;
           density =        1;
           density_units:   g/cm^3
           override_data =  0;
           override_order =  0;
           override_material =  0;
           material:        None
           invert_density_loading =  0;
           volume =         7.89281;
           pixel_min =      933;
           pixel_max =      1133;
           pixel_mean =     1037.56;
           pixel_std =      16.9398;
           bBEVDRROutline = 0;
           display_on_other_vols = 1;
           is_linked =      0;
           auto_update_contours =  0;
           UID: 1.3.46.670589.13.570038639.20200101090841.91297
           stoppingpower =        0;
           ctnumber =        0;
           is_created_by_atlasseg = 0;
           is_created_by_autoplanengine = 0;
           num_curve = 8;
//----------------------------------------------------
//  ROI: ROI_2
//  Curve 1 of 8
//----------------------------------------------------
               curve={
                       flags =       131092;
                       block_size =  32;
                       num_points =  67;
points={
-5.56646 9.86321 2.25
-5.4688 9.86321 2.25
-5.37115 9.86321 2.25
-5.27349 9.86321 2.25
-5.17583 9.86321 2.25
-5.07818 9.86321 2.25
-4.98052 9.76556 2.25
-4.88286 9.76556 2.25
-4.78521 9.76556 2.25
-4.68755 9.6679 2.25
-4.5899 9.57025 2.25
-4.49224 9.47259 2.25
-4.39458 9.37493 2.25
-4.29693 9.27728 2.25
-4.29693 9.17962 2.25
-4.29693 9.08197 2.25
-4.19927 8.98431 2.25
-4.19927 8.88665 2.25
-4.19927 8.789 2.25
-4.19927 8.69134 2.25
-4.19927 8.59369 2.25
-4.19927 8.49603 2.25
-4.19927 8.39837 2.25
-4.29693 8.30072 2.25
-4.29693 8.20306 2.25
-4.29693 8.10541 2.25
-4.39458 8.00775 2.25
-4.49224 7.91009 2.25
-4.5899 7.81244 2.25
-4.68755 7.71478 2.25
-4.78521 7.71478 2.25
-4.88286 7.71478 2.25
-4.98052 7.61713 2.25
-5.07818 7.61713 2.25
-5.17583 7.61713 2.25
-5.27349 7.61713 2.25
-5.37115 7.61713 2.25
-5.4688 7.61713 2.25
-5.56646 7.61713 2.25
-5.66411 7.61713 2.25
-5.76177 7.71478 2.25
-5.85942 7.71478 2.25
-5.95708 7.71478 2.25
-6.05474 7.81244 2.25
-6.15239 7.91009 2.25
-6.25005 8.00775 2.25
-6.3477 8.10541 2.25
-6.3477 8.20306 2.25
-6.3477 8.30072 2.25
-6.44536 8.39837 2.25
-6.44536 8.49603 2.25
-6.44536 8.59369 2.25
-6.44536 8.69134 2.25
-6.44536 8.789 2.25
-6.44536 8.88665 2.25
-6.44536 8.98431 2.25
-6.3477 9.08197 2.25
-6.3477 9.17962 2.25
-6.3477 9.27728 2.25
-6.25005 9.37493 2.25
-6.15239 9.47259 2.25
-6.05474 9.57025 2.25
-5.95708 9.6679 2.25
-5.85942 9.76556 2.25
-5.76177 9.76556 2.25
-5.66411 9.76556 2.25
-5.56646 9.86321 2.25
 };  // End of points for curve 1
}; // End of curve 1
//----------------------------------------------------
//  ROI: ROI_2
//  Curve 2 of 8
//----------------------------------------------------
               curve={
                       flags =       131092;
                       block_size =  32;
                       num_points =  73;
points={
-5.56646 10.0585 1.75
-5.4688 10.0585 1.75
-5.37115 10.0585 1.75
-5.27349 10.0585 1.75
-5.17583 10.0585 1.75
-5.07818 10.0585 1.75
-4.98052 10.0585 1.75
-4.88286 9.96087 1.75
-4.78521 9.96087 1.75
-4.68755 9.96087 1.75
-4.5899 9.86321 1.75
-4.49224 9.86321 1.75
-4.39458 9.76556 1.75
-4.29693 9.6679 1.75
-4.19927 9.57025 1.75
-4.10162 9.47259 1.75
-4.10162 9.37493 1.75
-4.10162 9.27728 1.75
-4.00396 9.17962 1.75
-4.00396 9.08197 1.75
-4.00396 8.98431 1.75
-4.00396 8.88665 1.75
-4.00396 8.789 1.75
-4.00396 8.69134 1.75
-4.00396 8.59369 1.75
-4.10162 8.49603 1.75
-4.10162 8.39837 1.75
-4.10162 8.30072 1.75
-4.19927 8.20306 1.75
-4.19927 8.10541 1.75
-4.29693 8.00775 1.75
-4.39458 7.91009 1.75
-4.49224 7.81244 1.75
-4.5899 7.71478 1.75
-4.68755 7.71478 1.75
-4.78521 7.71478 1.75
-4.88286 7.61713 1.75
-4.98052 7.61713 1.75
-5.07818 7.61713 1.75
-5.17583 7.61713 1.75
-5.27349 7.61713 1.75
-5.37115 7.61713 1.75
-5.4688 7.61713 1.75
-5.56646 7.61713 1.75
-5.66411 7.71478 1.75
-5.76177 7.71478 1.75
-5.85942 7.71478 1.75
-5.95708 7.81244 1.75
-6.05474 7.91009 1.75
-6.15239 8.00775 1.75
-6.25005 8.10541 1.75
-6.3477 8.20306 1.75
-6.3477 8.30072 1.75
-6.3477 8.39837 1.75
-6.44536 8.49603 1.75
-6.44536 8.59369 1.75
-6.44536 8.69134 1.75
-6.44536 8.789 1.75
-6.44536 8.88665 1.75
-6.44536 8.98431 1.75
-6.44536 9.08197 1.75
-6.44536 9.17962 1.75
-6.3477 9.27728 1.75
-6.3477 9.37493 1.75
-6.3477 9.47259 1.75
-6.25005 9.57025 1.75
-6.15239 9.6679 1.75
-6.05474 9.76556 1.75
-5.95708 9.86321 1.75
-5.85942 9.96087 1.75
-5.76177 9.96087 1.75
-5.66411 9.96087 1.75
-5.56646 10.0585 1.75
 };  // End of points for curve 2
}; // End of curve 2
//----------------------------------------------------
//  ROI: ROI_2
//  Curve 3 of 8
//----------------------------------------------------
               curve={
                       flags =       131092;
                       block_size =  32;
                       num_points =  72;
points={
-5.37115 10.0585 1.25
-5.27349 10.0585 1.25
-5.17583 10.0585 1.25
-5.07818 10.0585 1.25
-4.98052 10.0585 1.25
-4.88286 10.0585 1.25
-4.78521 10.0585 1.25
-4.68755 9.96087 1.25
-4.5899 9.96087 1.25
-4.49224 9.96087 1.25
-4.39458 9.86321 1.25
-4.29693 9.76556 1.25
-4.19927 9.6679 1.25
-4.10162 9.57025 1.25
-4.10162 9.47259 1.25
-4.10162 9.37493 1.25
-4.00396 9.27728 1.25
-4.00396 9.17962 1.25
-4.00396 9.08197 1.25
-4.00396 8.98431 1.25
-4.00396 8.88665 1.25
-4.10162 8.789 1.25
-4.10162 8.69134 1.25
-4.10162 8.59369 1.25
-4.10162 8.49603 1.25
-4.19927 8.39837 1.25
-4.19927 8.30072 1.25
-4.19927 8.20306 1.25
-4.29693 8.10541 1.25
-4.39458 8.00775 1.25
-4.49224 7.91009 1.25
-4.5899 7.81244 1.25
-4.68755 7.71478 1.25
-4.78521 7.71478 1.25
-4.88286 7.71478 1.25
-4.98052 7.61713 1.25
-5.07818 7.61713 1.25
-5.17583 7.61713 1.25
-5.27349 7.61713 1.25
-5.37115 7.61713 1.25
-5.4688 7.61713 1.25
-5.56646 7.61713 1.25
-5.66411 7.71478 1.25
-5.76177 7.71478 1.25
-5.85942 7.71478 1.25
-5.95708 7.81244 1.25
-6.05474 7.91009 1.25
-6.15239 8.00775 1.25
-6.25005 8.10541 1.25
-6.25005 8.20306 1.25
-6.25005 8.30072 1.25
-6.3477 8.39837 1.25
-6.3477 8.49603 1.25
-6.3477 8.59369 1.25
-6.3477 8.69134 1.25
-6.3477 8.789 1.25
-6.3477 8.88665 1.25
-6.3477 8.98431 1.25
-6.3477 9.08197 1.25
-6.3477 9.17962 1.25
-6.25005 9.27728 1.25
-6.25005 9.37493 1.25
-6.25005 9.47259 1.25
-6.15239 9.57025 1.25
-6.05474 9.6679 1.25
-5.95708 9.76556 1.25
-5.85942 9.86321 1.25
-5.76177 9.86321 1.25
-5.66411 9.96087 1.25
-5.56646 9.96087 1.25
-5.4688 9.96087 1.25
-5.37115 10.0585 1.25
 };  // End of points for curve 3
}; // End of curve 3
//----------------------------------------------------
//  ROI: ROI_2
//  Curve 4 of 8
//----------------------------------------------------
               curve={
                       flags =       131092;
                       block_size =  32;
                       num_points =  60;
points={
-5.37115 9.86321 1
-5.27349 9.86321 1
-5.17583 9.86321 1
-5.07818 9.86321 1
-4.98052 9.86321 1
-4.88286 9.86321 1
-4.78521 9.76556 1
-4.68755 9.76556 1
-4.5899 9.76556 1
-4.49224 9.6679 1
-4.39458 9.57025 1
-4.29693 9.47259 1
-4.19927 9.37493 1
-4.19927 9.27728 1
-4.19927 9.17962 1
-4.10162 9.08197 1
-4.10162 8.98431 1
-4.10162 8.88665 1
-4.10162 8.789 1
-4.10162 8.69134 1
-4.19927 8.59369 1
-4.19927 8.49603 1
-4.19927 8.39837 1
-4.29693 8.30072 1
-4.39458 8.20306 1
-4.49224 8.10541 1
-4.5899 8.00775 1
-4.68755 7.91009 1
-4.78521 7.91009 1
-4.88286 7.91009 1
-4.98052 7.81244 1
-5.07818 7.81244 1
-5.17583 7.81244 1
-5.27349 7.81244 1
-5.37115 7.81244 1
-5.4688 7.91009 1
-5.56646 7.91009 1
-5.66411 7.91009 1
-5.76177 8.00775 1
-5.85942 8.10541 1
-5.95708 8.20306 1
-6.05474 8.30072 1
-6.05474 8.39837 1
-6.05474 8.49603 1
-6.15239 8.59369 1
-6.15239 8.69134 1
-6.15239 8.789 1
-6.15239 8.88665 1
-6.15239 8.98431 1
-6.15239 9.08197 1
-6.05474 9.17962 1
-6.05474 9.27728 1
-6.05474 9.37493 1
-5.95708 9.47259 1
-5.85942 9.57025 1
-5.76177 9.6679 1
-5.66411 9.76556 1
-5.56646 9.76556 1
-5.4688 9.76556 1
-5.37115 9.86321 1
 };  // End of points for curve 4
}; // End of curve 4
//----------------------------------------------------
//  ROI: ROI_2
//  Curve 5 of 8
//----------------------------------------------------
               curve={
                       flags =       131092;
                       block_size =  32;
                       num_points =  57;
points={
-5.27349 9.76556 0.75
-5.17583 9.76556 0.75
-5.07818 9.76556 0.75
-4.98052 9.76556 0.75
-4.88286 9.76556 0.75
-4.78521 9.6679 0.75
-4.68755 9.6679 0.75
-4.5899 9.6679 0.75
-4.49224 9.57025 0.75
-4.39458 9.47259 0.75
-4.29693 9.37493 0.75
-4.19927 9.27728 0.75
-4.19927 9.17962 0.75
-4.19927 9.08197 0.75
-4.10162 8.98431 0.75
-4.10162 8.88665 0.75
-4.10162 8.789 0.75
-4.10162 8.69134 0.75
-4.10162 8.59369 0.75
-4.19927 8.49603 0.75
-4.19927 8.39837 0.75
-4.19927 8.30072 0.75
-4.29693 8.20306 0.75
-4.39458 8.10541 0.75
-4.49224 8.00775 0.75
-4.5899 7.91009 0.75
-4.68755 7.91009 0.75
-4.78521 7.91009 0.75
-4.88286 7.81244 0.75
-4.98052 7.81244 0.75
-5.07818 7.81244 0.75
-5.17583 7.81244 0.75
-5.27349 7.81244 0.75
-5.37115 7.91009 0.75
-5.4688 7.91009 0.75
-5.56646 7.91009 0.75
-5.66411 8.00775 0.75
-5.76177 8.10541 0.75
-5.85942 8.20306 0.75
-5.95708 8.30072 0.75
-5.95708 8.39837 0.75
-5.95708 8.49603 0.75
-6.05474 8.59369 0.75
-6.05474 8.69134 0.75
-6.05474 8.789 0.75
-6.05474 8.88665 0.75
-6.05474 8.98431 0.75
-5.95708 9.08197 0.75
-5.95708 9.17962 0.75
-5.95708 9.27728 0.75
-5.85942 9.37493 0.75
-5.76177 9.47259 0.75
-5.66411 9.57025 0.75
-5.56646 9.6679 0.75
-5.4688 9.6679 0.75
-5.37115 9.6679 0.75
-5.27349 9.76556 0.75
 };  // End of points for curve 5
}; // End of curve 5
//----------------------------------------------------
//  ROI: ROI_2
//  Curve 6 of 8
//----------------------------------------------------
               curve={
                       flags =       131092;
                       block_size =  32;
                       num_points =  67;
points={
-5.56646 9.76556 2
-5.4688 9.76556 2
-5.37115 9.76556 2
-5.27349 9.76556 2
-5.17583 9.76556 2
-5.07818 9.76556 2
-4.98052 9.76556 2
-4.88286 9.6679 2
-4.78521 9.6679 2
-4.68755 9.6679 2
-4.5899 9.57025 2
-4.49224 9.47259 2
-4.39458 9.37493 2
-4.29693 9.27728 2
-4.29693 9.17962 2
-4.29693 9.08197 2
-4.19927 8.98431 2
-4.19927 8.88665 2
-4.19927 8.789 2
-4.19927 8.69134 2
-4.19927 8.59369 2
-4.19927 8.49603 2
-4.19927 8.39837 2
-4.29693 8.30072 2
-4.29693 8.20306 2
-4.29693 8.10541 2
-4.39458 8.00775 2
-4.49224 7.91009 2
-4.5899 7.81244 2
-4.68755 7.71478 2
-4.78521 7.61713 2
-4.88286 7.51947 2
-4.98052 7.51947 2
-5.07818 7.51947 2
-5.17583 7.42181 2
-5.27349 7.42181 2
-5.37115 7.42181 2
-5.4688 7.42181 2
-5.56646 7.42181 2
-5.66411 7.51947 2
-5.76177 7.51947 2
-5.85942 7.51947 2
-5.95708 7.61713 2
-6.05474 7.71478 2
-6.15239 7.81244 2
-6.25005 7.91009 2
-6.25005 8.00775 2
-6.25005 8.10541 2
-6.3477 8.20306 2
-6.3477 8.30072 2
-6.3477 8.39837 2
-6.3477 8.49603 2
-6.3477 8.59369 2
-6.3477 8.69134 2
-6.3477 8.789 2
-6.3477 8.88665 2
-6.3477 8.98431 2
-6.25005 9.08197 2
-6.25005 9.17962 2
-6.25005 9.27728 2
-6.15239 9.37493 2
-6.05474 9.47259 2
-5.95708 9.57025 2
-5.85942 9.6679 2
-5.76177 9.6679 2
-5.66411 9.6679 2
-5.56646 9.76556 2
 };  // End of points for curve 6
}; // End of curve 6
//----------------------------------------------------
//  ROI: ROI_2
//  Curve 7 of 8
//----------------------------------------------------
               curve={
                       flags =       131092;
                       block_size =  32;
                       num_points =  57;
points={
-5.4688 9.76556 2.5
-5.37115 9.76556 2.5
-5.27349 9.76556 2.5
-5.17583 9.76556 2.5
-5.07818 9.76556 2.5
-4.98052 9.6679 2.5
-4.88286 9.6679 2.5
-4.78521 9.6679 2.5
-4.68755 9.57025 2.5
-4.5899 9.47259 2.5
-4.49224 9.37493 2.5
-4.39458 9.27728 2.5
-4.39458 9.17962 2.5
-4.39458 9.08197 2.5
-4.29693 8.98431 2.5
-4.29693 8.88665 2.5
-4.29693 8.789 2.5
-4.29693 8.69134 2.5
-4.29693 8.59369 2.5
-4.39458 8.49603 2.5
-4.39458 8.39837 2.5
-4.39458 8.30072 2.5
-4.49224 8.20306 2.5
-4.5899 8.10541 2.5
-4.68755 8.00775 2.5
-4.78521 7.91009 2.5
-4.88286 7.91009 2.5
-4.98052 7.91009 2.5
-5.07818 7.81244 2.5
-5.17583 7.81244 2.5
-5.27349 7.81244 2.5
-5.37115 7.81244 2.5
-5.4688 7.81244 2.5
-5.56646 7.91009 2.5
-5.66411 7.91009 2.5
-5.76177 7.91009 2.5
-5.85942 8.00775 2.5
-5.95708 8.10541 2.5
-6.05474 8.20306 2.5
-6.15239 8.30072 2.5
-6.15239 8.39837 2.5
-6.15239 8.49603 2.5
-6.25005 8.59369 2.5
-6.25005 8.69134 2.5
-6.25005 8.789 2.5
-6.25005 8.88665 2.5
-6.25005 8.98431 2.5
-6.15239 9.08197 2.5
-6.15239 9.17962 2.5
-6.15239 9.27728 2.5
-6.05474 9.37493 2.5
-5.95708 9.47259 2.5
-5.85942 9.57025 2.5
-5.76177 9.6679 2.5
-5.66411 9.6679 2.5
-5.56646 9.6679 2.5
-5.4688 9.76556 2.5
 };  // End of points for curve 7
}; // End of curve 7
//----------------------------------------------------
//  ROI: ROI_2
//  Curve 8 of 8
//----------------------------------------------------
               curve={
                       flags =       131092;
                       block_size =  32;
                       num_points =  72;
points={
-5.4688 10.0585 1.5
-5.37115 10.0585 1.5
-5.27349 10.0585 1.5
-5.17583 10.0585 1.5
-5.07818 10.0585 1.5
-4.98052 10.0585 1.5
-4.88286 9.96087 1.5
-4.78521 9.96087 1.5
-4.68755 9.96087 1.5
-4.5899 9.86321 1.5
-4.49224 9.86321 1.5
-4.39458 9.76556 1.5
-4.29693 9.6679 1.5
-4.19927 9.57025 1.5
-4.10162 9.47259 1.5
-4.10162 9.37493 1.5
-4.10162 9.27728 1.5
-4.00396 9.17962 1.5
-4.00396 9.08197 1.5
-4.00396 8.98431 1.5
-4.00396 8.88665 1.5
-4.10162 8.789 1.5
-4.10162 8.69134 1.5
-4.10162 8.59369 1.5
-4.10162 8.49603 1.5
-4.19927 8.39837 1.5
-4.19927 8.30072 1.5
-4.19927 8.20306 1.5
-4.29693 8.10541 1.5
-4.39458 8.00775 1.5
-4.49224 7.91009 1.5
-4.5899 7.81244 1.5
-4.68755 7.71478 1.5
-4.78521 7.71478 1.5
-4.88286 7.71478 1.5
-4.98052 7.61713 1.5
-5.07818 7.61713 1.5
-5.17583 7.61713 1.5
-5.27349 7.61713 1.5
-5.37115 7.61713 1.5
-5.4688 7.61713 1.5
-5.56646 7.61713 1.5
-5.66411 7.71478 1.5
-5.76177 7.71478 1.5
-5.85942 7.71478 1.5
-5.95708 7.81244 1.5
-6.05474 7.91009 1.5
-6.15239 8.00775 1.5
-6.25005 8.10541 1.5
-6.3477 8.20306 1.5
-6.3477 8.30072 1.5
-6.3477 8.39837 1.5
-6.3477 8.49603 1.5
-6.3477 8.59369 1.5
-6.3477 8.69134 1.5
-6.3477 8.789 1.5
-6.3477 8.88665 1.5
-6.3477 8.98431 1.5
-6.3477 9.08197 1.5
-6.3477 9.17962 1.5
-6.3477 9.27728 1.5
-6.3477 9.37493 1.5
-6.3477 9.47259 1.5
-6.25005 9.57025 1.5
-6.15239 9.6679 1.5
-6.05474 9.76556 1.5
-5.95708 9.86321 1.5
-5.85942 9.86321 1.5
-5.76177 9.96087 1.5
-5.66411 9.96087 1.5
-5.56646 9.96087 1.5
-5.4688 10.0585 1.5
 };  // End of points for curve 8
}; // End of curve 8
               surface_mesh={
                       allow_adaptation = 1;
                       replace_mean_mesh = 0;
                       auto_clean_contours = 1;
                       auto_smooth_mesh = 1;
                       internal_weight = -3.000000;
                       surface_weight = 1.000000;
                       distance_weight = 2.000000;
                       profile_length = 10;
                       limit_feature_search_string: Full
                       pixel_size = 1.000000;
                       feature_weight = 0.010000;
                       lower_CT_bound = 0.000000;
                       upper_CT_bound = 4095.000000;
                       gradient_clipping: Soft
                       gradient_direction: Unsigned
                       maximum_gradient = 100.000000;
                       avoid_CT_range: None
                       avoid_CT_threshold = 4095.000000;
                       number_of_modes = 0;
                       number_of_vertices = 0;
                       number_of_triangles = 0;
                       max_iterations = 10;
                       smooth_between_iterations = 0;
                       repair_between_iterations = 1;
                       translation_increment_cm = 0.500000;
                       rotation_increment = 20.000000;
                       magnify_increment = 0.200000;
                       sphere_tool_radius = 1.500000;
                       gaussian_tool_radius = 3.000000;
                       vertex_drag_curvature = 0.700000;
                       vertex_drag_neighbor_layers = 3;
                       vertices={
 };  // End of vertices for surface mesh
                       triangles={
 };  // End of triangles for surface mesh
}; // End of surface_mesh 
               mean_mesh={
                       samples = 1;
                       number_of_vertices = 0;
                       number_of_triangles = 0;
                       vertices={
 };  // End of vertices for mean mesh
                       triangles={
 };  // End of triangles for mean mesh
}; // End of mean_mesh 
        }; // End of ROI ROI_2
//-----------------------------------------------------
//  Beginning of ROI: ROI_3
//-----------------------------------------------------

 roi={
           name: ROI_3
    volume_name: LAST^FIRST^M
stats_volume_name: LAST^FIRST^M
           author:   
           organ_name:   
           flags =          135168;
           roiinterpretedtype: ORGAN
           color:           yellow
           box_size =       5;
           line_2d_width =  2;
           line_3d_width =  1;
           paint_brush_radius =  1;
           paint_allow_curve_closing = 1;
           curve_min_area =  0.1;
           curve_overlap_min =  88;
           lower =          800;
           upper =          4096;
           radius =         0;
           density =        1;
           density_units:   g/cm^3
           override_data =  0;
           override_order =  0;
           override_material =  0;
           material:        None
           invert_density_loading =  0;
           volume =         7.95479;
           pixel_min =      929;
           pixel_max =      1110;
           pixel_mean =     1038.7;
           pixel_std =      16.1489;
           bBEVDRROutline = 0;
           display_on_other_vols = 1;
           is_linked =      0;
           auto_update_contours =  0;
           UID: 1.3.46.670589.13.570038639.20200101090909.161493
           stoppingpower =        0;
           ctnumber =        0;
           is_created_by_atlasseg = 0;
           is_created_by_autoplanengine = 0;
           num_curve = 9;
//----------------------------------------------------
//  ROI: ROI_3
//  Curve 1 of 9
//----------------------------------------------------
               curve={
                       flags =       131092;
                       block_size =  32;
                       num_points =  64;
points={
-6.48499e-05 10.5468 2.75
0.0975914 10.5468 2.75
0.195248 10.5468 2.75
0.292904 10.5468 2.75
0.390558 10.5468 2.75
0.488214 10.5468 2.75
0.585871 10.4492 2.75
0.683527 10.4492 2.75
0.781183 10.4492 2.75
0.878839 10.3515 2.75
0.976496 10.2538 2.75
1.07415 10.1562 2.75
1.17181 10.0585 2.75
1.17181 9.96087 2.75
1.17181 9.86321 2.75
1.26946 9.76556 2.75
1.26946 9.6679 2.75
1.26946 9.57025 2.75
1.26946 9.47259 2.75
1.26946 9.37493 2.75
1.26946 9.27728 2.75
1.26946 9.17962 2.75
1.17181 9.08197 2.75
1.17181 8.98431 2.75
1.17181 8.88665 2.75
1.07415 8.789 2.75
0.976496 8.69134 2.75
0.878839 8.59369 2.75
0.781183 8.49603 2.75
0.683527 8.49603 2.75
0.585871 8.49603 2.75
0.488214 8.39837 2.75
0.390558 8.39837 2.75
0.292904 8.39837 2.75
0.195248 8.39837 2.75
0.0975914 8.39837 2.75
-6.48499e-05 8.39837 2.75
-0.0977211 8.39837 2.75
-0.195377 8.49603 2.75
-0.293034 8.49603 2.75
-0.39069 8.49603 2.75
-0.488344 8.59369 2.75
-0.586 8.69134 2.75
-0.683657 8.789 2.75
-0.781313 8.88665 2.75
-0.781313 8.98431 2.75
-0.781313 9.08197 2.75
-0.878969 9.17962 2.75
-0.878969 9.27728 2.75
-0.878969 9.37493 2.75
-0.878969 9.47259 2.75
-0.878969 9.57025 2.75
-0.878969 9.6679 2.75
-0.781313 9.76556 2.75
-0.781313 9.86321 2.75
-0.781313 9.96087 2.75
-0.683657 10.0585 2.75
-0.586 10.1562 2.75
-0.488344 10.2538 2.75
-0.39069 10.3515 2.75
-0.293034 10.4492 2.75
-0.195377 10.4492 2.75
-0.0977211 10.4492 2.75
-6.48499e-05 10.5468 2.75
 };  // End of points for curve 1
}; // End of curve 1
//----------------------------------------------------
//  ROI: ROI_3
//  Curve 2 of 9
//----------------------------------------------------
               curve={
                       flags =       131092;
                       block_size =  32;
                       num_points =  69;
points={
-0.0977211 10.6445 3.25
-6.48499e-05 10.6445 3.25
0.0975914 10.6445 3.25
0.195248 10.6445 3.25
0.292904 10.6445 3.25
0.390558 10.6445 3.25
0.488214 10.6445 3.25
0.585871 10.5468 3.25
0.683527 10.5468 3.25
0.781183 10.5468 3.25
0.878839 10.4492 3.25
0.976496 10.3515 3.25
1.07415 10.2538 3.25
1.17181 10.1562 3.25
1.26946 10.0585 3.25
1.26946 9.96087 3.25
1.26946 9.86321 3.25
1.36712 9.76556 3.25
1.36712 9.6679 3.25
1.36712 9.57025 3.25
1.36712 9.47259 3.25
1.36712 9.37493 3.25
1.36712 9.27728 3.25
1.36712 9.17962 3.25
1.26946 9.08197 3.25
1.26946 8.98431 3.25
1.26946 8.88665 3.25
1.17181 8.789 3.25
1.07415 8.69134 3.25
0.976496 8.59369 3.25
0.878839 8.49603 3.25
0.781183 8.39837 3.25
0.683527 8.39837 3.25
0.585871 8.39837 3.25
0.488214 8.30072 3.25
0.390558 8.30072 3.25
0.292904 8.30072 3.25
0.195248 8.30072 3.25
0.0975914 8.30072 3.25
-6.48499e-05 8.30072 3.25
-0.0977211 8.30072 3.25
-0.195377 8.39837 3.25
-0.293034 8.39837 3.25
-0.39069 8.39837 3.25
-0.488344 8.49603 3.25
-0.586 8.59369 3.25
-0.683657 8.69134 3.25
-0.781313 8.789 3.25
-0.781313 8.88665 3.25
-0.781313 8.98431 3.25
-0.878969 9.08197 3.25
-0.878969 9.17962 3.25
-0.878969 9.27728 3.25
-0.878969 9.37493 3.25
-0.878969 9.47259 3.25
-0.878969 9.57025 3.25
-0.878969 9.6679 3.25
-0.878969 9.76556 3.25
-0.878969 9.86321 3.25
-0.781313 9.96087 3.25
-0.781313 10.0585 3.25
-0.781313 10.1562 3.25
-0.683657 10.2538 3.25
-0.586 10.3515 3.25
-0.488344 10.4492 3.25
-0.39069 10.5468 3.25
-0.293034 10.5468 3.25
-0.195377 10.5468 3.25
-0.0977211 10.6445 3.25
 };  // End of points for curve 2
}; // End of curve 2
//----------------------------------------------------
//  ROI: ROI_3
//  Curve 3 of 9
//----------------------------------------------------
               curve={
                       flags =       131092;
                       block_size =  32;
                       num_points =  60;
points={
0.0975914 10.5468 3.75
0.195248 10.5468 3.75
0.292904 10.5468 3.75
0.390558 10.5468 3.75
0.488214 10.5468 3.75
0.585871 10.5468 3.75
0.683527 10.4492 3.75
0.781183 10.4492 3.75
0.878839 10.4492 3.75
0.976496 10.3515 3.75
1.07415 10.2538 3.75
1.17181 10.1562 3.75
1.26946 10.0585 3.75
1.26946 9.96087 3.75
1.26946 9.86321 3.75
1.36712 9.76556 3.75
1.36712 9.6679 3.75
1.36712 9.57025 3.75
1.36712 9.47259 3.75
1.36712 9.37493 3.75
1.36712 9.27728 3.75
1.26946 9.17962 3.75
1.26946 9.08197 3.75
1.26946 8.98431 3.75
1.17181 8.88665 3.75
1.07415 8.789 3.75
0.976496 8.69134 3.75
0.878839 8.59369 3.75
0.781183 8.59369 3.75
0.683527 8.59369 3.75
0.585871 8.49603 3.75
0.488214 8.49603 3.75
0.390558 8.49603 3.75
0.292904 8.49603 3.75
0.195248 8.49603 3.75
0.0975914 8.59369 3.75
-6.48499e-05 8.59369 3.75
-0.0977211 8.59369 3.75
-0.195377 8.69134 3.75
-0.293034 8.789 3.75
-0.39069 8.88665 3.75
-0.488344 8.98431 3.75
-0.586 9.08197 3.75
-0.586 9.17962 3.75
-0.586 9.27728 3.75
-0.683657 9.37493 3.75
-0.683657 9.47259 3.75
-0.683657 9.57025 3.75
-0.683657 9.6679 3.75
-0.683657 9.76556 3.75
-0.586 9.86321 3.75
-0.586 9.96087 3.75
-0.586 10.0585 3.75
-0.488344 10.1562 3.75
-0.39069 10.2538 3.75
-0.293034 10.3515 3.75
-0.195377 10.4492 3.75
-0.0977211 10.4492 3.75
-6.48499e-05 10.4492 3.75
0.0975914 10.5468 3.75
 };  // End of points for curve 3
}; // End of curve 3
//----------------------------------------------------
//  ROI: ROI_3
//  Curve 4 of 9
//----------------------------------------------------
               curve={
                       flags =       131092;
                       block_size =  32;
                       num_points =  57;
points={
0.0975914 10.4492 4
0.195248 10.4492 4
0.292904 10.4492 4
0.390558 10.4492 4
0.488214 10.4492 4
0.585871 10.3515 4
0.683527 10.3515 4
0.781183 10.3515 4
0.878839 10.2538 4
0.976496 10.1562 4
1.07415 10.0585 4
1.17181 9.96087 4
1.17181 9.86321 4
1.17181 9.76556 4
1.26946 9.6679 4
1.26946 9.57025 4
1.26946 9.47259 4
1.26946 9.37493 4
1.26946 9.27728 4
1.17181 9.17962 4
1.17181 9.08197 4
1.17181 8.98431 4
1.07415 8.88665 4
0.976496 8.789 4
0.878839 8.69134 4
0.781183 8.59369 4
0.683527 8.59369 4
0.585871 8.59369 4
0.488214 8.49603 4
0.390558 8.49603 4
0.292904 8.49603 4
0.195248 8.49603 4
0.0975914 8.49603 4
-6.48499e-05 8.59369 4
-0.0977211 8.59369 4
-0.195377 8.59369 4
-0.293034 8.69134 4
-0.39069 8.789 4
-0.488344 8.88665 4
-0.586 8.98431 4
-0.586 9.08197 4
-0.586 9.17962 4
-0.683657 9.27728 4
-0.683657 9.37493 4
-0.683657 9.47259 4
-0.683657 9.57025 4
-0.683657 9.6679 4
-0.586 9.76556 4
-0.586 9.86321 4
-0.586 9.96087 4
-0.488344 10.0585 4
-0.39069 10.1562 4
-0.293034 10.2538 4
-0.195377 10.3515 4
-0.0977211 10.3515 4
-6.48499e-05 10.3515 4
0.0975914 10.4492 4
 };  // End of points for curve 4
}; // End of curve 4
//----------------------------------------------------
//  ROI: ROI_3
//  Curve 5 of 9
//----------------------------------------------------
               curve={
                       flags =       131092;
                       block_size =  32;
                       num_points =  63;
points={
-6.48499e-05 10.5468 2.5
0.0975914 10.5468 2.5
0.195248 10.5468 2.5
0.292904 10.5468 2.5
0.390558 10.5468 2.5
0.488214 10.5468 2.5
0.585871 10.5468 2.5
0.683527 10.4492 2.5
0.781183 10.4492 2.5
0.878839 10.4492 2.5
0.976496 10.3515 2.5
1.07415 10.2538 2.5
1.17181 10.1562 2.5
1.26946 10.0585 2.5
1.26946 9.96087 2.5
1.26946 9.86321 2.5
1.36712 9.76556 2.5
1.36712 9.6679 2.5
1.36712 9.57025 2.5
1.36712 9.47259 2.5
1.36712 9.37493 2.5
1.26946 9.27728 2.5
1.26946 9.17962 2.5
1.26946 9.08197 2.5
1.17181 8.98431 2.5
1.17181 8.88665 2.5
1.07415 8.789 2.5
0.976496 8.69134 2.5
0.878839 8.59369 2.5
0.781183 8.49603 2.5
0.683527 8.49603 2.5
0.585871 8.49603 2.5
0.488214 8.39837 2.5
0.390558 8.39837 2.5
0.292904 8.39837 2.5
0.195248 8.39837 2.5
0.0975914 8.39837 2.5
-6.48499e-05 8.49603 2.5
-0.0977211 8.49603 2.5
-0.195377 8.49603 2.5
-0.293034 8.59369 2.5
-0.39069 8.69134 2.5
-0.488344 8.789 2.5
-0.586 8.88665 2.5
-0.683657 8.98431 2.5
-0.683657 9.08197 2.5
-0.683657 9.17962 2.5
-0.781313 9.27728 2.5
-0.781313 9.37493 2.5
-0.781313 9.47259 2.5
-0.781313 9.57025 2.5
-0.781313 9.6679 2.5
-0.781313 9.76556 2.5
-0.683657 9.86321 2.5
-0.683657 9.96087 2.5
-0.683657 10.0585 2.5
-0.586 10.1562 2.5
-0.488344 10.2538 2.5
-0.39069 10.3515 2.5
-0.293034 10.4492 2.5
-0.195377 10.4492 2.5
-0.0977211 10.4492 2.5
-6.48499e-05 10.5468 2.5
 };  // End of points for curve 5
}; // End of curve 5
//----------------------------------------------------
//  ROI: ROI_3
//  Curve 6 of 9
//----------------------------------------------------
               curve={
                       flags =       131092;
                       block_size =  32;
                       num_points =  57;
points={
-6.48499e-05 10.4492 2.25
0.0975914 10.4492 2.25
0.195248 10.4492 2.25
0.292904 10.4492 2.25
0.390558 10.4492 2.25
0.488214 10.3515 2.25
0.585871 10.3515 2.25
0.683527 10.3515 2.25
0.781183 10.2538 2.25
0.878839 10.1562 2.25
0.976496 10.0585 2.25
1.07415 9.96087 2.25
1.07415 9.86321 2.25
1.07415 9.76556 2.25
1.17181 9.6679 2.25
1.17181 9.57025 2.25
1.17181 9.47259 2.25
1.17181 9.37493 2.25
1.17181 9.27728 2.25
1.07415 9.17962 2.25
1.07415 9.08197 2.25
1.07415 8.98431 2.25
0.976496 8.88665 2.25
0.878839 8.789 2.25
0.781183 8.69134 2.25
0.683527 8.59369 2.25
0.585871 8.59369 2.25
0.488214 8.59369 2.25
0.390558 8.49603 2.25
0.292904 8.49603 2.25
0.195248 8.49603 2.25
0.0975914 8.49603 2.25
-6.48499e-05 8.49603 2.25
-0.0977211 8.59369 2.25
-0.195377 8.59369 2.25
-0.293034 8.59369 2.25
-0.39069 8.69134 2.25
-0.488344 8.789 2.25
-0.586 8.88665 2.25
-0.683657 8.98431 2.25
-0.683657 9.08197 2.25
-0.683657 9.17962 2.25
-0.781313 9.27728 2.25
-0.781313 9.37493 2.25
-0.781313 9.47259 2.25
-0.781313 9.57025 2.25
-0.781313 9.6679 2.25
-0.683657 9.76556 2.25
-0.683657 9.86321 2.25
-0.683657 9.96087 2.25
-0.586 10.0585 2.25
-0.488344 10.1562 2.25
-0.39069 10.2538 2.25
-0.293034 10.3515 2.25
-0.195377 10.3515 2.25
-0.0977211 10.3515 2.25
-6.48499e-05 10.4492 2.25
 };  // End of points for curve 6
}; // End of curve 6
//----------------------------------------------------
//  ROI: ROI_3
//  Curve 7 of 9
//----------------------------------------------------
               curve={
                       flags =       131092;
                       block_size =  32;
                       num_points =  57;
points={
0.0975914 10.3515 2
0.195248 10.3515 2
0.292904 10.3515 2
0.390558 10.3515 2
0.488214 10.3515 2
0.585871 10.2538 2
0.683527 10.2538 2
0.781183 10.2538 2
0.878839 10.1562 2
0.976496 10.0585 2
1.07415 9.96087 2
1.17181 9.86321 2
1.17181 9.76556 2
1.17181 9.6679 2
1.26946 9.57025 2
1.26946 9.47259 2
1.26946 9.37493 2
1.26946 9.27728 2
1.26946 9.17962 2
1.17181 9.08197 2
1.17181 8.98431 2
1.17181 8.88665 2
1.07415 8.789 2
0.976496 8.69134 2
0.878839 8.59369 2
0.781183 8.49603 2
0.683527 8.49603 2
0.585871 8.49603 2
0.488214 8.39837 2
0.390558 8.39837 2
0.292904 8.39837 2
0.195248 8.39837 2
0.0975914 8.39837 2
-6.48499e-05 8.49603 2
-0.0977211 8.49603 2
-0.195377 8.49603 2
-0.293034 8.59369 2
-0.39069 8.69134 2
-0.488344 8.789 2
-0.586 8.88665 2
-0.586 8.98431 2
-0.586 9.08197 2
-0.683657 9.17962 2
-0.683657 9.27728 2
-0.683657 9.37493 2
-0.683657 9.47259 2
-0.683657 9.57025 2
-0.586 9.6679 2
-0.586 9.76556 2
-0.586 9.86321 2
-0.488344 9.96087 2
-0.39069 10.0585 2
-0.293034 10.1562 2
-0.195377 10.2538 2
-0.0977211 10.2538 2
-6.48499e-05 10.2538 2
0.0975914 10.3515 2
 };  // End of points for curve 7
}; // End of curve 7
//----------------------------------------------------
//  ROI: ROI_3
//  Curve 8 of 9
//----------------------------------------------------
               curve={
                       flags =       131092;
                       block_size =  32;
                       num_points =  65;
points={
-0.293034 10.5468 3.5
-0.195377 10.5468 3.5
-0.0977211 10.5468 3.5
-6.48499e-05 10.5468 3.5
0.0975914 10.5468 3.5
0.195248 10.5468 3.5
0.292904 10.5468 3.5
0.390558 10.5468 3.5
0.488214 10.5468 3.5
0.585871 10.5468 3.5
0.683527 10.5468 3.5
0.781183 10.5468 3.5
0.878839 10.4492 3.5
0.976496 10.3515 3.5
1.07415 10.2538 3.5
1.17181 10.1562 3.5
1.26946 10.0585 3.5
1.26946 9.96087 3.5
1.26946 9.86321 3.5
1.36712 9.76556 3.5
1.36712 9.6679 3.5
1.36712 9.57025 3.5
1.36712 9.47259 3.5
1.36712 9.37493 3.5
1.36712 9.27728 3.5
1.36712 9.17962 3.5
1.26946 9.08197 3.5
1.26946 8.98431 3.5
1.26946 8.88665 3.5
1.17181 8.789 3.5
1.07415 8.69134 3.5
0.976496 8.59369 3.5
0.878839 8.49603 3.5
0.781183 8.49603 3.5
0.683527 8.49603 3.5
0.585871 8.39837 3.5
0.488214 8.39837 3.5
0.390558 8.39837 3.5
0.292904 8.39837 3.5
0.195248 8.39837 3.5
0.0975914 8.39837 3.5
-6.48499e-05 8.49603 3.5
-0.0977211 8.49603 3.5
-0.195377 8.49603 3.5
-0.293034 8.59369 3.5
-0.39069 8.69134 3.5
-0.488344 8.789 3.5
-0.586 8.88665 3.5
-0.683657 8.98431 3.5
-0.683657 9.08197 3.5
-0.683657 9.17962 3.5
-0.781313 9.27728 3.5
-0.781313 9.37493 3.5
-0.781313 9.47259 3.5
-0.781313 9.57025 3.5
-0.781313 9.6679 3.5
-0.781313 9.76556 3.5
-0.781313 9.86321 3.5
-0.683657 9.96087 3.5
-0.683657 10.0585 3.5
-0.683657 10.1562 3.5
-0.586 10.2538 3.5
-0.488344 10.3515 3.5
-0.39069 10.4492 3.5
-0.293034 10.5468 3.5
 };  // End of points for curve 8
}; // End of curve 8
//----------------------------------------------------
//  ROI: ROI_3
//  Curve 9 of 9
//----------------------------------------------------
               curve={
                       flags =       131092;
                       block_size =  32;
                       num_points =  68;
points={
-0.293034 10.5468 3
-0.195377 10.5468 3
-0.0977211 10.5468 3
-6.48499e-05 10.5468 3
0.0975914 10.5468 3
0.195248 10.5468 3
0.292904 10.5468 3
0.390558 10.5468 3
0.488214 10.5468 3
0.585871 10.5468 3
0.683527 10.5468 3
0.781183 10.5468 3
0.878839 10.4492 3
0.976496 10.3515 3
1.07415 10.2538 3
1.17181 10.1562 3
1.26946 10.0585 3
1.26946 9.96087 3
1.26946 9.86321 3
1.26946 9.76556 3
1.26946 9.6679 3
1.26946 9.57025 3
1.26946 9.47259 3
1.26946 9.37493 3
1.26946 9.27728 3
1.26946 9.17962 3
1.26946 9.08197 3
1.26946 8.98431 3
1.26946 8.88665 3
1.17181 8.789 3
1.07415 8.69134 3
0.976496 8.59369 3
0.878839 8.49603 3
0.781183 8.39837 3
0.683527 8.39837 3
0.585871 8.39837 3
0.488214 8.39837 3
0.390558 8.39837 3
0.292904 8.39837 3
0.195248 8.39837 3
0.0975914 8.39837 3
-6.48499e-05 8.39837 3
-0.0977211 8.39837 3
-0.195377 8.39837 3
-0.293034 8.39837 3
-0.39069 8.39837 3
-0.488344 8.49603 3
-0.586 8.59369 3
-0.683657 8.69134 3
-0.781313 8.789 3
-0.781313 8.88665 3
-0.781313 8.98431 3
-0.878969 9.08197 3
-0.878969 9.17962 3
-0.878969 9.27728 3
-0.878969 9.37493 3
-0.878969 9.47259 3
-0.878969 9.57025 3
-0.878969 9.6679 3
-0.878969 9.76556 3
-0.781313 9.86321 3
-0.781313 9.96087 3
-0.781313 10.0585 3
-0.683657 10.1562 3
-0.586 10.2538 3
-0.488344 10.3515 3
-0.39069 10.4492 3
-0.293034 10.5468 3
 };  // End of points for curve 9
}; // End of curve 9
               surface_mesh={
                       allow_adaptation = 1;
                       replace_mean_mesh = 0;
                       auto_clean_contours = 1;
                       auto_smooth_mesh = 1;
                       internal_weight = -3.000000;
                       surface_weight = 1.000000;
                       distance_weight = 2.000000;
                       profile_length = 10;
                       limit_feature_search_string: Full
                       pixel_size = 1.000000;
                       feature_weight = 0.010000;
                       lower_CT_bound = 0.000000;
                       upper_CT_bound = 4095.000000;
                       gradient_clipping: Soft
                       gradient_direction: Unsigned
                       maximum_gradient = 100.000000;
                       avoid_CT_range: None
                       avoid_CT_threshold = 4095.000000;
                       number_of_modes = 0;
                       number_of_vertices = 0;
                       number_of_triangles = 0;
                       max_iterations = 10;
                       smooth_between_iterations = 0;
                       repair_between_iterations = 1;
                       translation_increment_cm = 0.500000;
                       rotation_increment = 20.000000;
                       magnify_increment = 0.200000;
                       sphere_tool_radius = 1.500000;
                       gaussian_tool_radius = 3.000000;
                       vertex_drag_curvature = 0.700000;
                       vertex_drag_neighbor_layers = 3;
                       vertices={
 };  // End of vertices for surface mesh
                       triangles={
 };  // End of triangles for surface mesh
}; // End of surface_mesh 
               mean_mesh={
                       samples = 1;
                       number_of_vertices = 0;
                       number_of_triangles = 0;
                       vertices={
 };  // End of vertices for mean mesh
                       triangles={
 };  // End of triangles for mean mesh
}; // End of mean_mesh 
        }; // End of ROI ROI_3
//-----------------------------------------------------
//  Beginning of ROI: ROI_4
//-----------------------------------------------------

 roi={
           name: ROI_4
    volume_name: LAST^FIRST^M
stats_volume_name: LAST^FIRST^M
           author:   
           organ_name:   
           flags =          135168;
           roiinterpretedtype: ORGAN
           color:           purple
           box_size =       5;
           line_2d_width =  2;
           line_3d_width =  1;
           paint_brush_radius =  0.4;
           paint_allow_curve_closing = 1;
           curve_min_area =  0.1;
           curve_overlap_min =  88;
           lower =          800;
           upper =          4096;
           radius =         0;
           density =        1;
           density_units:   g/cm^3
           override_data =  0;
           override_order =  0;
           override_material =  0;
           material:        None
           invert_density_loading =  0;
           volume =         0.116825;
           pixel_min =      1010;
           pixel_max =      1058;
           pixel_mean =     1031.64;
           pixel_std =      10.9398;
           bBEVDRROutline = 0;
           display_on_other_vols = 1;
           is_linked =      0;
           auto_update_contours =  0;
           UID: 1.3.46.670589.13.570038639.20200101091001.761474
           stoppingpower =        0;
           ctnumber =        0;
           is_created_by_atlasseg = 0;
           is_created_by_autoplanengine = 0;
           num_curve = 2;
//----------------------------------------------------
//  ROI: ROI_4
//  Curve 1 of 2
//----------------------------------------------------
               curve={
                       flags =       20;
                       block_size =  32;
                       num_points =  69;
points={
0.126578 10.4284 3.25
0.0949173 10.4284 3.25
0.0632563 10.4284 3.25
0.0315952 10.4284 3.25
-6.58035e-05 10.4284 3.25
-0.0317268 10.4284 3.25
-0.0633869 10.4284 3.25
-0.095048 10.4284 3.25
-0.126709 10.4284 3.25
-0.15837 10.3967 3.25
-0.190031 10.3967 3.25
-0.221691 10.3967 3.25
-0.221691 10.3651 3.25
-0.253352 10.3651 3.25
-0.253352 10.3334 3.25
-0.285013 10.3018 3.25
-0.285013 10.2701 3.25
-0.316674 10.2701 3.25
-0.316674 10.2385 3.25
-0.348335 10.2385 3.25
-0.348335 10.2068 3.25
-0.348335 10.1752 3.25
-0.348335 10.1435 3.25
-0.316674 10.1435 3.25
-0.316674 10.1119 3.25
-0.316674 10.0802 3.25
-0.316674 10.0486 3.25
-0.285013 10.0486 3.25
-0.253352 10.0486 3.25
-0.221691 10.0486 3.25
-0.190031 10.0486 3.25
-0.15837 10.0486 3.25
-0.095048 10.0486 3.25
-0.0317268 10.0486 3.25
-6.58035e-05 10.0486 3.25
0.0315952 10.0486 3.25
0.0315952 10.0802 3.25
0.0632563 10.0802 3.25
0.0949173 10.0802 3.25
0.126578 10.0802 3.25
0.158238 10.0802 3.25
0.158238 10.1119 3.25
0.189899 10.1119 3.25
0.189899 10.1435 3.25
0.22156 10.1435 3.25
0.22156 10.1752 3.25
0.253222 10.1752 3.25
0.284883 10.2068 3.25
0.316543 10.2068 3.25
0.316543 10.2385 3.25
0.348204 10.2385 3.25
0.348204 10.2701 3.25
0.348204 10.3018 3.25
0.348204 10.3334 3.25
0.316543 10.3334 3.25
0.316543 10.3651 3.25
0.284883 10.3651 3.25
0.284883 10.3967 3.25
0.253222 10.4284 3.25
0.22156 10.4284 3.25
0.189899 10.4284 3.25
0.189899 10.46 3.25
0.158238 10.46 3.25
0.158238 10.4284 3.25
0.126578 10.4284 3.25
0.0949173 10.4284 3.25
0.0632563 10.4284 3.25
0.0315952 10.4284 3.25
-6.58035e-05 10.4284 3.25
 };  // End of points for curve 1
}; // End of curve 1
//----------------------------------------------------
//  ROI: ROI_4
//  Curve 2 of 2
//----------------------------------------------------
               curve={
                       flags =       131092;
                       block_size =  32;
                       num_points =  74;
points={
0.316543 10.3018 3
0.284883 10.3018 3
0.253222 10.3018 3
0.22156 10.3018 3
0.189899 10.3018 3
0.158238 10.3018 3
0.126578 10.3018 3
0.0949173 10.3018 3
0.0632563 10.3018 3
0.0315952 10.3018 3
-6.58035e-05 10.3018 3
-0.0317268 10.3018 3
-0.0633869 10.3018 3
-0.095048 10.3018 3
-0.095048 10.3334 3
-0.126709 10.3334 3
-0.15837 10.3334 3
-0.190031 10.3334 3
-0.221691 10.3334 3
-0.253352 10.3334 3
-0.285013 10.3334 3
-0.316674 10.3334 3
-0.348335 10.3334 3
-0.379996 10.3334 3
-0.379996 10.3018 3
-0.411656 10.2701 3
-0.443317 10.2385 3
-0.443317 10.2068 3
-0.474978 10.1752 3
-0.506639 10.1435 3
-0.506639 10.1119 3
-0.474978 10.1119 3
-0.443317 10.1119 3
-0.411656 10.1119 3
-0.379996 10.1119 3
-0.348335 10.1119 3
-0.316674 10.1119 3
-0.316674 10.0802 3
-0.285013 10.0802 3
-0.253352 10.0802 3
-0.221691 10.0802 3
-0.190031 10.0802 3
-0.15837 10.0802 3
-0.126709 10.0802 3
-0.095048 10.0802 3
-0.0633869 10.0802 3
-0.0317268 10.0802 3
-6.58035e-05 10.0802 3
0.0315952 10.0802 3
0.0632563 10.0802 3
0.0949173 10.0802 3
0.126578 10.1119 3
0.158238 10.1119 3
0.158238 10.1435 3
0.189899 10.1435 3
0.22156 10.1752 3
0.253222 10.1752 3
0.253222 10.2068 3
0.284883 10.2068 3
0.316543 10.2385 3
0.348204 10.2385 3
0.379865 10.2701 3
0.411526 10.2701 3
0.443187 10.2701 3
0.443187 10.3018 3
0.443187 10.3334 3
0.411526 10.3334 3
0.411526 10.3651 3
0.379865 10.3651 3
0.348204 10.3651 3
0.316543 10.3651 3
0.284883 10.3651 3
0.253222 10.3651 3
0.22156 10.3651 3
 };  // End of points for curve 2
}; // End of curve 2
               surface_mesh={
                       allow_adaptation = 1;
                       replace_mean_mesh = 0;
                       auto_clean_contours = 1;
                       auto_smooth_mesh = 1;
                       internal_weight = -3.000000;
                       surface_weight = 1.000000;
                       distance_weight = 2.000000;
                       profile_length = 10;
                       limit_feature_search_string: Full
                       pixel_size = 1.000000;
                       feature_weight = 0.010000;
                       lower_CT_bound = 0.000000;
                       upper_CT_bound = 4095.000000;
                       gradient_clipping: Soft
                       gradient_direction: Unsigned
                       maximum_gradient = 100.000000;
                       avoid_CT_range: None
                       avoid_CT_threshold = 4095.000000;
                       number_of_modes = 0;
                       number_of_vertices = 0;
                       number_of_triangles = 0;
                       max_iterations = 10;
                       smooth_between_iterations = 0;
                       repair_between_iterations = 1;
                       translation_increment_cm = 0.500000;
                       rotation_increment = 20.000000;
                       magnify_increment = 0.200000;
                       sphere_tool_radius = 1.500000;
                       gaussian_tool_radius = 3.000000;
                       vertex_drag_curvature = 0.700000;
                       vertex_drag_neighbor_layers = 3;
                       vertices={
 };  // End of vertices for surface mesh
                       triangles={
 };  // End of triangles for surface mesh
}; // End of surface_mesh 
               mean_mesh={
                       samples = 1;
                       number_of_vertices = 0;
                       number_of_triangles = 0;
                       vertices={
 };  // End of vertices for mean mesh
                       triangles={
 };  // End of triangles for mean mesh
}; // End of mean_mesh 
        }; // End of ROI ROI_4
