Name = "RogueOne";
VersionTimestamp = "2020-01-01 10:00:00";
PhotonEnergyList ={
  MachineEnergy ={
    Value = 6;
    Id = 0;
    Name = "6X";
    PhysicsData ={
      OutputFactor ={
        ReferenceDepth = 10;
        SourceToCalibrationPointDistance = 100;
        ElectronSSDTolerance = 0.1;
        DosePerMuAtCalibration = 0.8074;
        MinMLCPositionAtCalibration = 2;
        CalculatedCalibrationDose = 0.0186649;
      };
    };
  };
  MachineEnergy ={
    Value = 15;
    Id = 1;
    Name = "15X";
    PhysicsData ={
      OutputFactor ={
        ReferenceDepth = 10;
        SourceToCalibrationPointDistance = 100;
        ElectronSSDTolerance = 0.1;
        DosePerMuAtCalibration = 0.9303;
        MinMLCPositionAtCalibration = 2;
        CalculatedCalibrationDose = 0.016031;
      };
    };
  };
};
