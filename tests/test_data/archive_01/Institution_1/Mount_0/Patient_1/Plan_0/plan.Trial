Trial ={
  Name = "Trial_1";
  PatientRepresentation ={
    PatientVolumeName = "LAST^FIRST^M";
    CtToDensityName = "CT_DENSITY_01";
    CtToDensityVersion = "2020-01-01 10:00:00";
    DMTableName = "Standard Patient";
    DMTableVersion = "2020-01-01 10:00:00";
    TopZPadding = 0;
    BottomZPadding = 0;
    HighResZSpacingForVariable = 0.2;
    OutsidePatientIsCtNumber = 0;
    OutsidePatientAirThreshold = 0.6;
    CtToDensityTableAccepted = 1;
    CtToDensityTableExtended = 0;
    CtToStoppingPowerTableName = "LinearStoppingPowerTable";
    CtToStoppingPowerVersion = "2020-01-01 10:00:00";
    CtToStoppingPowerExtended = 0;
    CtToStoppingPowerAccepted = 1;
  };
  DoseGrid .VoxelSize .X = 0.3;
  DoseGrid .VoxelSize .Y = 0.3;
  DoseGrid .VoxelSize .Z = 0.3;
  DoseGrid .Dimension .X = 93;
  DoseGrid .Dimension .Y = 110;
  DoseGrid .Dimension .Z = 89;
  DoseGrid .Origin .X = -14.9044;
  DoseGrid .Origin .Y = -17.5729;
  DoseGrid .Origin .Z = -10.7747;
  DoseGrid .VolRotDelta .X = 0;
  DoseGrid .VolRotDelta .Y = 0;
  DoseGrid .VolRotDelta .Z = 0;
  DoseGrid .Display2d = 1;
  DoseGrid .DoseSummationType = 1;
  DoseStartSlice = 49;
  DoseEndSlice = 49;
  ScenarioDoseGridRes = 0.4;
  ScenarioDoseGridDimX = 0;
  ScenarioDoseGridDimY = 0;
  ScenarioDoseGridDimZ = 0;
  SuppressDoseGridSumming = 0;
  TrialUsedForDoseDisplayOnly = 0;
  RecordDoseData = \XDR:0\;
  FluenceGridResolution = 0.4;
  FluenceGridMatchesDoseGrid = 1;
  SourceToFilmDistance = 140;
  ScreenToPrinterZoomFactor = 1.33333;
  MCMaxSeconds = 3600;
  MCGlobalUncertaintyType = "mean_error_over_max_value";
  MCStatisticsThreshold = 50;
  MCUncertaintyGoal = 2;
  RemoveCouchFromScan = 1;
  CouchRemovalYCoordinate = -21.1183;
  Display2dCouchPosition = 1;
  Display3dCouchPosition = 1;
  CouchDisplayColor = "purple";
  RecomputeDensity = 1;
  PhysicsPlan = 0;
  ComputeRelativeDose = 0;
  RelativeDoseNormPointName = "<None>";
  RelativeDoseNormValue = 0;
  RelativeDoseNormValid = 0;
  RelativeDoseReferenceFieldName = "<None>";
  LastRelativeDoseReferenceField = "";
  RelativeDoseComputationStatus = "Uncomputed.";
  IsodoseNormPointName = "<None>";
  MaxDosePoint .Display2d = "Label";
  UseActualPatientForIrreg = 0;
  MaxDosePoint .Color = "green";
  PrescriptionList ={
    Prescription ={
      Name = "Brain";
      RequestedMonitorUnitsPerFraction = 280;
      PrescriptionDose = 250;
      PrescriptionPercent = 99;
      NumberOfFractions = 12;
      PrescriptionPoint = "iso";
      Method = "Prescribe";
      NormalizationMethod = "Point Dose";
      PrescriptionPeriod = "Per Fraction";
      WeightsProportionalTo = "Point Dose";
      DoseUncertainty = 1;
      PrescriptionUncertainty = 1;
      DoseUncertaintyValid = 0;
      PrescripUncertaintyValid = 0;
      Color = "red";
    };
  };
  BeamList ={
    Beam ={
      Name = "02 Lao Brain";
      IsocenterName = "iso";
      PrescriptionName = "Brain";
      UsePoiForPrescriptionPoint = 1;
      PrescriptionPointName = "iso";
      PrescriptionPointDepth = 5;
      PrescriptionPointXOffset = 0;
      PrescriptionPointYOffset = 0;
      SpecifyDosePerMuAtPrescriptionPoint = 0;
      DosePerMuAtPrescriptionPoint = 1;
      MachineNameAndVersion = "RogueOne: 2020-01-01 10:00:00";
      Modality = "Photons";
      MachineEnergyName = "6X";
      DesiredLocalizerName = "Laser";
      ActualLocalizerName = "Laser";
      DisplayLaserMotion = "Table";
      SetBeamType = "Step & Shoot MLC";
      PrevBeamType = "Static";
      ComputationVersion = "Pinnacle v16.0";
      CPManager ={
        CPManagerObject ={
          IsGantryStartStopLocked = 1;
          IsCouchStartStopLocked = 1;
          IsCollimatorStartStopLocked = 1;
          IsLeftRightIndependent = 1;
          IsTopBottomIndependent = 1;
          NumberOfControlPoints = 2;
          ControlPointList ={
            #0 ={
              Gantry = 79;
              Couch = 355;
              Collimator = 0;
              WedgeContext ={
                WedgeName = "No Wedge";
                Orientation = "NoWedge";
                OffsetOrigin = "Patient Surface";
                OffsetDistance = -2.5;
                Angle = "No Wedge";
                MinDeliverableMU = 0;
                MaxDeliverableMU = 1e+30;
              };
              LeftJawPosition = 10;
              RightJawPosition = 10;
              TopJawPosition = 11;
              BottomJawPosition = 6;
              ModifierList ={
                BeamModifier ={
                  Name = "BeamModifier_1";
                  FixToCollimator = 1;
                  AutoBlock = 0;
                  StructureToBlock = "Manual";
                  Margin = 0;
                  InsideMode = "Expose";
                  OutsideMode = "Block";
                  ContourList ={
                    CurvePainter ={
                      Curve ={
                        RawData ={
                          NumberOfDimensions = 2;
                          NumberOfPoints = 28;
                          Points[] ={
                            -12.0066,-12.6974,
                            -11.4145,-6.25,
                            -9.90132,-3.28948,
                            -8.71711,-1.9079,
                            -8.32237,-1.25,
                            -7.79606,-0.723685,
                            -6.67763,-5.83777e-07,
                            -5.88816,0.592105,
                            -5.29605,1.38158,
                            -4.57237,2.36842,
                            -3.38816,3.1579,
                            -1.80921,3.68421,
                            -0.625001,4.34211,
                            0.0986838,5.19737,
                            0.625,6.84211,
                            4.63816,6.71053,
                            6.21711,5.59211,
                            7.46711,4.27632,
                            8.84869,2.56579,
                            10.6908,0.13158,
                            12.5987,-4.86842,
                            11.8092,-11.3816,
                            9.76974,-13.8158,
                            6.61185,-14.7368,
                            -2.26974,-14.8026,
                            -7.33553,-14.2763,
                            -11.875,-12.7632,
                            -12.0066,-12.6974
                          };
                        };
                        LabelList ={
                          #0 ={
                            String = "X";
                          };
                          #1 ={
                            String = "Y";
                          };
                        };
                        RowLabelList ={
                        };
                        LabelFormatList ={
                          #0 ={
                            String = "%6.2f";
                          };
                          #1 ={
                            String = "%6.2f";
                          };
                        };
                      };
                      Color = "";
                      SliceCoordinate = 0;
                      Orientation = "Transverse";
                    };
                  };
                };
              };
              MLCLeafPositions ={
                RawData ={
                  NumberOfDimensions = 2;
                  NumberOfPoints = 60;
                  Points[] ={
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    9.9,10,
                    9.5,10,
                    9,10,
                    8.6,10,
                    8.4,10,
                    7.8,10,
                    7.1,10,
                    6.4,10,
                    5.8,10,
                    5.4,9.9,
                    5,9.5,
                    4.7,9.1,
                    4.1,8.7,
                    3.2,8.3,
                    1.7,7.9,
                    0.8,7.5,
                    0.3,7,
                    -0.1,6.6,
                    -0.3,6,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0
                  };
                };
                LabelList ={
                  #0 ={
                    String = "B (X1)";
                  };
                  #1 ={
                    String = "A (X2)";
                  };
                };
                RowLabelList ={
                  #0 ={
                    String = "  1. Y = 19.50 cm";
                  };
                  #1 ={
                    String = "  2. Y = 18.50 cm";
                  };
                  #2 ={
                    String = "  3. Y = 17.50 cm";
                  };
                  #3 ={
                    String = "  4. Y = 16.50 cm";
                  };
                  #4 ={
                    String = "  5. Y = 15.50 cm";
                  };
                  #5 ={
                    String = "  6. Y = 14.50 cm";
                  };
                  #6 ={
                    String = "  7. Y = 13.50 cm";
                  };
                  #7 ={
                    String = "  8. Y = 12.50 cm";
                  };
                  #8 ={
                    String = "  9. Y = 11.50 cm";
                  };
                  #9 ={
                    String = " 10. Y = 10.50 cm";
                  };
                  #10 ={
                    String = " 11. Y =  9.75 cm";
                  };
                  #11 ={
                    String = " 12. Y =  9.25 cm";
                  };
                  #12 ={
                    String = " 13. Y =  8.75 cm";
                  };
                  #13 ={
                    String = " 14. Y =  8.25 cm";
                  };
                  #14 ={
                    String = " 15. Y =  7.75 cm";
                  };
                  #15 ={
                    String = " 16. Y =  7.25 cm";
                  };
                  #16 ={
                    String = " 17. Y =  6.75 cm";
                  };
                  #17 ={
                    String = " 18. Y =  6.25 cm";
                  };
                  #18 ={
                    String = " 19. Y =  5.75 cm";
                  };
                  #19 ={
                    String = " 20. Y =  5.25 cm";
                  };
                  #20 ={
                    String = " 21. Y =  4.75 cm";
                  };
                  #21 ={
                    String = " 22. Y =  4.25 cm";
                  };
                  #22 ={
                    String = " 23. Y =  3.75 cm";
                  };
                  #23 ={
                    String = " 24. Y =  3.25 cm";
                  };
                  #24 ={
                    String = " 25. Y =  2.75 cm";
                  };
                  #25 ={
                    String = " 26. Y =  2.25 cm";
                  };
                  #26 ={
                    String = " 27. Y =  1.75 cm";
                  };
                  #27 ={
                    String = " 28. Y =  1.25 cm";
                  };
                  #28 ={
                    String = " 29. Y =  0.75 cm";
                  };
                  #29 ={
                    String = " 30. Y =  0.25 cm";
                  };
                  #30 ={
                    String = " 31. Y = -0.25 cm";
                  };
                  #31 ={
                    String = " 32. Y = -0.75 cm";
                  };
                  #32 ={
                    String = " 33. Y = -1.25 cm";
                  };
                  #33 ={
                    String = " 34. Y = -1.75 cm";
                  };
                  #34 ={
                    String = " 35. Y = -2.25 cm";
                  };
                  #35 ={
                    String = " 36. Y = -2.75 cm";
                  };
                  #36 ={
                    String = " 37. Y = -3.25 cm";
                  };
                  #37 ={
                    String = " 38. Y = -3.75 cm";
                  };
                  #38 ={
                    String = " 39. Y = -4.25 cm";
                  };
                  #39 ={
                    String = " 40. Y = -4.75 cm";
                  };
                  #40 ={
                    String = " 41. Y = -5.25 cm";
                  };
                  #41 ={
                    String = " 42. Y = -5.75 cm";
                  };
                  #42 ={
                    String = " 43. Y = -6.25 cm";
                  };
                  #43 ={
                    String = " 44. Y = -6.75 cm";
                  };
                  #44 ={
                    String = " 45. Y = -7.25 cm";
                  };
                  #45 ={
                    String = " 46. Y = -7.75 cm";
                  };
                  #46 ={
                    String = " 47. Y = -8.25 cm";
                  };
                  #47 ={
                    String = " 48. Y = -8.75 cm";
                  };
                  #48 ={
                    String = " 49. Y = -9.25 cm";
                  };
                  #49 ={
                    String = " 50. Y = -9.75 cm";
                  };
                  #50 ={
                    String = " 51. Y = -10.50 cm";
                  };
                  #51 ={
                    String = " 52. Y = -11.50 cm";
                  };
                  #52 ={
                    String = " 53. Y = -12.50 cm";
                  };
                  #53 ={
                    String = " 54. Y = -13.50 cm";
                  };
                  #54 ={
                    String = " 55. Y = -14.50 cm";
                  };
                  #55 ={
                    String = " 56. Y = -15.50 cm";
                  };
                  #56 ={
                    String = " 57. Y = -16.50 cm";
                  };
                  #57 ={
                    String = " 58. Y = -17.50 cm";
                  };
                  #58 ={
                    String = " 59. Y = -18.50 cm";
                  };
                  #59 ={
                    String = " 60. Y = -19.50 cm";
                  };
                };
                LabelFormatList ={
                  #0 ={
                    String = "%8.1f";
                  };
                  #1 ={
                    String = "%8.1f";
                  };
                };
              };
              Weight = 0.95;
              WeightLocked = 0;
              PercentOfArc = 0;
              HasSharedModifierList = 0;
              MLCTransForDisplay = 0.0135;
              CArmAngle = 0;
              TargetProjectionValid = 0;
              DoseRate = 0;
              DeliveryTime = 0;
              ODM = "Bad Value!  Programming Error\\\"";
              CPBlockingMask ={
                XDim = 1;
                YDim = 1;
                ZDim = 1;
                XMin = 0;
                YMin = 0;
                ZMin = 0;
                XMax = 0;
                YMax = 0;
                ZMax = 0;
                BinaryData = \XDR:1\;
              };
              CPBlockContourList ={
              };
              CPMLCDisplayBlockContourList ={
              };
              DoseVector = \XDR:2\;
            };
            #1 ={
              Gantry = 79;
              Couch = 355;
              Collimator = 0;
              WedgeContext ={
                WedgeName = "No Wedge";
                Orientation = "NoWedge";
                OffsetOrigin = "Patient Surface";
                OffsetDistance = -2.5;
                Angle = "No Wedge";
                MinDeliverableMU = 0;
                MaxDeliverableMU = 1e+30;
              };
              LeftJawPosition = 10;
              RightJawPosition = 10;
              TopJawPosition = 11;
              BottomJawPosition = 6;
              ModifierList ={
                BeamModifier ={
                  Name = "BeamModifier_1";
                  FixToCollimator = 1;
                  AutoBlock = 0;
                  StructureToBlock = "Manual";
                  Margin = 0;
                  InsideMode = "Expose";
                  OutsideMode = "Block";
                  ContourList ={
                    CurvePainter ={
                      Curve ={
                        RawData ={
                          NumberOfDimensions = 2;
                          NumberOfPoints = 28;
                          Points[] ={
                            0.0986848,-6.97368,
                            -0.230263,-6.18421,
                            -0.888158,-2.89474,
                            -2.13816,-2.30263,
                            -1.15132,-1.18421,
                            -2.79605,-0.986842,
                            -3.38816,-0.263158,
                            -5.88816,0.592105,
                            -5.29605,1.38158,
                            -4.57237,2.36842,
                            -3.38816,3.1579,
                            -1.80921,3.68421,
                            -0.625001,4.34211,
                            0.0986838,5.19737,
                            0.625,6.84211,
                            4.63816,6.71053,
                            5.69079,5,
                            7.26974,2.36842,
                            8.51974,2.03947,
                            9.30921,0.723685,
                            9.57237,-1.8421,
                            8.71711,-4.21053,
                            7.40132,-7.56579,
                            4.04605,-9.47368,
                            1.74342,-9.3421,
                            1.21711,-8.61842,
                            0.0328953,-6.97368,
                            0.0986848,-7.03947
                          };
                        };
                        LabelList ={
                          #0 ={
                            String = "X";
                          };
                          #1 ={
                            String = "Y";
                          };
                        };
                        RowLabelList ={
                        };
                        LabelFormatList ={
                          #0 ={
                            String = "%6.2f";
                          };
                          #1 ={
                            String = "%6.2f";
                          };
                        };
                      };
                      Color = "";
                      SliceCoordinate = 0;
                      Orientation = "Transverse";
                    };
                  };
                };
              };
              MLCLeafPositions ={
                RawData ={
                  NumberOfDimensions = 2;
                  NumberOfPoints = 60;
                  Points[] ={
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    -1.7,4.4,
                    -1.3,5.3,
                    -1,6.2,
                    -0.6,7,
                    -0.2,7.5,
                    0,7.7,
                    0.2,7.9,
                    0.3,8.1,
                    0.4,8.3,
                    0.5,8.5,
                    0.6,8.7,
                    0.7,8.9,
                    0.8,9.1,
                    1.1,9.2,
                    2.1,9.4,
                    1.7,9.6,
                    1.2,9.5,
                    3,9.5,
                    3.4,9.4,
                    4.9,9.4,
                    5.8,9.3,
                    5.4,9,
                    5,8.7,
                    4.7,7.8,
                    4.1,7.1,
                    3.2,6.8,
                    1.7,6.5,
                    0.8,6.2,
                    0.3,5.9,
                    -0.1,5.6,
                    -0.3,5.3,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0
                  };
                };
                LabelList ={
                  #0 ={
                    String = "B (X1)";
                  };
                  #1 ={
                    String = "A (X2)";
                  };
                };
                RowLabelList ={
                  #0 ={
                    String = "  1. Y = 19.50 cm";
                  };
                  #1 ={
                    String = "  2. Y = 18.50 cm";
                  };
                  #2 ={
                    String = "  3. Y = 17.50 cm";
                  };
                  #3 ={
                    String = "  4. Y = 16.50 cm";
                  };
                  #4 ={
                    String = "  5. Y = 15.50 cm";
                  };
                  #5 ={
                    String = "  6. Y = 14.50 cm";
                  };
                  #6 ={
                    String = "  7. Y = 13.50 cm";
                  };
                  #7 ={
                    String = "  8. Y = 12.50 cm";
                  };
                  #8 ={
                    String = "  9. Y = 11.50 cm";
                  };
                  #9 ={
                    String = " 10. Y = 10.50 cm";
                  };
                  #10 ={
                    String = " 11. Y =  9.75 cm";
                  };
                  #11 ={
                    String = " 12. Y =  9.25 cm";
                  };
                  #12 ={
                    String = " 13. Y =  8.75 cm";
                  };
                  #13 ={
                    String = " 14. Y =  8.25 cm";
                  };
                  #14 ={
                    String = " 15. Y =  7.75 cm";
                  };
                  #15 ={
                    String = " 16. Y =  7.25 cm";
                  };
                  #16 ={
                    String = " 17. Y =  6.75 cm";
                  };
                  #17 ={
                    String = " 18. Y =  6.25 cm";
                  };
                  #18 ={
                    String = " 19. Y =  5.75 cm";
                  };
                  #19 ={
                    String = " 20. Y =  5.25 cm";
                  };
                  #20 ={
                    String = " 21. Y =  4.75 cm";
                  };
                  #21 ={
                    String = " 22. Y =  4.25 cm";
                  };
                  #22 ={
                    String = " 23. Y =  3.75 cm";
                  };
                  #23 ={
                    String = " 24. Y =  3.25 cm";
                  };
                  #24 ={
                    String = " 25. Y =  2.75 cm";
                  };
                  #25 ={
                    String = " 26. Y =  2.25 cm";
                  };
                  #26 ={
                    String = " 27. Y =  1.75 cm";
                  };
                  #27 ={
                    String = " 28. Y =  1.25 cm";
                  };
                  #28 ={
                    String = " 29. Y =  0.75 cm";
                  };
                  #29 ={
                    String = " 30. Y =  0.25 cm";
                  };
                  #30 ={
                    String = " 31. Y = -0.25 cm";
                  };
                  #31 ={
                    String = " 32. Y = -0.75 cm";
                  };
                  #32 ={
                    String = " 33. Y = -1.25 cm";
                  };
                  #33 ={
                    String = " 34. Y = -1.75 cm";
                  };
                  #34 ={
                    String = " 35. Y = -2.25 cm";
                  };
                  #35 ={
                    String = " 36. Y = -2.75 cm";
                  };
                  #36 ={
                    String = " 37. Y = -3.25 cm";
                  };
                  #37 ={
                    String = " 38. Y = -3.75 cm";
                  };
                  #38 ={
                    String = " 39. Y = -4.25 cm";
                  };
                  #39 ={
                    String = " 40. Y = -4.75 cm";
                  };
                  #40 ={
                    String = " 41. Y = -5.25 cm";
                  };
                  #41 ={
                    String = " 42. Y = -5.75 cm";
                  };
                  #42 ={
                    String = " 43. Y = -6.25 cm";
                  };
                  #43 ={
                    String = " 44. Y = -6.75 cm";
                  };
                  #44 ={
                    String = " 45. Y = -7.25 cm";
                  };
                  #45 ={
                    String = " 46. Y = -7.75 cm";
                  };
                  #46 ={
                    String = " 47. Y = -8.25 cm";
                  };
                  #47 ={
                    String = " 48. Y = -8.75 cm";
                  };
                  #48 ={
                    String = " 49. Y = -9.25 cm";
                  };
                  #49 ={
                    String = " 50. Y = -9.75 cm";
                  };
                  #50 ={
                    String = " 51. Y = -10.50 cm";
                  };
                  #51 ={
                    String = " 52. Y = -11.50 cm";
                  };
                  #52 ={
                    String = " 53. Y = -12.50 cm";
                  };
                  #53 ={
                    String = " 54. Y = -13.50 cm";
                  };
                  #54 ={
                    String = " 55. Y = -14.50 cm";
                  };
                  #55 ={
                    String = " 56. Y = -15.50 cm";
                  };
                  #56 ={
                    String = " 57. Y = -16.50 cm";
                  };
                  #57 ={
                    String = " 58. Y = -17.50 cm";
                  };
                  #58 ={
                    String = " 59. Y = -18.50 cm";
                  };
                  #59 ={
                    String = " 60. Y = -19.50 cm";
                  };
                };
                LabelFormatList ={
                  #0 ={
                    String = "%8.1f";
                  };
                  #1 ={
                    String = "%8.1f";
                  };
                };
              };
              Weight = 0.05;
              WeightLocked = 0;
              PercentOfArc = 0;
              HasSharedModifierList = 0;
              MLCTransForDisplay = 0.0135;
              CArmAngle = 0;
              TargetProjectionValid = 0;
              DoseRate = 0;
              DeliveryTime = 0;
              ODM = "Bad Value!  Programming Error\\\"";
              CPBlockingMask ={
                XDim = 1;
                YDim = 1;
                ZDim = 1;
                XMin = 0;
                YMin = 0;
                ZMin = 0;
                XMax = 0;
                YMax = 0;
                ZMax = 0;
                BinaryData = \XDR:3\;
              };
              CPBlockContourList ={
              };
              CPMLCDisplayBlockContourList ={
              };
              DoseVector = \XDR:4\;
            };
          };
          GantryIsCCW = 0;
          MLCPushMethod = "Center";
          JawsConformance = "Static";
        };
      };
      ExtendPastTarget = 10;
      ExtendBlockPlanePastTarget = -10;
      ExtendArcPastTarget = 0;
      BevRotationAngle = 180;
      BevIsParallel = 0;
      RotationIndicatorOffset = 10;
      IMRTFilter = "Compensator";
      IMRTWedge = "Hard Wedge";
      IMRTDirection = "Gantry";
      IMRTParameterType = "Intensity Modulation";
      PrevIMRTParameterType = "None";
      PhilipsMLCTreatment = "No Export";
      PhilipsMLCBeamNumber = "No Export";
      ToshibaMLCPlanNumber = "No Export";
      ToshibaMLCBeamNumberString = "No Export";
      UseMLC = 1;
      ClipMLCDisplay = 1;
      SolidMLCDisplay = 0;
      DynamicBlocks = 0;
      Display2d = 0;
      Display3d = 1;
      CircularFieldDiameter = 0;
      ElectronApplicatorName = "None";
      SSD = 92.8167;
      AvgSSD = 92.8167;
      SSDValid = 1;
      LeftAutoSurroundMargin = 0;
      RightAutoSurroundMargin = 0;
      TopAutoSurroundMargin = 0;
      BottomAutoSurroundMargin = 0;
      AutoSurround = 0;
      BlockingMaskPixelSize = 0.05;
      BlockingMaskCutoffArea = 0.001;
      BlockAndTrayFactor = 0.03;
      TrayNumber = "";
      BlockExportName = "";
      BlockCutterFormat = "Huestis CompuCutter";
      BlockJawOverlap = 1;
      TrayFactor = 0.952;
      Compensator ={
        Name = "";
        ExportName = "";
        TrayNumber = "";
        ExportFormat = "Binary";
        IsValid = 0;
        GeneratedAutomatically = 0;
        ProtonSourceToCompensatorDistance = 50;
        ScaleWidthHeight = 0;
        Width = 5;
        Height = 5;
        ResolutionX = 0.25;
        ResolutionY = 0.25;
        DisplayResolution = 0.5;
        CompensatorHangsDown = 0;
        IncludePolyBase = 1;
        MaxIterations = 5;
        MinAllowableThickness = 0;
        MaxAllowableThickness = 10;
        RoundData = 0;
        RoundingValue = 0.001;
        CutoffHomogeneity = 0.05;
        StartOptimizationWithCurrent = 0;
        ActualHomogeneity = 0;
        OutputFactor = 1;
        Density = 1;
        EdgeOfFieldBorder = 0.5;
        InitialThickness = 0;
        MillingXScaler = 1;
        MillingYScaler = 1;
        MillingZScaler = 1;
        MillingThickness = 4;
        PositiveMilled = 3.58732e-43;
        OptimizeToMinDoseFirstIteration = 1;
        ResampleUsingLinearInterpolation = 1;
        FillOutsideMode = "Mean Thickness";
        FillOutsideThickness = 0;
        DoseCompMode = "Full 3D Dose";
        Type = "Thickness";
        PlaneDepth = 10;
        ArrayObjectName = "02 Lao Brain filter.";
        CenterAtZero = 0;
        Thickness = \XDR_COPY:5\;
        DoseMin = 0;
        DoseMax = 0;
        DoseMean = 0;
        DoseStdDev = 0;
        WetXDim = 0;
        WetYDim = 0;
      };
      CompensatorScaleFactor = 0;
      ODM ={
        Name = "";
        ExportName = "";
        ExportAscii = 0;
        ExportEffectiveODM = 0;
        IsValid = 0;
        EffectiveODMIsValid = 0;
        SourceToODMDistance = 100;
        MUForODM = 0;
        LeftJawPosition = 5;
        RightJawPosition = 5;
        TopJawPosition = 5;
        BottomJawPosition = 5;
        OFForODM = 1;
        TransCorrection = "MLC Transmission";
        GeneratedAutomatically = 0;
        BeamModified = 1;
        ODMVolume .Dimension .X = 20;
        ODMVolume .Dimension .Y = 20;
        ODMVolume .Start .X = -5;
        ODMVolume .Start .Y = -5;
        ODMVolume .VoxelSize .X = 0.5;
        ODMVolume .VoxelSize .Y = 0.5;
        ODMData = \XDR:6\;
      };
      ObjectVersion ={
        WriteVersion = "Pinnacle v16.0";
        CreateVersion = "Pinnacle v16.0";
        LoginName = "candor01";
        CreateTimeStamp = "2020-01-01 10:00:00";
        WriteTimeStamp = "2020-01-01 10:00:00";
        LastModifiedTimeStamp = "";
      };
      DoseEngine ={
        TypeName = "Adaptive Convolve";
        ConvolveHomogeneous = 0;
        FluenceHomogeneous = 0;
        FlatWaterPhantom = 0;
        FlatHomogeneous = 0;
        ElectronHomogeneous = 0;
        FluenceType = "Ray Trace";
        LongStepTuningFactor = 2;
        ShortStepLength = 0.1;
        NumberOfShortSteps = 20;
        SplitFluenceFieldSizeCutoff = 0;
        AzimuthalBinCount = 8;
        ZenithBinCount = 10;
        CumKernelRadialBinWidth = 0.005;
        SiddonCornerCutoff = 1e-07;
        NrdBinWidth = 0.1;
        AllowableDoseDiff = 5;
        HighFluenceCutoff = 20;
        LowFirstDerivCutoff = 10;
        LowSecondDerivCutoff = 20;
        HighFirstDerivCutoff = 5;
        HighSecondDerivCutoff = 10;
        AdaptiveLevels = 3;
        EnergyFlatnessCutoff = 1e-05;
        EnergyFlatnessMinimumDistance = 5.25;
        EnergyFlatnessScalingDistance = 10.5;
        EnergyFlatnessPower = 8;
        RestartIndex = 0;
        SamplesPerBatch = 10000;
        NumberOfHistoriesGoal = 1e+09;
        UncertaintyGoal = 2;
        MaxSeconds = 1e+09;
        CompletedHistories = 0;
        DoseUncertainty = 100;
        PercentDone = 0;
        ElapsedSeconds = 0;
        ElapsedCpuSeconds = 0;
        CpuPercentUtilization = 100;
        PrintBatchFiles = 0;
        PrintDataFile = 0;
        PrintEventFile = 0;
        PrintTrackFile = 0;
        StatisticsOutsideRoi = 1;
      };
      DegreesBetweenSubbeamsForDoseCalc = 5;
      IrregPointList ={
      };
      IrregPrescriptionPointName = "";
      IrregSpecifyMonitorUnits = 0;
      IrregPointPrescriptionDose = 0;
      IrregPointMonitorUnits = 0;
      IrregPointActualMonitorUnits = 0;
      IrregPointPrescribeOverall = 0;
      IrregPointNumberOfFractions = 1;
      FilmImageList ={
        FilmImage ={
          Name = "FilmImage_1";
          Type = "DRR";
          SourceToFilmDistance = 100;
          NormalDRRSpecs ={
            Energy = 0.03;
            CtToDensityTable = "New DRRa";
            BrightnessFactor = 1;
            UseRenderingLimits = 0;
            UseThreshold = 0;
            LowerThreshold = 0;
            UpperThreshold = 1;
            LinearImageLoad = 1;
            ImageWidth = 40;
            ImagePixels = 256;
            StepSize = 0.1;
            RayInterpolationMode = "Smoothest";
          };
          FastDRRSpecs ={
            Energy = 0.03;
            CtToDensityTable = "New DRRa";
            BrightnessFactor = 1;
            UseRenderingLimits = 0;
            UseThreshold = 0;
            LowerThreshold = 0;
            UpperThreshold = 1;
            LinearImageLoad = 1;
            ImageWidth = 40;
            ImagePixels = 128;
            StepSize = 0.6;
            RayInterpolationMode = "Coarse";
          };
          ApplyBoundingBox = 1;
          BoundingBox ={
            Origin ={
              X = -25;
              Y = -24.9023;
              Z = -8.75;
              Name = "";
            };
            Size ={
              X = 49.9999;
              Y = 49.9999;
              Z = 25.25;
              Name = "";
            };
            SlabMode = 0;
            SlabThickness = 3;
            Color = "red";
          };
          Film1Index = 0;
          Film2Index = 0;
          Film1Fraction = 0.5;
          Image .VolumeDisplay .Window = 4094;
          Image .VolumeDisplay .Level = 0;
          Image .VoxelSize .X = 0.15625;
          Image .VoxelSize .Y = 0.15625;
          Image .VoxelSize .Z = 1;
          Image .Dimension .X = 256;
          Image .Dimension .Y = 256;
          Image .Dimension .Z = 1;
          Image .Origin .X = -19.9219;
          Image .Origin .Y = -19.9219;
          Image .Origin .Z = 0;
          DRRValid = 0;
          HighQuality = 1;
          DRRAutoWLMode = 1;
          DCRAutoWLMode = 0;
          ApplyDrrControl = 0;
          DrrControlTable ={
            Name = "";
            NotForDoseComputation = 0;
            CanModifyDoseFlag = 1;
            Table ={
              NumberOfDimensions = 2;
              NumberOfPoints = 0;
              Points[] ={
                              };
            };
            ObjectVersion ={
              WriteVersion = "Pinnacle v16.0";
              CreateVersion = "Pinnacle v16.0";
              LoginName = "candor01";
              CreateTimeStamp = "2020-01-01 10:00:00";
              WriteTimeStamp = "2020-01-01 10:00:00";
              LastModifiedTimeStamp = "";
            };
            InOutDirection = "not set";
            RightLeftDirection = "+X";
            IsoShift = "Laser";
          };
          DrrControlType = "Ramp";
          DrrControlWindow = 550;
          DrrControlLevel = 875;
          DrrControlMaxDensity = 1.5;
          DcrControlTable ={
            Name = "Drr Ramp";
            NotForDoseComputation = 0;
            CanModifyDoseFlag = 0;
            Table ={
              NumberOfDimensions = 2;
              NumberOfPoints = 4;
              Points[] ={
                0,0,
                500,0,
                4500,1,
                4501,1
              };
            };
            ObjectVersion ={
              WriteVersion = "Pinnacle v16.0";
              CreateVersion = "Pinnacle v16.0";
              LoginName = "candor01";
              CreateTimeStamp = "2020-01-01 10:00:00";
              WriteTimeStamp = "2020-01-01 10:00:00";
              LastModifiedTimeStamp = "2020-01-01 10:00:00";
            };
            InOutDirection = "not set";
            RightLeftDirection = "+X";
            IsoShift = "Laser";
          };
          DcrControlType = "Ramp";
          DcrControlWindow = 4000;
          DcrControlLevel = 500;
          CtToOpacityTable ={
            Name = "Skin Wires AP";
            Table ={
              NumberOfDimensions = 2;
              NumberOfPoints = 9;
              Points[] ={
                0,0,
                0,0,
                263,0,
                492,0.384,
                1252,0.384,
                2540,0,
                3204,0,
                4095,0,
                4095,0
              };
            };
            ObjectVersion ={
              WriteVersion = "Pinnacle v16.0";
              CreateVersion = "Pinnacle v16.0";
              LoginName = "candor01";
              CreateTimeStamp = "2020-01-01 10:00:00";
              WriteTimeStamp = "2020-01-01 10:00:00";
              LastModifiedTimeStamp = "2020-01-01 10:00:00";
            };
          };
          UseDCR = 0;
          NumClassifBins = 0;
          ClassifList ={
          };
          DCRContrast = 1610;
          DCRBrightness = -700;
          ImageData = \XDR:7\;
        };
      };
      MonitorUnitInfo ={
        PrescriptionDose = 126.27;
        SourceToPrescriptionPointDistance = 100;
        TotalTransmissionFraction = 1;
        TransmissionDescription = "";
        PrescriptionPointDepth = 7.1833;
        PrescriptionPointRadDepth = 7.87472;
        DepthToActualPoint = 7.1833;
        SSDToActualPoint = 92.8167;
        RadDepthToActualPoint = 7.87472;
        PrescriptionPointRadDepthValid = 1;
        PrescriptionPointOffAxisDistance = 9.73291e-06;
        UnblockedFieldAreaAtSAD = 340;
        UnblockedFieldPerimeterAtSAD = 74;
        BlockedFieldAreaAtSAD = 284.75;
        IntersectFieldAreaAtSAD = 277.596;
        NormalizedDose = 1.10137;
        OffAxisRatio = 0;
        CollimatorOutputFactor = 1.02155;
        RelativeOutputFactor = 0;
        PhantomOutputFactor = 1.06549;
        OFMeasurementDepth = 0;
        OutputFactorInfo = "Interpolated using equivalent square.  OFp = 1.065";
      };
      PhotonModelDescription = "All Field Sizes";
      TarTable ={
        RawData ={
          NumberOfDimensions = 6;
          NumberOfPoints = 0;
          Points[] ={
                      };
        };
        LabelList ={
          #0 ={
            String = "Index";
          };
          #1 ={
            String = "Gantry";
          };
          #2 ={
            String = "Weight";
          };
          #3 ={
            String = "SSD (cm)";
          };
          #4 ={
            String = "Depth (cm)";
          };
          #5 ={
            String = "TAR";
          };
        };
        RowLabelList ={
        };
        LabelFormatList ={
          #0 ={
            String = " %g";
          };
          #1 ={
            String = " %5.1f";
          };
          #2 ={
            String = " %5.1f %%";
          };
          #3 ={
            String = " %5.1f";
          };
          #4 ={
            String = " %5.1f";
          };
          #5 ={
            String = " %5.3f";
          };
        };
      };
      AvgTar = 0;
      StereoDosePerMULookup = 0;
      StereoDoseDiValue = 0;
      BlocksAreLocked = 0;
      IsProtonBeamLocked = 0;
      ProtonBeam ={
      };
      DisplayList ={
        #0 ={
          Name = "Blocked Field";
          Color = "red";
          DashColor2d = "greyscale";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "On";
        };
        #1 ={
          Name = "Wedge Thick End Indicator";
          Color = "red";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
        #2 ={
          Name = "Open Field";
          Color = "red";
          DashColor2d = "greyscale";
          DisplayTransparently = 0;
          Is2dDashOn = 1;
          OnOff2d = "On";
          WireOnOff = "On";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
        #3 ={
          Name = "Wedge";
          Color = "red";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #4 ={
          Name = "Wedge at End of Arc";
          Color = "red";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #5 ={
          Name = "Central Axis Crosshair";
          Color = "red";
          DashColor2d = "greyscale";
          DisplayTransparently = 0;
          Is2dDashOn = 1;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
        #6 ={
          Name = "Arc Rotation Indicator";
          Color = "red";
          DashColor2d = "greyscale";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
        #7 ={
          Name = "Compensator";
          Color = "red";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #8 ={
          Name = "MLC Leaves";
          Color = "greyscale";
          DashColor2d = "";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
        #9 ={
          Name = "Block Plane";
          Color = "red";
          DashColor2d = "";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
        #10 ={
          Name = "Table";
          Color = "greyscale";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #11 ={
          Name = "Gantry";
          Color = "lavender";
          DashColor2d = "red";
          DisplayTransparently = 1;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #12 ={
          Name = "Accelerator";
          Color = "greyscale";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #13 ={
          Name = "Floor";
          Color = "lightblue";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #14 ={
          Name = "Opening Density Matrix";
          Color = "red";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #15 ={
          Name = "";
          Color = "";
          DashColor2d = "";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
      };
      RelyOnBolusNames = "&";
      DoseVolume = \XDR:8\;
      DoseVarVolume = \XDR:9\;
      Weight = 50;
      IsWeightLocked = 0;
      MonitorUnitsValid = 1;
      MonitorUnitsApproximate = 0;
      FieldID = "02";
      SpeedUpCollimator = 0;
      SpeedUpVirtFlouro = 0;
      DisplayMAXLeafMotion = 0;
      BeamWasSplit = 0;
      DoseRate = 600;
      IsCopyOpposeAllowed = 1;
      VerticalJawSync = 0;
      ScenarioDoseVolume0 = \XDR:10\;
      ScenarioDoseVolume1 = \XDR:11\;
      ScenarioDoseVolume2 = \XDR:12\;
      ScenarioDoseVolume3 = \XDR:13\;
      ScenarioDoseVolume4 = \XDR:14\;
      ScenarioDoseVolume5 = \XDR:15\;
      ScenarioDoseVolume6 = \XDR:16\;
      ScenarioDoseVolume7 = \XDR:17\;
      DeserializationCompleted = 0;
    };
    Beam ={
      Name = "03 R lat Brain";
      IsocenterName = "iso";
      PrescriptionName = "Brain";
      UsePoiForPrescriptionPoint = 1;
      PrescriptionPointName = "iso";
      PrescriptionPointDepth = 5;
      PrescriptionPointXOffset = 0;
      PrescriptionPointYOffset = 0;
      SpecifyDosePerMuAtPrescriptionPoint = 0;
      DosePerMuAtPrescriptionPoint = 1;
      MachineNameAndVersion = "RogueOne: 2020-01-01 10:00:00";
      Modality = "Photons";
      MachineEnergyName = "6X";
      DesiredLocalizerName = "Laser";
      ActualLocalizerName = "Laser";
      DisplayLaserMotion = "Table";
      SetBeamType = "Step & Shoot MLC";
      PrevBeamType = "Static";
      ComputationVersion = "Pinnacle v16.0";
      CPManager ={
        CPManagerObject ={
          IsGantryStartStopLocked = 1;
          IsCouchStartStopLocked = 1;
          IsCollimatorStartStopLocked = 1;
          IsLeftRightIndependent = 1;
          IsTopBottomIndependent = 1;
          NumberOfControlPoints = 2;
          ControlPointList ={
            #0 ={
              Gantry = 270;
              Couch = 355;
              Collimator = 0;
              WedgeContext ={
                WedgeName = "No Wedge";
                Orientation = "NoWedge";
                OffsetOrigin = "Patient Surface";
                OffsetDistance = -2.5;
                Angle = "No Wedge";
                MinDeliverableMU = 0;
                MaxDeliverableMU = 1e+30;
              };
              LeftJawPosition = 10;
              RightJawPosition = 10;
              TopJawPosition = 11;
              BottomJawPosition = 6;
              ModifierList ={
                BeamModifier ={
                  Name = "BeamModifier_1";
                  FixToCollimator = 1;
                  AutoBlock = 0;
                  StructureToBlock = "Manual";
                  Margin = 0;
                  InsideMode = "Expose";
                  OutsideMode = "Block";
                  ContourList ={
                    CurvePainter ={
                      Curve ={
                        RawData ={
                          NumberOfDimensions = 2;
                          NumberOfPoints = 28;
                          Points[] ={
                            12.0066,-12.6974,
                            11.4145,-6.25,
                            9.90132,-3.28948,
                            8.71711,-1.9079,
                            8.32237,-1.25,
                            7.79606,-0.723685,
                            6.67763,-5.83777e-07,
                            5.88816,0.592105,
                            5.29605,1.38158,
                            4.57237,2.36842,
                            3.38816,3.1579,
                            1.80921,3.68421,
                            0.625001,4.34211,
                            -0.0986838,5.19737,
                            -0.625,6.84211,
                            -4.63816,6.71053,
                            -6.21711,5.59211,
                            -7.46711,4.27632,
                            -8.84869,2.56579,
                            -10.6908,0.13158,
                            -12.5987,-4.86842,
                            -11.8092,-11.3816,
                            -9.76974,-13.8158,
                            -6.61185,-14.7368,
                            2.26974,-14.8026,
                            7.33553,-14.2763,
                            11.875,-12.7632,
                            12.0066,-12.6974
                          };
                        };
                        LabelList ={
                          #0 ={
                            String = "X";
                          };
                          #1 ={
                            String = "Y";
                          };
                        };
                        RowLabelList ={
                        };
                        LabelFormatList ={
                          #0 ={
                            String = "%6.2f";
                          };
                          #1 ={
                            String = "%6.2f";
                          };
                        };
                      };
                      Color = "";
                      SliceCoordinate = 0;
                      Orientation = "Transverse";
                    };
                  };
                };
              };
              MLCLeafPositions ={
                RawData ={
                  NumberOfDimensions = 2;
                  NumberOfPoints = 60;
                  Points[] ={
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,9.9,
                    10,9.5,
                    10,9,
                    10,8.6,
                    10,8.4,
                    10,7.8,
                    10,7.1,
                    10,6.4,
                    10,5.8,
                    9.9,5.4,
                    9.5,5,
                    9.1,4.7,
                    8.7,4.1,
                    8.3,3.2,
                    7.9,1.7,
                    7.5,0.8,
                    7,0.3,
                    6.6,-0.1,
                    6,-0.3,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0
                  };
                };
                LabelList ={
                  #0 ={
                    String = "B (X1)";
                  };
                  #1 ={
                    String = "A (X2)";
                  };
                };
                RowLabelList ={
                  #0 ={
                    String = "  1. Y = 19.50 cm";
                  };
                  #1 ={
                    String = "  2. Y = 18.50 cm";
                  };
                  #2 ={
                    String = "  3. Y = 17.50 cm";
                  };
                  #3 ={
                    String = "  4. Y = 16.50 cm";
                  };
                  #4 ={
                    String = "  5. Y = 15.50 cm";
                  };
                  #5 ={
                    String = "  6. Y = 14.50 cm";
                  };
                  #6 ={
                    String = "  7. Y = 13.50 cm";
                  };
                  #7 ={
                    String = "  8. Y = 12.50 cm";
                  };
                  #8 ={
                    String = "  9. Y = 11.50 cm";
                  };
                  #9 ={
                    String = " 10. Y = 10.50 cm";
                  };
                  #10 ={
                    String = " 11. Y =  9.75 cm";
                  };
                  #11 ={
                    String = " 12. Y =  9.25 cm";
                  };
                  #12 ={
                    String = " 13. Y =  8.75 cm";
                  };
                  #13 ={
                    String = " 14. Y =  8.25 cm";
                  };
                  #14 ={
                    String = " 15. Y =  7.75 cm";
                  };
                  #15 ={
                    String = " 16. Y =  7.25 cm";
                  };
                  #16 ={
                    String = " 17. Y =  6.75 cm";
                  };
                  #17 ={
                    String = " 18. Y =  6.25 cm";
                  };
                  #18 ={
                    String = " 19. Y =  5.75 cm";
                  };
                  #19 ={
                    String = " 20. Y =  5.25 cm";
                  };
                  #20 ={
                    String = " 21. Y =  4.75 cm";
                  };
                  #21 ={
                    String = " 22. Y =  4.25 cm";
                  };
                  #22 ={
                    String = " 23. Y =  3.75 cm";
                  };
                  #23 ={
                    String = " 24. Y =  3.25 cm";
                  };
                  #24 ={
                    String = " 25. Y =  2.75 cm";
                  };
                  #25 ={
                    String = " 26. Y =  2.25 cm";
                  };
                  #26 ={
                    String = " 27. Y =  1.75 cm";
                  };
                  #27 ={
                    String = " 28. Y =  1.25 cm";
                  };
                  #28 ={
                    String = " 29. Y =  0.75 cm";
                  };
                  #29 ={
                    String = " 30. Y =  0.25 cm";
                  };
                  #30 ={
                    String = " 31. Y = -0.25 cm";
                  };
                  #31 ={
                    String = " 32. Y = -0.75 cm";
                  };
                  #32 ={
                    String = " 33. Y = -1.25 cm";
                  };
                  #33 ={
                    String = " 34. Y = -1.75 cm";
                  };
                  #34 ={
                    String = " 35. Y = -2.25 cm";
                  };
                  #35 ={
                    String = " 36. Y = -2.75 cm";
                  };
                  #36 ={
                    String = " 37. Y = -3.25 cm";
                  };
                  #37 ={
                    String = " 38. Y = -3.75 cm";
                  };
                  #38 ={
                    String = " 39. Y = -4.25 cm";
                  };
                  #39 ={
                    String = " 40. Y = -4.75 cm";
                  };
                  #40 ={
                    String = " 41. Y = -5.25 cm";
                  };
                  #41 ={
                    String = " 42. Y = -5.75 cm";
                  };
                  #42 ={
                    String = " 43. Y = -6.25 cm";
                  };
                  #43 ={
                    String = " 44. Y = -6.75 cm";
                  };
                  #44 ={
                    String = " 45. Y = -7.25 cm";
                  };
                  #45 ={
                    String = " 46. Y = -7.75 cm";
                  };
                  #46 ={
                    String = " 47. Y = -8.25 cm";
                  };
                  #47 ={
                    String = " 48. Y = -8.75 cm";
                  };
                  #48 ={
                    String = " 49. Y = -9.25 cm";
                  };
                  #49 ={
                    String = " 50. Y = -9.75 cm";
                  };
                  #50 ={
                    String = " 51. Y = -10.50 cm";
                  };
                  #51 ={
                    String = " 52. Y = -11.50 cm";
                  };
                  #52 ={
                    String = " 53. Y = -12.50 cm";
                  };
                  #53 ={
                    String = " 54. Y = -13.50 cm";
                  };
                  #54 ={
                    String = " 55. Y = -14.50 cm";
                  };
                  #55 ={
                    String = " 56. Y = -15.50 cm";
                  };
                  #56 ={
                    String = " 57. Y = -16.50 cm";
                  };
                  #57 ={
                    String = " 58. Y = -17.50 cm";
                  };
                  #58 ={
                    String = " 59. Y = -18.50 cm";
                  };
                  #59 ={
                    String = " 60. Y = -19.50 cm";
                  };
                };
                LabelFormatList ={
                  #0 ={
                    String = "%8.1f";
                  };
                  #1 ={
                    String = "%8.1f";
                  };
                };
              };
              Weight = 0.95;
              WeightLocked = 0;
              PercentOfArc = 0;
              HasSharedModifierList = 0;
              MLCTransForDisplay = 0.0135;
              CArmAngle = 0;
              TargetProjectionValid = 0;
              DoseRate = 0;
              DeliveryTime = 0;
              ODM = "Bad Value!  Programming Error\\\"";
              CPBlockingMask ={
                XDim = 1;
                YDim = 1;
                ZDim = 1;
                XMin = 0;
                YMin = 0;
                ZMin = 0;
                XMax = 0;
                YMax = 0;
                ZMax = 0;
                BinaryData = \XDR:18\;
              };
              CPBlockContourList ={
              };
              CPMLCDisplayBlockContourList ={
              };
              DoseVector = \XDR:19\;
            };
            #1 ={
              Gantry = 270;
              Couch = 355;
              Collimator = 0;
              WedgeContext ={
                WedgeName = "No Wedge";
                Orientation = "NoWedge";
                OffsetOrigin = "Patient Surface";
                OffsetDistance = -2.5;
                Angle = "No Wedge";
                MinDeliverableMU = 0;
                MaxDeliverableMU = 1e+30;
              };
              LeftJawPosition = 10;
              RightJawPosition = 10;
              TopJawPosition = 11;
              BottomJawPosition = 6;
              ModifierList ={
                BeamModifier ={
                  Name = "BeamModifier_1";
                  FixToCollimator = 1;
                  AutoBlock = 0;
                  StructureToBlock = "Manual";
                  Margin = 0;
                  InsideMode = "Expose";
                  OutsideMode = "Block";
                  ContourList ={
                    CurvePainter ={
                      Curve ={
                        RawData ={
                          NumberOfDimensions = 2;
                          NumberOfPoints = 29;
                          Points[] ={
                            1.61184,-5.52632,
                            2.33553,-3.35526,
                            4.375,-1.51316,
                            8.71711,-1.9079,
                            8.32237,-1.25,
                            7.79606,-0.723685,
                            6.67763,-5.83777e-07,
                            5.88816,0.592105,
                            5.29605,1.38158,
                            4.57237,2.36842,
                            3.38816,3.1579,
                            1.80921,3.68421,
                            0.625001,4.34211,
                            -0.0986838,5.19737,
                            -0.625,6.84211,
                            -4.63816,6.71053,
                            -4.17763,5.19737,
                            -7.5329,2.63158,
                            -8.58553,2.36842,
                            -9.30921,-1.25,
                            -8.19079,-5.32895,
                            -6.01974,-8.55263,
                            -3.19079,-9.60526,
                            -0.49342,-9.47368,
                            3.32237,-8.35526,
                            5.42763,-6.71053,
                            2.33553,-6.57895,
                            1.54605,-5.52632,
                            1.61184,-5.39474
                          };
                        };
                        LabelList ={
                          #0 ={
                            String = "X";
                          };
                          #1 ={
                            String = "Y";
                          };
                        };
                        RowLabelList ={
                        };
                        LabelFormatList ={
                          #0 ={
                            String = "%6.2f";
                          };
                          #1 ={
                            String = "%6.2f";
                          };
                        };
                      };
                      Color = "";
                      SliceCoordinate = 0;
                      Orientation = "Transverse";
                    };
                  };
                };
              };
              MLCLeafPositions ={
                RawData ={
                  NumberOfDimensions = 2;
                  NumberOfPoints = 60;
                  Points[] ={
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    4.1,0.2,
                    5.5,1.9,
                    6.2,3.4,
                    6.6,4.1,
                    6.9,4.7,
                    7.2,5.4,
                    7.6,2.1,
                    7.9,1.7,
                    8.2,1.7,
                    8.3,1.9,
                    8.5,2,
                    8.6,2.2,
                    8.7,2.4,
                    8.9,3,
                    9,3.5,
                    9.2,8.6,
                    9.3,8.4,
                    9.2,7.8,
                    9.1,7.1,
                    9,6.4,
                    8.9,5.8,
                    8.8,5.4,
                    8.7,5,
                    8.6,4.7,
                    7.4,4.1,
                    6.7,3.2,
                    6.1,1.7,
                    5.4,0.8,
                    4.8,0.3,
                    4.2,-0.1,
                    4.3,-0.3,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0
                  };
                };
                LabelList ={
                  #0 ={
                    String = "B (X1)";
                  };
                  #1 ={
                    String = "A (X2)";
                  };
                };
                RowLabelList ={
                  #0 ={
                    String = "  1. Y = 19.50 cm";
                  };
                  #1 ={
                    String = "  2. Y = 18.50 cm";
                  };
                  #2 ={
                    String = "  3. Y = 17.50 cm";
                  };
                  #3 ={
                    String = "  4. Y = 16.50 cm";
                  };
                  #4 ={
                    String = "  5. Y = 15.50 cm";
                  };
                  #5 ={
                    String = "  6. Y = 14.50 cm";
                  };
                  #6 ={
                    String = "  7. Y = 13.50 cm";
                  };
                  #7 ={
                    String = "  8. Y = 12.50 cm";
                  };
                  #8 ={
                    String = "  9. Y = 11.50 cm";
                  };
                  #9 ={
                    String = " 10. Y = 10.50 cm";
                  };
                  #10 ={
                    String = " 11. Y =  9.75 cm";
                  };
                  #11 ={
                    String = " 12. Y =  9.25 cm";
                  };
                  #12 ={
                    String = " 13. Y =  8.75 cm";
                  };
                  #13 ={
                    String = " 14. Y =  8.25 cm";
                  };
                  #14 ={
                    String = " 15. Y =  7.75 cm";
                  };
                  #15 ={
                    String = " 16. Y =  7.25 cm";
                  };
                  #16 ={
                    String = " 17. Y =  6.75 cm";
                  };
                  #17 ={
                    String = " 18. Y =  6.25 cm";
                  };
                  #18 ={
                    String = " 19. Y =  5.75 cm";
                  };
                  #19 ={
                    String = " 20. Y =  5.25 cm";
                  };
                  #20 ={
                    String = " 21. Y =  4.75 cm";
                  };
                  #21 ={
                    String = " 22. Y =  4.25 cm";
                  };
                  #22 ={
                    String = " 23. Y =  3.75 cm";
                  };
                  #23 ={
                    String = " 24. Y =  3.25 cm";
                  };
                  #24 ={
                    String = " 25. Y =  2.75 cm";
                  };
                  #25 ={
                    String = " 26. Y =  2.25 cm";
                  };
                  #26 ={
                    String = " 27. Y =  1.75 cm";
                  };
                  #27 ={
                    String = " 28. Y =  1.25 cm";
                  };
                  #28 ={
                    String = " 29. Y =  0.75 cm";
                  };
                  #29 ={
                    String = " 30. Y =  0.25 cm";
                  };
                  #30 ={
                    String = " 31. Y = -0.25 cm";
                  };
                  #31 ={
                    String = " 32. Y = -0.75 cm";
                  };
                  #32 ={
                    String = " 33. Y = -1.25 cm";
                  };
                  #33 ={
                    String = " 34. Y = -1.75 cm";
                  };
                  #34 ={
                    String = " 35. Y = -2.25 cm";
                  };
                  #35 ={
                    String = " 36. Y = -2.75 cm";
                  };
                  #36 ={
                    String = " 37. Y = -3.25 cm";
                  };
                  #37 ={
                    String = " 38. Y = -3.75 cm";
                  };
                  #38 ={
                    String = " 39. Y = -4.25 cm";
                  };
                  #39 ={
                    String = " 40. Y = -4.75 cm";
                  };
                  #40 ={
                    String = " 41. Y = -5.25 cm";
                  };
                  #41 ={
                    String = " 42. Y = -5.75 cm";
                  };
                  #42 ={
                    String = " 43. Y = -6.25 cm";
                  };
                  #43 ={
                    String = " 44. Y = -6.75 cm";
                  };
                  #44 ={
                    String = " 45. Y = -7.25 cm";
                  };
                  #45 ={
                    String = " 46. Y = -7.75 cm";
                  };
                  #46 ={
                    String = " 47. Y = -8.25 cm";
                  };
                  #47 ={
                    String = " 48. Y = -8.75 cm";
                  };
                  #48 ={
                    String = " 49. Y = -9.25 cm";
                  };
                  #49 ={
                    String = " 50. Y = -9.75 cm";
                  };
                  #50 ={
                    String = " 51. Y = -10.50 cm";
                  };
                  #51 ={
                    String = " 52. Y = -11.50 cm";
                  };
                  #52 ={
                    String = " 53. Y = -12.50 cm";
                  };
                  #53 ={
                    String = " 54. Y = -13.50 cm";
                  };
                  #54 ={
                    String = " 55. Y = -14.50 cm";
                  };
                  #55 ={
                    String = " 56. Y = -15.50 cm";
                  };
                  #56 ={
                    String = " 57. Y = -16.50 cm";
                  };
                  #57 ={
                    String = " 58. Y = -17.50 cm";
                  };
                  #58 ={
                    String = " 59. Y = -18.50 cm";
                  };
                  #59 ={
                    String = " 60. Y = -19.50 cm";
                  };
                };
                LabelFormatList ={
                  #0 ={
                    String = "%8.1f";
                  };
                  #1 ={
                    String = "%8.1f";
                  };
                };
              };
              Weight = 0.05;
              WeightLocked = 0;
              PercentOfArc = 0;
              HasSharedModifierList = 0;
              MLCTransForDisplay = 0.0135;
              CArmAngle = 0;
              TargetProjectionValid = 0;
              DoseRate = 0;
              DeliveryTime = 0;
              ODM = "Bad Value!  Programming Error\\\"";
              CPBlockingMask ={
                XDim = 1;
                YDim = 1;
                ZDim = 1;
                XMin = 0;
                YMin = 0;
                ZMin = 0;
                XMax = 0;
                YMax = 0;
                ZMax = 0;
                BinaryData = \XDR:20\;
              };
              CPBlockContourList ={
              };
              CPMLCDisplayBlockContourList ={
              };
              DoseVector = \XDR:21\;
            };
          };
          GantryIsCCW = 0;
          MLCPushMethod = "Center";
          JawsConformance = "Static";
        };
      };
      ExtendPastTarget = 10;
      ExtendBlockPlanePastTarget = -10;
      ExtendArcPastTarget = 0;
      BevRotationAngle = 180;
      BevIsParallel = 0;
      RotationIndicatorOffset = 10;
      IMRTFilter = "Compensator";
      IMRTWedge = "Hard Wedge";
      IMRTDirection = "Gantry";
      IMRTParameterType = "Intensity Modulation";
      PrevIMRTParameterType = "None";
      PhilipsMLCTreatment = "No Export";
      PhilipsMLCBeamNumber = "No Export";
      ToshibaMLCPlanNumber = "No Export";
      ToshibaMLCBeamNumberString = "No Export";
      UseMLC = 1;
      ClipMLCDisplay = 1;
      SolidMLCDisplay = 0;
      DynamicBlocks = 0;
      Display2d = 0;
      Display3d = 1;
      CircularFieldDiameter = 0;
      ElectronApplicatorName = "None";
      SSD = 92.313;
      AvgSSD = 92.313;
      SSDValid = 1;
      LeftAutoSurroundMargin = 0;
      RightAutoSurroundMargin = 0;
      TopAutoSurroundMargin = 0;
      BottomAutoSurroundMargin = 0;
      AutoSurround = 0;
      BlockingMaskPixelSize = 0.05;
      BlockingMaskCutoffArea = 0.001;
      BlockAndTrayFactor = 0.03;
      TrayNumber = "";
      BlockExportName = "";
      BlockCutterFormat = "Huestis CompuCutter";
      BlockJawOverlap = 1;
      TrayFactor = 0.952;
      Compensator ={
        Name = "";
        ExportName = "";
        TrayNumber = "";
        ExportFormat = "Binary";
        IsValid = 0;
        GeneratedAutomatically = 0;
        ProtonSourceToCompensatorDistance = 50;
        ScaleWidthHeight = 0;
        Width = 5;
        Height = 5;
        ResolutionX = 0.25;
        ResolutionY = 0.25;
        DisplayResolution = 0.5;
        CompensatorHangsDown = 0;
        IncludePolyBase = 1;
        MaxIterations = 5;
        MinAllowableThickness = 0;
        MaxAllowableThickness = 10;
        RoundData = 0;
        RoundingValue = 0.001;
        CutoffHomogeneity = 0.05;
        StartOptimizationWithCurrent = 0;
        ActualHomogeneity = 0;
        OutputFactor = 1;
        Density = 1;
        EdgeOfFieldBorder = 0.5;
        InitialThickness = 0;
        MillingXScaler = 1;
        MillingYScaler = 1;
        MillingZScaler = 1;
        MillingThickness = 4;
        PositiveMilled = 3.58732e-43;
        OptimizeToMinDoseFirstIteration = 1;
        ResampleUsingLinearInterpolation = 1;
        FillOutsideMode = "Mean Thickness";
        FillOutsideThickness = 0;
        DoseCompMode = "Full 3D Dose";
        Type = "Thickness";
        PlaneDepth = 10;
        ArrayObjectName = "03 R lat Brain filter.";
        CenterAtZero = 0;
        Thickness = \XDR_COPY:22\;
        DoseMin = 0;
        DoseMax = 0;
        DoseMean = 0;
        DoseStdDev = 0;
        WetXDim = 0;
        WetYDim = 0;
      };
      CompensatorScaleFactor = 0;
      ODM ={
        Name = "";
        ExportName = "";
        ExportAscii = 0;
        ExportEffectiveODM = 0;
        IsValid = 0;
        EffectiveODMIsValid = 0;
        SourceToODMDistance = 100;
        MUForODM = 0;
        LeftJawPosition = 5;
        RightJawPosition = 5;
        TopJawPosition = 5;
        BottomJawPosition = 5;
        OFForODM = 1;
        TransCorrection = "MLC Transmission";
        GeneratedAutomatically = 0;
        BeamModified = 1;
        ODMVolume .Dimension .X = 20;
        ODMVolume .Dimension .Y = 20;
        ODMVolume .Start .X = -5;
        ODMVolume .Start .Y = -5;
        ODMVolume .VoxelSize .X = 0.5;
        ODMVolume .VoxelSize .Y = 0.5;
        ODMData = \XDR:23\;
      };
      ObjectVersion ={
        WriteVersion = "Pinnacle v16.0";
        CreateVersion = "Pinnacle v16.0";
        LoginName = "candor01";
        CreateTimeStamp = "2020-01-01 10:00:00";
        WriteTimeStamp = "2020-01-01 10:00:00";
        LastModifiedTimeStamp = "";
      };
      DoseEngine ={
        TypeName = "Adaptive Convolve";
        ConvolveHomogeneous = 0;
        FluenceHomogeneous = 0;
        FlatWaterPhantom = 0;
        FlatHomogeneous = 0;
        ElectronHomogeneous = 0;
        FluenceType = "Ray Trace";
        LongStepTuningFactor = 2;
        ShortStepLength = 0.1;
        NumberOfShortSteps = 20;
        SplitFluenceFieldSizeCutoff = 0;
        AzimuthalBinCount = 8;
        ZenithBinCount = 10;
        CumKernelRadialBinWidth = 0.005;
        SiddonCornerCutoff = 1e-07;
        NrdBinWidth = 0.1;
        AllowableDoseDiff = 5;
        HighFluenceCutoff = 20;
        LowFirstDerivCutoff = 10;
        LowSecondDerivCutoff = 20;
        HighFirstDerivCutoff = 5;
        HighSecondDerivCutoff = 10;
        AdaptiveLevels = 3;
        EnergyFlatnessCutoff = 1e-05;
        EnergyFlatnessMinimumDistance = 5.25;
        EnergyFlatnessScalingDistance = 10.5;
        EnergyFlatnessPower = 8;
        RestartIndex = 0;
        SamplesPerBatch = 10000;
        NumberOfHistoriesGoal = 1e+09;
        UncertaintyGoal = 2;
        MaxSeconds = 1e+09;
        CompletedHistories = 0;
        DoseUncertainty = 100;
        PercentDone = 0;
        ElapsedSeconds = 0;
        ElapsedCpuSeconds = 0;
        CpuPercentUtilization = 100;
        PrintBatchFiles = 0;
        PrintDataFile = 0;
        PrintEventFile = 0;
        PrintTrackFile = 0;
        StatisticsOutsideRoi = 1;
      };
      DegreesBetweenSubbeamsForDoseCalc = 5;
      IrregPointList ={
      };
      IrregPrescriptionPointName = "";
      IrregSpecifyMonitorUnits = 0;
      IrregPointPrescriptionDose = 0;
      IrregPointMonitorUnits = 0;
      IrregPointActualMonitorUnits = 0;
      IrregPointPrescribeOverall = 0;
      IrregPointNumberOfFractions = 1;
      FilmImageList ={
        FilmImage ={
          Name = "FilmImage_1";
          Type = "DRR";
          SourceToFilmDistance = 100;
          NormalDRRSpecs ={
            Energy = 0.03;
            CtToDensityTable = "New DRRa";
            BrightnessFactor = 1;
            UseRenderingLimits = 0;
            UseThreshold = 0;
            LowerThreshold = 0;
            UpperThreshold = 1;
            LinearImageLoad = 1;
            ImageWidth = 40;
            ImagePixels = 256;
            StepSize = 0.1;
            RayInterpolationMode = "Smoothest";
          };
          FastDRRSpecs ={
            Energy = 0.03;
            CtToDensityTable = "New DRRa";
            BrightnessFactor = 1;
            UseRenderingLimits = 0;
            UseThreshold = 0;
            LowerThreshold = 0;
            UpperThreshold = 1;
            LinearImageLoad = 1;
            ImageWidth = 40;
            ImagePixels = 128;
            StepSize = 0.6;
            RayInterpolationMode = "Coarse";
          };
          ApplyBoundingBox = 1;
          BoundingBox ={
            Origin ={
              X = -25;
              Y = -24.9023;
              Z = -8.75;
              Name = "";
            };
            Size ={
              X = 49.9999;
              Y = 49.9999;
              Z = 25.25;
              Name = "";
            };
            SlabMode = 0;
            SlabThickness = 3;
            Color = "red";
          };
          Film1Index = 0;
          Film2Index = 0;
          Film1Fraction = 0.5;
          Image .VolumeDisplay .Window = 4094;
          Image .VolumeDisplay .Level = 0;
          Image .VoxelSize .X = 0.15625;
          Image .VoxelSize .Y = 0.15625;
          Image .VoxelSize .Z = 1;
          Image .Dimension .X = 256;
          Image .Dimension .Y = 256;
          Image .Dimension .Z = 1;
          Image .Origin .X = -19.9219;
          Image .Origin .Y = -19.9219;
          Image .Origin .Z = 0;
          DRRValid = 0;
          HighQuality = 1;
          DRRAutoWLMode = 1;
          DCRAutoWLMode = 0;
          ApplyDrrControl = 0;
          DrrControlTable ={
            Name = "";
            NotForDoseComputation = 0;
            CanModifyDoseFlag = 1;
            Table ={
              NumberOfDimensions = 2;
              NumberOfPoints = 0;
              Points[] ={
                              };
            };
            ObjectVersion ={
              WriteVersion = "Pinnacle v16.0";
              CreateVersion = "Pinnacle v16.0";
              LoginName = "candor01";
              CreateTimeStamp = "2020-01-01 10:00:00";
              WriteTimeStamp = "2020-01-01 10:00:00";
              LastModifiedTimeStamp = "";
            };
            InOutDirection = "not set";
            RightLeftDirection = "+X";
            IsoShift = "Laser";
          };
          DrrControlType = "Ramp";
          DrrControlWindow = 550;
          DrrControlLevel = 875;
          DrrControlMaxDensity = 1.5;
          DcrControlTable ={
            Name = "Drr Ramp";
            NotForDoseComputation = 0;
            CanModifyDoseFlag = 0;
            Table ={
              NumberOfDimensions = 2;
              NumberOfPoints = 4;
              Points[] ={
                0,0,
                500,0,
                4500,1,
                4501,1
              };
            };
            ObjectVersion ={
              WriteVersion = "Pinnacle v16.0";
              CreateVersion = "Pinnacle v16.0";
              LoginName = "candor01";
              CreateTimeStamp = "2020-01-01 10:00:00";
              WriteTimeStamp = "2020-01-01 10:00:00";
              LastModifiedTimeStamp = "2020-01-01 10:00:00";
            };
            InOutDirection = "not set";
            RightLeftDirection = "+X";
            IsoShift = "Laser";
          };
          DcrControlType = "Ramp";
          DcrControlWindow = 4000;
          DcrControlLevel = 500;
          CtToOpacityTable ={
            Name = "Skin Wires AP";
            Table ={
              NumberOfDimensions = 2;
              NumberOfPoints = 9;
              Points[] ={
                0,0,
                0,0,
                263,0,
                492,0.384,
                1252,0.384,
                2540,0,
                3204,0,
                4095,0,
                4095,0
              };
            };
            ObjectVersion ={
              WriteVersion = "Pinnacle v16.0";
              CreateVersion = "Pinnacle v16.0";
              LoginName = "candor01";
              CreateTimeStamp = "2020-01-01 10:00:00";
              WriteTimeStamp = "2020-01-01 10:00:00";
              LastModifiedTimeStamp = "2020-01-01 10:00:00";
            };
          };
          UseDCR = 0;
          NumClassifBins = 0;
          ClassifList ={
          };
          DCRContrast = 1610;
          DCRBrightness = -700;
          ImageData = \XDR:24\;
        };
      };
      MonitorUnitInfo ={
        PrescriptionDose = 126.56;
        SourceToPrescriptionPointDistance = 100;
        TotalTransmissionFraction = 1;
        TransmissionDescription = "";
        PrescriptionPointDepth = 7.68694;
        PrescriptionPointRadDepth = 8.12132;
        DepthToActualPoint = 7.68694;
        SSDToActualPoint = 92.313;
        RadDepthToActualPoint = 8.12134;
        PrescriptionPointRadDepthValid = 1;
        PrescriptionPointOffAxisDistance = 1.78814e-06;
        UnblockedFieldAreaAtSAD = 340;
        UnblockedFieldPerimeterAtSAD = 74;
        BlockedFieldAreaAtSAD = 284.75;
        IntersectFieldAreaAtSAD = 274.821;
        NormalizedDose = 1.08824;
        OffAxisRatio = 0;
        CollimatorOutputFactor = 1.02155;
        RelativeOutputFactor = 0;
        PhantomOutputFactor = 1.06549;
        OFMeasurementDepth = 0;
        OutputFactorInfo = "Interpolated using equivalent square.  OFp = 1.065";
      };
      PhotonModelDescription = "All Field Sizes";
      TarTable ={
        RawData ={
          NumberOfDimensions = 6;
          NumberOfPoints = 0;
          Points[] ={
                      };
        };
        LabelList ={
          #0 ={
            String = "Index";
          };
          #1 ={
            String = "Gantry";
          };
          #2 ={
            String = "Weight";
          };
          #3 ={
            String = "SSD (cm)";
          };
          #4 ={
            String = "Depth (cm)";
          };
          #5 ={
            String = "TAR";
          };
        };
        RowLabelList ={
        };
        LabelFormatList ={
          #0 ={
            String = " %g";
          };
          #1 ={
            String = " %5.1f";
          };
          #2 ={
            String = " %5.1f %%";
          };
          #3 ={
            String = " %5.1f";
          };
          #4 ={
            String = " %5.1f";
          };
          #5 ={
            String = " %5.3f";
          };
        };
      };
      AvgTar = 0;
      StereoDosePerMULookup = 0;
      StereoDoseDiValue = 0;
      BlocksAreLocked = 0;
      IsProtonBeamLocked = 0;
      ProtonBeam ={
      };
      DisplayList ={
        #0 ={
          Name = "Blocked Field";
          Color = "green";
          DashColor2d = "greyscale";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "On";
        };
        #1 ={
          Name = "Wedge Thick End Indicator";
          Color = "green";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
        #2 ={
          Name = "Open Field";
          Color = "green";
          DashColor2d = "greyscale";
          DisplayTransparently = 0;
          Is2dDashOn = 1;
          OnOff2d = "On";
          WireOnOff = "On";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
        #3 ={
          Name = "Wedge";
          Color = "green";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #4 ={
          Name = "Wedge at End of Arc";
          Color = "green";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #5 ={
          Name = "Central Axis Crosshair";
          Color = "green";
          DashColor2d = "greyscale";
          DisplayTransparently = 0;
          Is2dDashOn = 1;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
        #6 ={
          Name = "Arc Rotation Indicator";
          Color = "green";
          DashColor2d = "greyscale";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
        #7 ={
          Name = "Compensator";
          Color = "green";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #8 ={
          Name = "MLC Leaves";
          Color = "greyscale";
          DashColor2d = "";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
        #9 ={
          Name = "Block Plane";
          Color = "green";
          DashColor2d = "";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
        #10 ={
          Name = "Table";
          Color = "greyscale";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #11 ={
          Name = "Gantry";
          Color = "lavender";
          DashColor2d = "red";
          DisplayTransparently = 1;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #12 ={
          Name = "Accelerator";
          Color = "greyscale";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #13 ={
          Name = "Floor";
          Color = "lightblue";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #14 ={
          Name = "Opening Density Matrix";
          Color = "green";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #15 ={
          Name = "";
          Color = "";
          DashColor2d = "";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
      };
      RelyOnBolusNames = "&";
      DoseVolume = \XDR:25\;
      DoseVarVolume = \XDR:26\;
      Weight = 50;
      IsWeightLocked = 0;
      MonitorUnitsValid = 1;
      MonitorUnitsApproximate = 0;
      FieldID = "03";
      SpeedUpCollimator = 0;
      SpeedUpVirtFlouro = 0;
      DisplayMAXLeafMotion = 0;
      BeamWasSplit = 0;
      DoseRate = 600;
      IsCopyOpposeAllowed = 1;
      VerticalJawSync = 0;
      ScenarioDoseVolume0 = \XDR:27\;
      ScenarioDoseVolume1 = \XDR:28\;
      ScenarioDoseVolume2 = \XDR:29\;
      ScenarioDoseVolume3 = \XDR:30\;
      ScenarioDoseVolume4 = \XDR:31\;
      ScenarioDoseVolume5 = \XDR:32\;
      ScenarioDoseVolume6 = \XDR:33\;
      ScenarioDoseVolume7 = \XDR:34\;
      DeserializationCompleted = 0;
    };
  };
  BrachyManager ={
    DefaultSegmentTipTineLength = 0.5;
    SourceGroupList ={
    };
    SeedArray ={
      TemplateName = "BrachyTemplate_1";
      Color = "Yellow";
      SingleSeedCathColor = "Yellow";
      MultiSeedCathColor = "Red";
      CoordSystem ={
        OriginX = 1.66;
        OriginY = 19.38;
        OriginZ = 0;
        XAxisX = 7.66;
        XAxisY = 19.38;
        XAxisZ = 0;
        YAxisX = 1.66;
        YAxisY = 24.88;
        YAxisZ = 0;
        ZAxisX = 1.66;
        ZAxisY = 19.38;
        ZAxisZ = 0.5;
        XLength = 12;
        YLength = 11;
        ZLength = 1;
      };
      PositionDefined = 1;
      IsLocked = 0;
    };
    SourceList ={
    };
    CatheterList ={
    };
    FilmAP = 1;
    FilmRL = 1;
    BrachyFilm1 ={
      Name = "AP Film";
      SFD = 100;
      SAD = 100;
      Couch = 0;
      Gantry = 0;
      Collimator = 0;
      Magnification = 1;
      ShiftDistance = 25;
      Target ={
        RawData ={
          NumberOfDimensions = 2;
          NumberOfPoints = 0;
          Points[] ={
                      };
        };
        LabelList ={
          #0 ={
            String = "X";
          };
          #1 ={
            String = "Y";
          };
        };
        RowLabelList ={
        };
        LabelFormatList ={
          #0 ={
            String = " %g";
          };
          #1 ={
            String = " %g";
          };
        };
      };
    };
    BrachyFilm2 ={
      Name = "LR Film";
      SFD = 100;
      SAD = 100;
      Couch = 0;
      Gantry = 0;
      Collimator = 0;
      Magnification = 1;
      ShiftDistance = 25;
      Target ={
        RawData ={
          NumberOfDimensions = 2;
          NumberOfPoints = 0;
          Points[] ={
                      };
        };
        LabelList ={
          #0 ={
            String = "X";
          };
          #1 ={
            String = "Y";
          };
        };
        RowLabelList ={
        };
        LabelFormatList ={
          #0 ={
            String = " %g";
          };
          #1 ={
            String = " %g";
          };
        };
      };
    };
    ReconstructionList ={
    };
    MachineNameAndVersion = "None";
    SourcePositionList = "Cath Tip to Source Center";
    NumberOfSourcesToLoad = 10;
    DoubleLoadTip = 0;
    DoubleLoadBase = 0;
    TipToFirstSourceSpace = 0.5;
    InterSourceSpace = 2;
    TemplateRows = 4;
    TemplateColumns = 4;
    TemplateRowSpace = 2;
    TemplateColumnSpace = 2;
    TemplateCatheterLength = 30;
    TemplateOffsetGap = 0;
    TemplateAutoLoadCatheters = 0;
    TemplateGuideCathCentered = 1;
    ReconstructionErrorTolerance = 0.5;
    FilmReconstructionList = "Orthogonal";
    TargetReconstructionList = "POIs";
    CommonAxisErrorTolerance = 0.5;
    HighlightCurrentSource = 1;
    HighlightCurrentCath = 0;
    DrawCurrentCathOnly = 0;
    StereoShiftIsVertical = 1;
  };
  BrachyPrescriptionMethod = "Dose Rate";
  BrachyImplantDateAndTime ={
    UTC_ISODateAndTimeString = "2020-01-01 10:00:00Z";
  };
  BrachyImplantDuration = 24;
  BrachyImplantDurationUnits = "Hours";
  MaximumDoseForSeeds = 0;
  PrintSubbeams = 1;
  PrintPois = 1;
  PrintRois = 1;
  PrintBrachyByCatheter = 0;
  PrintBrachyByGroup = 0;
  PrintMLC = 1;
  PrintMLCIrreg = 0;
  UseTrialForTreatment = 1;
  TargetOptGoalList ={
  };
  OAROptGoalList ={
  };
  APEngineSettings ={
    MaxIterations = 50;
    IsEngineTypeBiological = 1;
    TuningBalance = 11;
    DoseFallOffMargin = 2.6;
    HotspotMaximumGoal = 107;
    UseColdSpotROI = 1;
  };
  PlanarDoseList ={
  };
  EPIDDoseList ={
  };
  BeamPageSelection = "Modality";
  DisplayState = "Normal";
  BrachyDoseComputationVersion = "Unknown";
  BrachyDose = \XDR:35\;
  BeamPoiWeightOptimizer ={
    PointList ={
    };
    SimplexOptimizer ={
      MaxIterations = 10000;
      Alpha = 1;
      Beta = 0.5;
      Gamma = 2;
      NumDimensions = 3;
      SolutionTolerance = 0.0001;
      SimplexScale = 50;
      NumberOfRestarts = 5;
    };
    NegativeMUPenalty = 1000;
    ModifyPrescription = 0;
    Grouping = "No Grouping";
    NumberOfBeamsPenalty = 0;
  };
  ObjectVersion ={
    WriteVersion = "Pinnacle v16.0";
    CreateVersion = "Unknown";
    LoginName = "candor01";
    CreateTimeStamp = "Unknown";
    WriteTimeStamp = "2020-01-01 10:00:00";
    LastModifiedTimeStamp = "Unknown";
  };
  MLCExport ={
    PatientID = "";
    ExportFormat = "Varian";
    VarianVersion = "F";
    VarianTolerance = 0.2;
    PhilipsMLCName = "MLC_1";
    PhilipsAccessory = 0;
    PhilipsFitmentCode = 1;
    ToshibaDoseRate = "Normal";
  };
  CoordRefPoint ={
    Name = "Coord Ref";
    XCoord = -0.0977211;
    YCoord = -4.19617e-05;
    ZCoord = 3.5;
    XRotation = 0;
    YRotation = 0;
    ZRotation = 0;
    Radius = 1;
    Color = "green";
    CoordSys = "CT";
    CoordinateFormat = "%6.2f";
    Display2d = "Off";
    Display3d = "Off";
    ObjectVersion ={
      WriteVersion = "Pinnacle v16.0";
      CreateVersion = "Pinnacle v16.0";
      LoginName = "candor01";
      CreateTimeStamp = "2020-01-01 10:00:00";
      WriteTimeStamp = "2020-01-01 10:00:00";
      LastModifiedTimeStamp = "";
    };
    VolumeName = "LAST^FIRST^M";
    PoiInterpretedType = "MARKER";
    PoiDisplayOnOtherVolumes = 1;
    IsLocked = 0;
  };
  UseCoordRefPoint = 0;
  CourseID = 99;
  ToleranceTable = 0;
  AlwaysDisplay2dCouchPosition = 0;
  AlwaysDisplay3dCouchPosition = 0;
  ExportPlanarDoseAscii = 0;
  PrintIMRTSummary = 0;
  PrintIMPTSummary = 0;
  PrimaryVOI ={
    Origin ={
      X = -25;
      Y = -24.9023;
      Z = -8.75;
      Name = "";
    };
    Size ={
      X = 49.9999;
      Y = 49.9999;
      Z = 25.25;
      Name = "";
    };
    SlabMode = 0;
    SlabThickness = 3;
    Color = "skyblue";
  };
  MultipleMachines = 0;
  CheckCtToDensityExtension = 0;
  SeriesNumber = 1;
  SeriesDescription = "";
  CTScanner = "York Cancer Center";
  HasDose = 0;
  IncludeDICOMCoordInReport = 0;
  IncludeShiftsFromFirstInReport = 0;
  IsAbsoluteLaserMode = 1;
  LastLaserTransmissionMode = "--";
  APError = 0;
  LRError = 0;
  ISError = 0;
  DepthError = 0;
  FluenceDoseSpread = 4;
  SingleGaussianDoseSpread = 4;
  DoubleGaussianDoseSpread = 3;
  NuclearDoseSpread = 3;
  MinDoseThreshold = 1e-06;
  IsRO = 0;
  AntPostWeight = 25;
  LateralWeight = 25;
  InfSupWeight = 25;
  DepthWeight = 25;
  NominalWeight = 50;
  PDM ={
    PDVolList ={
    };
    ScaleMode = 1;
  };
};
Trial ={
  Name = "Trial_2";
  PatientRepresentation ={
    PatientVolumeName = "LAST^FIRST^M";
    CtToDensityName = "CT_DENSITY_01";
    CtToDensityVersion = "2020-01-01 10:00:00";
    DMTableName = "Standard Patient";
    DMTableVersion = "2020-01-01 10:00:00";
    TopZPadding = 0;
    BottomZPadding = 0;
    HighResZSpacingForVariable = 0.2;
    OutsidePatientIsCtNumber = 0;
    OutsidePatientAirThreshold = 0.6;
    CtToDensityTableAccepted = 1;
    CtToDensityTableExtended = 0;
    CtToStoppingPowerTableName = "LinearStoppingPowerTable";
    CtToStoppingPowerVersion = "2020-01-01 10:00:00";
    CtToStoppingPowerExtended = 0;
    CtToStoppingPowerAccepted = 1;
  };
  DoseGrid .VoxelSize .X = 0.3;
  DoseGrid .VoxelSize .Y = 0.3;
  DoseGrid .VoxelSize .Z = 0.3;
  DoseGrid .Dimension .X = 93;
  DoseGrid .Dimension .Y = 110;
  DoseGrid .Dimension .Z = 89;
  DoseGrid .Origin .X = -14.9044;
  DoseGrid .Origin .Y = -17.5729;
  DoseGrid .Origin .Z = -10.7747;
  DoseGrid .VolRotDelta .X = 0;
  DoseGrid .VolRotDelta .Y = 0;
  DoseGrid .VolRotDelta .Z = 0;
  DoseGrid .Display2d = 1;
  DoseGrid .DoseSummationType = 1;
  DoseStartSlice = 49;
  DoseEndSlice = 49;
  ScenarioDoseGridRes = 0.4;
  ScenarioDoseGridDimX = 0;
  ScenarioDoseGridDimY = 0;
  ScenarioDoseGridDimZ = 0;
  SuppressDoseGridSumming = 0;
  TrialUsedForDoseDisplayOnly = 0;
  RecordDoseData = \XDR:0\;
  FluenceGridResolution = 0.4;
  FluenceGridMatchesDoseGrid = 1;
  SourceToFilmDistance = 140;
  ScreenToPrinterZoomFactor = 1.33333;
  MCMaxSeconds = 3600;
  MCGlobalUncertaintyType = "mean_error_over_max_value";
  MCStatisticsThreshold = 50;
  MCUncertaintyGoal = 2;
  RemoveCouchFromScan = 1;
  CouchRemovalYCoordinate = -21.1183;
  Display2dCouchPosition = 1;
  Display3dCouchPosition = 1;
  CouchDisplayColor = "purple";
  RecomputeDensity = 1;
  PhysicsPlan = 0;
  ComputeRelativeDose = 0;
  RelativeDoseNormPointName = "<None>";
  RelativeDoseNormValue = 0;
  RelativeDoseNormValid = 0;
  RelativeDoseReferenceFieldName = "<None>";
  LastRelativeDoseReferenceField = "";
  RelativeDoseComputationStatus = "Uncomputed.";
  IsodoseNormPointName = "<None>";
  MaxDosePoint .Display2d = "Label";
  UseActualPatientForIrreg = 0;
  MaxDosePoint .Color = "green";
  PrescriptionList ={
    Prescription ={
      Name = "Brain";
      RequestedMonitorUnitsPerFraction = 280;
      PrescriptionDose = 250;
      PrescriptionPercent = 99;
      NumberOfFractions = 12;
      PrescriptionPoint = "iso";
      Method = "Prescribe";
      NormalizationMethod = "Point Dose";
      PrescriptionPeriod = "Per Fraction";
      WeightsProportionalTo = "Point Dose";
      DoseUncertainty = 1;
      PrescriptionUncertainty = 1;
      DoseUncertaintyValid = 0;
      PrescripUncertaintyValid = 0;
      Color = "red";
    };
  };
  BeamList ={
    Beam ={
      Name = "02 Lao Brain";
      IsocenterName = "iso";
      PrescriptionName = "Brain";
      UsePoiForPrescriptionPoint = 1;
      PrescriptionPointName = "iso";
      PrescriptionPointDepth = 5;
      PrescriptionPointXOffset = 0;
      PrescriptionPointYOffset = 0;
      SpecifyDosePerMuAtPrescriptionPoint = 0;
      DosePerMuAtPrescriptionPoint = 1;
      MachineNameAndVersion = "RogueOne: 2020-01-01 10:00:00";
      Modality = "Photons";
      MachineEnergyName = "6X";
      DesiredLocalizerName = "Laser";
      ActualLocalizerName = "Laser";
      DisplayLaserMotion = "Table";
      SetBeamType = "Step & Shoot MLC";
      PrevBeamType = "Static";
      ComputationVersion = "Pinnacle v16.0";
      CPManager ={
        CPManagerObject ={
          IsGantryStartStopLocked = 1;
          IsCouchStartStopLocked = 1;
          IsCollimatorStartStopLocked = 1;
          IsLeftRightIndependent = 1;
          IsTopBottomIndependent = 1;
          NumberOfControlPoints = 2;
          ControlPointList ={
            #0 ={
              Gantry = 79;
              Couch = 355;
              Collimator = 0;
              WedgeContext ={
                WedgeName = "No Wedge";
                Orientation = "NoWedge";
                OffsetOrigin = "Patient Surface";
                OffsetDistance = -2.5;
                Angle = "No Wedge";
                MinDeliverableMU = 0;
                MaxDeliverableMU = 1e+30;
              };
              LeftJawPosition = 10;
              RightJawPosition = 10;
              TopJawPosition = 11;
              BottomJawPosition = 6;
              ModifierList ={
                BeamModifier ={
                  Name = "BeamModifier_1";
                  FixToCollimator = 1;
                  AutoBlock = 0;
                  StructureToBlock = "Manual";
                  Margin = 0;
                  InsideMode = "Expose";
                  OutsideMode = "Block";
                  ContourList ={
                    CurvePainter ={
                      Curve ={
                        RawData ={
                          NumberOfDimensions = 2;
                          NumberOfPoints = 28;
                          Points[] ={
                            -12.0066,-12.6974,
                            -11.4145,-6.25,
                            -9.90132,-3.28948,
                            -8.71711,-1.9079,
                            -8.32237,-1.25,
                            -7.79606,-0.723685,
                            -6.67763,-5.83777e-07,
                            -5.88816,0.592105,
                            -5.29605,1.38158,
                            -4.57237,2.36842,
                            -3.38816,3.1579,
                            -1.80921,3.68421,
                            -0.625001,4.34211,
                            0.0986838,5.19737,
                            0.625,6.84211,
                            4.63816,6.71053,
                            6.21711,5.59211,
                            7.46711,4.27632,
                            8.84869,2.56579,
                            10.6908,0.13158,
                            12.5987,-4.86842,
                            11.8092,-11.3816,
                            9.76974,-13.8158,
                            6.61185,-14.7368,
                            -2.26974,-14.8026,
                            -7.33553,-14.2763,
                            -11.875,-12.7632,
                            -12.0066,-12.6974
                          };
                        };
                        LabelList ={
                          #0 ={
                            String = "X";
                          };
                          #1 ={
                            String = "Y";
                          };
                        };
                        RowLabelList ={
                        };
                        LabelFormatList ={
                          #0 ={
                            String = "%6.2f";
                          };
                          #1 ={
                            String = "%6.2f";
                          };
                        };
                      };
                      Color = "";
                      SliceCoordinate = 0;
                      Orientation = "Transverse";
                    };
                  };
                };
              };
              MLCLeafPositions ={
                RawData ={
                  NumberOfDimensions = 2;
                  NumberOfPoints = 60;
                  Points[] ={
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    9.9,10,
                    9.5,10,
                    9,10,
                    8.6,10,
                    8.4,10,
                    7.8,10,
                    7.1,10,
                    6.4,10,
                    5.8,10,
                    5.4,9.9,
                    5,9.5,
                    4.7,9.1,
                    4.1,8.7,
                    3.2,8.3,
                    1.7,7.9,
                    0.8,7.5,
                    0.3,7,
                    -0.1,6.6,
                    -0.3,6,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0
                  };
                };
                LabelList ={
                  #0 ={
                    String = "B (X1)";
                  };
                  #1 ={
                    String = "A (X2)";
                  };
                };
                RowLabelList ={
                  #0 ={
                    String = "  1. Y = 19.50 cm";
                  };
                  #1 ={
                    String = "  2. Y = 18.50 cm";
                  };
                  #2 ={
                    String = "  3. Y = 17.50 cm";
                  };
                  #3 ={
                    String = "  4. Y = 16.50 cm";
                  };
                  #4 ={
                    String = "  5. Y = 15.50 cm";
                  };
                  #5 ={
                    String = "  6. Y = 14.50 cm";
                  };
                  #6 ={
                    String = "  7. Y = 13.50 cm";
                  };
                  #7 ={
                    String = "  8. Y = 12.50 cm";
                  };
                  #8 ={
                    String = "  9. Y = 11.50 cm";
                  };
                  #9 ={
                    String = " 10. Y = 10.50 cm";
                  };
                  #10 ={
                    String = " 11. Y =  9.75 cm";
                  };
                  #11 ={
                    String = " 12. Y =  9.25 cm";
                  };
                  #12 ={
                    String = " 13. Y =  8.75 cm";
                  };
                  #13 ={
                    String = " 14. Y =  8.25 cm";
                  };
                  #14 ={
                    String = " 15. Y =  7.75 cm";
                  };
                  #15 ={
                    String = " 16. Y =  7.25 cm";
                  };
                  #16 ={
                    String = " 17. Y =  6.75 cm";
                  };
                  #17 ={
                    String = " 18. Y =  6.25 cm";
                  };
                  #18 ={
                    String = " 19. Y =  5.75 cm";
                  };
                  #19 ={
                    String = " 20. Y =  5.25 cm";
                  };
                  #20 ={
                    String = " 21. Y =  4.75 cm";
                  };
                  #21 ={
                    String = " 22. Y =  4.25 cm";
                  };
                  #22 ={
                    String = " 23. Y =  3.75 cm";
                  };
                  #23 ={
                    String = " 24. Y =  3.25 cm";
                  };
                  #24 ={
                    String = " 25. Y =  2.75 cm";
                  };
                  #25 ={
                    String = " 26. Y =  2.25 cm";
                  };
                  #26 ={
                    String = " 27. Y =  1.75 cm";
                  };
                  #27 ={
                    String = " 28. Y =  1.25 cm";
                  };
                  #28 ={
                    String = " 29. Y =  0.75 cm";
                  };
                  #29 ={
                    String = " 30. Y =  0.25 cm";
                  };
                  #30 ={
                    String = " 31. Y = -0.25 cm";
                  };
                  #31 ={
                    String = " 32. Y = -0.75 cm";
                  };
                  #32 ={
                    String = " 33. Y = -1.25 cm";
                  };
                  #33 ={
                    String = " 34. Y = -1.75 cm";
                  };
                  #34 ={
                    String = " 35. Y = -2.25 cm";
                  };
                  #35 ={
                    String = " 36. Y = -2.75 cm";
                  };
                  #36 ={
                    String = " 37. Y = -3.25 cm";
                  };
                  #37 ={
                    String = " 38. Y = -3.75 cm";
                  };
                  #38 ={
                    String = " 39. Y = -4.25 cm";
                  };
                  #39 ={
                    String = " 40. Y = -4.75 cm";
                  };
                  #40 ={
                    String = " 41. Y = -5.25 cm";
                  };
                  #41 ={
                    String = " 42. Y = -5.75 cm";
                  };
                  #42 ={
                    String = " 43. Y = -6.25 cm";
                  };
                  #43 ={
                    String = " 44. Y = -6.75 cm";
                  };
                  #44 ={
                    String = " 45. Y = -7.25 cm";
                  };
                  #45 ={
                    String = " 46. Y = -7.75 cm";
                  };
                  #46 ={
                    String = " 47. Y = -8.25 cm";
                  };
                  #47 ={
                    String = " 48. Y = -8.75 cm";
                  };
                  #48 ={
                    String = " 49. Y = -9.25 cm";
                  };
                  #49 ={
                    String = " 50. Y = -9.75 cm";
                  };
                  #50 ={
                    String = " 51. Y = -10.50 cm";
                  };
                  #51 ={
                    String = " 52. Y = -11.50 cm";
                  };
                  #52 ={
                    String = " 53. Y = -12.50 cm";
                  };
                  #53 ={
                    String = " 54. Y = -13.50 cm";
                  };
                  #54 ={
                    String = " 55. Y = -14.50 cm";
                  };
                  #55 ={
                    String = " 56. Y = -15.50 cm";
                  };
                  #56 ={
                    String = " 57. Y = -16.50 cm";
                  };
                  #57 ={
                    String = " 58. Y = -17.50 cm";
                  };
                  #58 ={
                    String = " 59. Y = -18.50 cm";
                  };
                  #59 ={
                    String = " 60. Y = -19.50 cm";
                  };
                };
                LabelFormatList ={
                  #0 ={
                    String = "%8.1f";
                  };
                  #1 ={
                    String = "%8.1f";
                  };
                };
              };
              Weight = 0.95;
              WeightLocked = 0;
              PercentOfArc = 0;
              HasSharedModifierList = 0;
              MLCTransForDisplay = 0.0135;
              CArmAngle = 0;
              TargetProjectionValid = 0;
              DoseRate = 0;
              DeliveryTime = 0;
              ODM = "Bad Value!  Programming Error\\\"";
              CPBlockingMask ={
                XDim = 1;
                YDim = 1;
                ZDim = 1;
                XMin = 0;
                YMin = 0;
                ZMin = 0;
                XMax = 0;
                YMax = 0;
                ZMax = 0;
                BinaryData = \XDR:1\;
              };
              CPBlockContourList ={
              };
              CPMLCDisplayBlockContourList ={
              };
              DoseVector = \XDR:2\;
            };
            #1 ={
              Gantry = 79;
              Couch = 355;
              Collimator = 0;
              WedgeContext ={
                WedgeName = "No Wedge";
                Orientation = "NoWedge";
                OffsetOrigin = "Patient Surface";
                OffsetDistance = -2.5;
                Angle = "No Wedge";
                MinDeliverableMU = 0;
                MaxDeliverableMU = 1e+30;
              };
              LeftJawPosition = 10;
              RightJawPosition = 10;
              TopJawPosition = 11;
              BottomJawPosition = 6;
              ModifierList ={
                BeamModifier ={
                  Name = "BeamModifier_1";
                  FixToCollimator = 1;
                  AutoBlock = 0;
                  StructureToBlock = "Manual";
                  Margin = 0;
                  InsideMode = "Expose";
                  OutsideMode = "Block";
                  ContourList ={
                    CurvePainter ={
                      Curve ={
                        RawData ={
                          NumberOfDimensions = 2;
                          NumberOfPoints = 28;
                          Points[] ={
                            0.0986848,-6.97368,
                            -0.230263,-6.18421,
                            -0.888158,-2.89474,
                            -2.13816,-2.30263,
                            -1.15132,-1.18421,
                            -2.79605,-0.986842,
                            -3.38816,-0.263158,
                            -5.88816,0.592105,
                            -5.29605,1.38158,
                            -4.57237,2.36842,
                            -3.38816,3.1579,
                            -1.80921,3.68421,
                            -0.625001,4.34211,
                            0.0986838,5.19737,
                            0.625,6.84211,
                            4.63816,6.71053,
                            5.69079,5,
                            7.26974,2.36842,
                            8.51974,2.03947,
                            9.30921,0.723685,
                            9.57237,-1.8421,
                            8.71711,-4.21053,
                            7.40132,-7.56579,
                            4.04605,-9.47368,
                            1.74342,-9.3421,
                            1.21711,-8.61842,
                            0.0328953,-6.97368,
                            0.0986848,-7.03947
                          };
                        };
                        LabelList ={
                          #0 ={
                            String = "X";
                          };
                          #1 ={
                            String = "Y";
                          };
                        };
                        RowLabelList ={
                        };
                        LabelFormatList ={
                          #0 ={
                            String = "%6.2f";
                          };
                          #1 ={
                            String = "%6.2f";
                          };
                        };
                      };
                      Color = "";
                      SliceCoordinate = 0;
                      Orientation = "Transverse";
                    };
                  };
                };
              };
              MLCLeafPositions ={
                RawData ={
                  NumberOfDimensions = 2;
                  NumberOfPoints = 60;
                  Points[] ={
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    -1.7,4.4,
                    -1.3,5.3,
                    -1,6.2,
                    -0.6,7,
                    -0.2,7.5,
                    0,7.7,
                    0.2,7.9,
                    0.3,8.1,
                    0.4,8.3,
                    0.5,8.5,
                    0.6,8.7,
                    0.7,8.9,
                    0.8,9.1,
                    1.1,9.2,
                    2.1,9.4,
                    1.7,9.6,
                    1.2,9.5,
                    3,9.5,
                    3.4,9.4,
                    4.9,9.4,
                    5.8,9.3,
                    5.4,9,
                    5,8.7,
                    4.7,7.8,
                    4.1,7.1,
                    3.2,6.8,
                    1.7,6.5,
                    0.8,6.2,
                    0.3,5.9,
                    -0.1,5.6,
                    -0.3,5.3,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0
                  };
                };
                LabelList ={
                  #0 ={
                    String = "B (X1)";
                  };
                  #1 ={
                    String = "A (X2)";
                  };
                };
                RowLabelList ={
                  #0 ={
                    String = "  1. Y = 19.50 cm";
                  };
                  #1 ={
                    String = "  2. Y = 18.50 cm";
                  };
                  #2 ={
                    String = "  3. Y = 17.50 cm";
                  };
                  #3 ={
                    String = "  4. Y = 16.50 cm";
                  };
                  #4 ={
                    String = "  5. Y = 15.50 cm";
                  };
                  #5 ={
                    String = "  6. Y = 14.50 cm";
                  };
                  #6 ={
                    String = "  7. Y = 13.50 cm";
                  };
                  #7 ={
                    String = "  8. Y = 12.50 cm";
                  };
                  #8 ={
                    String = "  9. Y = 11.50 cm";
                  };
                  #9 ={
                    String = " 10. Y = 10.50 cm";
                  };
                  #10 ={
                    String = " 11. Y =  9.75 cm";
                  };
                  #11 ={
                    String = " 12. Y =  9.25 cm";
                  };
                  #12 ={
                    String = " 13. Y =  8.75 cm";
                  };
                  #13 ={
                    String = " 14. Y =  8.25 cm";
                  };
                  #14 ={
                    String = " 15. Y =  7.75 cm";
                  };
                  #15 ={
                    String = " 16. Y =  7.25 cm";
                  };
                  #16 ={
                    String = " 17. Y =  6.75 cm";
                  };
                  #17 ={
                    String = " 18. Y =  6.25 cm";
                  };
                  #18 ={
                    String = " 19. Y =  5.75 cm";
                  };
                  #19 ={
                    String = " 20. Y =  5.25 cm";
                  };
                  #20 ={
                    String = " 21. Y =  4.75 cm";
                  };
                  #21 ={
                    String = " 22. Y =  4.25 cm";
                  };
                  #22 ={
                    String = " 23. Y =  3.75 cm";
                  };
                  #23 ={
                    String = " 24. Y =  3.25 cm";
                  };
                  #24 ={
                    String = " 25. Y =  2.75 cm";
                  };
                  #25 ={
                    String = " 26. Y =  2.25 cm";
                  };
                  #26 ={
                    String = " 27. Y =  1.75 cm";
                  };
                  #27 ={
                    String = " 28. Y =  1.25 cm";
                  };
                  #28 ={
                    String = " 29. Y =  0.75 cm";
                  };
                  #29 ={
                    String = " 30. Y =  0.25 cm";
                  };
                  #30 ={
                    String = " 31. Y = -0.25 cm";
                  };
                  #31 ={
                    String = " 32. Y = -0.75 cm";
                  };
                  #32 ={
                    String = " 33. Y = -1.25 cm";
                  };
                  #33 ={
                    String = " 34. Y = -1.75 cm";
                  };
                  #34 ={
                    String = " 35. Y = -2.25 cm";
                  };
                  #35 ={
                    String = " 36. Y = -2.75 cm";
                  };
                  #36 ={
                    String = " 37. Y = -3.25 cm";
                  };
                  #37 ={
                    String = " 38. Y = -3.75 cm";
                  };
                  #38 ={
                    String = " 39. Y = -4.25 cm";
                  };
                  #39 ={
                    String = " 40. Y = -4.75 cm";
                  };
                  #40 ={
                    String = " 41. Y = -5.25 cm";
                  };
                  #41 ={
                    String = " 42. Y = -5.75 cm";
                  };
                  #42 ={
                    String = " 43. Y = -6.25 cm";
                  };
                  #43 ={
                    String = " 44. Y = -6.75 cm";
                  };
                  #44 ={
                    String = " 45. Y = -7.25 cm";
                  };
                  #45 ={
                    String = " 46. Y = -7.75 cm";
                  };
                  #46 ={
                    String = " 47. Y = -8.25 cm";
                  };
                  #47 ={
                    String = " 48. Y = -8.75 cm";
                  };
                  #48 ={
                    String = " 49. Y = -9.25 cm";
                  };
                  #49 ={
                    String = " 50. Y = -9.75 cm";
                  };
                  #50 ={
                    String = " 51. Y = -10.50 cm";
                  };
                  #51 ={
                    String = " 52. Y = -11.50 cm";
                  };
                  #52 ={
                    String = " 53. Y = -12.50 cm";
                  };
                  #53 ={
                    String = " 54. Y = -13.50 cm";
                  };
                  #54 ={
                    String = " 55. Y = -14.50 cm";
                  };
                  #55 ={
                    String = " 56. Y = -15.50 cm";
                  };
                  #56 ={
                    String = " 57. Y = -16.50 cm";
                  };
                  #57 ={
                    String = " 58. Y = -17.50 cm";
                  };
                  #58 ={
                    String = " 59. Y = -18.50 cm";
                  };
                  #59 ={
                    String = " 60. Y = -19.50 cm";
                  };
                };
                LabelFormatList ={
                  #0 ={
                    String = "%8.1f";
                  };
                  #1 ={
                    String = "%8.1f";
                  };
                };
              };
              Weight = 0.05;
              WeightLocked = 0;
              PercentOfArc = 0;
              HasSharedModifierList = 0;
              MLCTransForDisplay = 0.0135;
              CArmAngle = 0;
              TargetProjectionValid = 0;
              DoseRate = 0;
              DeliveryTime = 0;
              ODM = "Bad Value!  Programming Error\\\"";
              CPBlockingMask ={
                XDim = 1;
                YDim = 1;
                ZDim = 1;
                XMin = 0;
                YMin = 0;
                ZMin = 0;
                XMax = 0;
                YMax = 0;
                ZMax = 0;
                BinaryData = \XDR:3\;
              };
              CPBlockContourList ={
              };
              CPMLCDisplayBlockContourList ={
              };
              DoseVector = \XDR:4\;
            };
          };
          GantryIsCCW = 0;
          MLCPushMethod = "Center";
          JawsConformance = "Static";
        };
      };
      ExtendPastTarget = 10;
      ExtendBlockPlanePastTarget = -10;
      ExtendArcPastTarget = 0;
      BevRotationAngle = 180;
      BevIsParallel = 0;
      RotationIndicatorOffset = 10;
      IMRTFilter = "Compensator";
      IMRTWedge = "Hard Wedge";
      IMRTDirection = "Gantry";
      IMRTParameterType = "Intensity Modulation";
      PrevIMRTParameterType = "None";
      PhilipsMLCTreatment = "No Export";
      PhilipsMLCBeamNumber = "No Export";
      ToshibaMLCPlanNumber = "No Export";
      ToshibaMLCBeamNumberString = "No Export";
      UseMLC = 1;
      ClipMLCDisplay = 1;
      SolidMLCDisplay = 0;
      DynamicBlocks = 0;
      Display2d = 0;
      Display3d = 1;
      CircularFieldDiameter = 0;
      ElectronApplicatorName = "None";
      SSD = 92.8167;
      AvgSSD = 92.8167;
      SSDValid = 1;
      LeftAutoSurroundMargin = 0;
      RightAutoSurroundMargin = 0;
      TopAutoSurroundMargin = 0;
      BottomAutoSurroundMargin = 0;
      AutoSurround = 0;
      BlockingMaskPixelSize = 0.05;
      BlockingMaskCutoffArea = 0.001;
      BlockAndTrayFactor = 0.03;
      TrayNumber = "";
      BlockExportName = "";
      BlockCutterFormat = "Huestis CompuCutter";
      BlockJawOverlap = 1;
      TrayFactor = 0.952;
      Compensator ={
        Name = "";
        ExportName = "";
        TrayNumber = "";
        ExportFormat = "Binary";
        IsValid = 0;
        GeneratedAutomatically = 0;
        ProtonSourceToCompensatorDistance = 50;
        ScaleWidthHeight = 0;
        Width = 5;
        Height = 5;
        ResolutionX = 0.25;
        ResolutionY = 0.25;
        DisplayResolution = 0.5;
        CompensatorHangsDown = 0;
        IncludePolyBase = 1;
        MaxIterations = 5;
        MinAllowableThickness = 0;
        MaxAllowableThickness = 10;
        RoundData = 0;
        RoundingValue = 0.001;
        CutoffHomogeneity = 0.05;
        StartOptimizationWithCurrent = 0;
        ActualHomogeneity = 0;
        OutputFactor = 1;
        Density = 1;
        EdgeOfFieldBorder = 0.5;
        InitialThickness = 0;
        MillingXScaler = 1;
        MillingYScaler = 1;
        MillingZScaler = 1;
        MillingThickness = 4;
        PositiveMilled = 3.58732e-43;
        OptimizeToMinDoseFirstIteration = 1;
        ResampleUsingLinearInterpolation = 1;
        FillOutsideMode = "Mean Thickness";
        FillOutsideThickness = 0;
        DoseCompMode = "Full 3D Dose";
        Type = "Thickness";
        PlaneDepth = 10;
        ArrayObjectName = "02 Lao Brain filter.";
        CenterAtZero = 0;
        Thickness = \XDR_COPY:5\;
        DoseMin = 0;
        DoseMax = 0;
        DoseMean = 0;
        DoseStdDev = 0;
        WetXDim = 0;
        WetYDim = 0;
      };
      CompensatorScaleFactor = 0;
      ODM ={
        Name = "";
        ExportName = "";
        ExportAscii = 0;
        ExportEffectiveODM = 0;
        IsValid = 0;
        EffectiveODMIsValid = 0;
        SourceToODMDistance = 100;
        MUForODM = 0;
        LeftJawPosition = 5;
        RightJawPosition = 5;
        TopJawPosition = 5;
        BottomJawPosition = 5;
        OFForODM = 1;
        TransCorrection = "MLC Transmission";
        GeneratedAutomatically = 0;
        BeamModified = 1;
        ODMVolume .Dimension .X = 20;
        ODMVolume .Dimension .Y = 20;
        ODMVolume .Start .X = -5;
        ODMVolume .Start .Y = -5;
        ODMVolume .VoxelSize .X = 0.5;
        ODMVolume .VoxelSize .Y = 0.5;
        ODMData = \XDR:6\;
      };
      ObjectVersion ={
        WriteVersion = "Pinnacle v16.0";
        CreateVersion = "Pinnacle v16.0";
        LoginName = "candor01";
        CreateTimeStamp = "2020-01-01 10:00:00";
        WriteTimeStamp = "2020-01-01 10:00:00";
        LastModifiedTimeStamp = "";
      };
      DoseEngine ={
        TypeName = "Adaptive Convolve";
        ConvolveHomogeneous = 0;
        FluenceHomogeneous = 0;
        FlatWaterPhantom = 0;
        FlatHomogeneous = 0;
        ElectronHomogeneous = 0;
        FluenceType = "Ray Trace";
        LongStepTuningFactor = 2;
        ShortStepLength = 0.1;
        NumberOfShortSteps = 20;
        SplitFluenceFieldSizeCutoff = 0;
        AzimuthalBinCount = 8;
        ZenithBinCount = 10;
        CumKernelRadialBinWidth = 0.005;
        SiddonCornerCutoff = 1e-07;
        NrdBinWidth = 0.1;
        AllowableDoseDiff = 5;
        HighFluenceCutoff = 20;
        LowFirstDerivCutoff = 10;
        LowSecondDerivCutoff = 20;
        HighFirstDerivCutoff = 5;
        HighSecondDerivCutoff = 10;
        AdaptiveLevels = 3;
        EnergyFlatnessCutoff = 1e-05;
        EnergyFlatnessMinimumDistance = 5.25;
        EnergyFlatnessScalingDistance = 10.5;
        EnergyFlatnessPower = 8;
        RestartIndex = 0;
        SamplesPerBatch = 10000;
        NumberOfHistoriesGoal = 1e+09;
        UncertaintyGoal = 2;
        MaxSeconds = 1e+09;
        CompletedHistories = 0;
        DoseUncertainty = 100;
        PercentDone = 0;
        ElapsedSeconds = 0;
        ElapsedCpuSeconds = 0;
        CpuPercentUtilization = 100;
        PrintBatchFiles = 0;
        PrintDataFile = 0;
        PrintEventFile = 0;
        PrintTrackFile = 0;
        StatisticsOutsideRoi = 1;
      };
      DegreesBetweenSubbeamsForDoseCalc = 5;
      IrregPointList ={
      };
      IrregPrescriptionPointName = "";
      IrregSpecifyMonitorUnits = 0;
      IrregPointPrescriptionDose = 0;
      IrregPointMonitorUnits = 0;
      IrregPointActualMonitorUnits = 0;
      IrregPointPrescribeOverall = 0;
      IrregPointNumberOfFractions = 1;
      FilmImageList ={
        FilmImage ={
          Name = "FilmImage_1";
          Type = "DRR";
          SourceToFilmDistance = 100;
          NormalDRRSpecs ={
            Energy = 0.03;
            CtToDensityTable = "New DRRa";
            BrightnessFactor = 1;
            UseRenderingLimits = 0;
            UseThreshold = 0;
            LowerThreshold = 0;
            UpperThreshold = 1;
            LinearImageLoad = 1;
            ImageWidth = 40;
            ImagePixels = 256;
            StepSize = 0.1;
            RayInterpolationMode = "Smoothest";
          };
          FastDRRSpecs ={
            Energy = 0.03;
            CtToDensityTable = "New DRRa";
            BrightnessFactor = 1;
            UseRenderingLimits = 0;
            UseThreshold = 0;
            LowerThreshold = 0;
            UpperThreshold = 1;
            LinearImageLoad = 1;
            ImageWidth = 40;
            ImagePixels = 128;
            StepSize = 0.6;
            RayInterpolationMode = "Coarse";
          };
          ApplyBoundingBox = 1;
          BoundingBox ={
            Origin ={
              X = -25;
              Y = -24.9023;
              Z = -8.75;
              Name = "";
            };
            Size ={
              X = 49.9999;
              Y = 49.9999;
              Z = 25.25;
              Name = "";
            };
            SlabMode = 0;
            SlabThickness = 3;
            Color = "red";
          };
          Film1Index = 0;
          Film2Index = 0;
          Film1Fraction = 0.5;
          Image .VolumeDisplay .Window = 4094;
          Image .VolumeDisplay .Level = 0;
          Image .VoxelSize .X = 0.15625;
          Image .VoxelSize .Y = 0.15625;
          Image .VoxelSize .Z = 1;
          Image .Dimension .X = 256;
          Image .Dimension .Y = 256;
          Image .Dimension .Z = 1;
          Image .Origin .X = -19.9219;
          Image .Origin .Y = -19.9219;
          Image .Origin .Z = 0;
          DRRValid = 0;
          HighQuality = 1;
          DRRAutoWLMode = 1;
          DCRAutoWLMode = 0;
          ApplyDrrControl = 0;
          DrrControlTable ={
            Name = "";
            NotForDoseComputation = 0;
            CanModifyDoseFlag = 1;
            Table ={
              NumberOfDimensions = 2;
              NumberOfPoints = 0;
              Points[] ={
                              };
            };
            ObjectVersion ={
              WriteVersion = "Pinnacle v16.0";
              CreateVersion = "Pinnacle v16.0";
              LoginName = "candor01";
              CreateTimeStamp = "2020-01-01 10:00:00";
              WriteTimeStamp = "2020-01-01 10:00:00";
              LastModifiedTimeStamp = "";
            };
            InOutDirection = "not set";
            RightLeftDirection = "+X";
            IsoShift = "Laser";
          };
          DrrControlType = "Ramp";
          DrrControlWindow = 550;
          DrrControlLevel = 875;
          DrrControlMaxDensity = 1.5;
          DcrControlTable ={
            Name = "Drr Ramp";
            NotForDoseComputation = 0;
            CanModifyDoseFlag = 0;
            Table ={
              NumberOfDimensions = 2;
              NumberOfPoints = 4;
              Points[] ={
                0,0,
                500,0,
                4500,1,
                4501,1
              };
            };
            ObjectVersion ={
              WriteVersion = "Pinnacle v16.0";
              CreateVersion = "Pinnacle v16.0";
              LoginName = "candor01";
              CreateTimeStamp = "2020-01-01 10:00:00";
              WriteTimeStamp = "2020-01-01 10:00:00";
              LastModifiedTimeStamp = "2020-01-01 10:00:00";
            };
            InOutDirection = "not set";
            RightLeftDirection = "+X";
            IsoShift = "Laser";
          };
          DcrControlType = "Ramp";
          DcrControlWindow = 4000;
          DcrControlLevel = 500;
          CtToOpacityTable ={
            Name = "Skin Wires AP";
            Table ={
              NumberOfDimensions = 2;
              NumberOfPoints = 9;
              Points[] ={
                0,0,
                0,0,
                263,0,
                492,0.384,
                1252,0.384,
                2540,0,
                3204,0,
                4095,0,
                4095,0
              };
            };
            ObjectVersion ={
              WriteVersion = "Pinnacle v16.0";
              CreateVersion = "Pinnacle v16.0";
              LoginName = "candor01";
              CreateTimeStamp = "2020-01-01 10:00:00";
              WriteTimeStamp = "2020-01-01 10:00:00";
              LastModifiedTimeStamp = "2020-01-01 10:00:00";
            };
          };
          UseDCR = 0;
          NumClassifBins = 0;
          ClassifList ={
          };
          DCRContrast = 1610;
          DCRBrightness = -700;
          ImageData = \XDR:7\;
        };
      };
      MonitorUnitInfo ={
        PrescriptionDose = 126.27;
        SourceToPrescriptionPointDistance = 100;
        TotalTransmissionFraction = 1;
        TransmissionDescription = "";
        PrescriptionPointDepth = 7.1833;
        PrescriptionPointRadDepth = 7.87472;
        DepthToActualPoint = 7.1833;
        SSDToActualPoint = 92.8167;
        RadDepthToActualPoint = 7.87472;
        PrescriptionPointRadDepthValid = 1;
        PrescriptionPointOffAxisDistance = 9.73291e-06;
        UnblockedFieldAreaAtSAD = 340;
        UnblockedFieldPerimeterAtSAD = 74;
        BlockedFieldAreaAtSAD = 284.75;
        IntersectFieldAreaAtSAD = 277.596;
        NormalizedDose = 1.10137;
        OffAxisRatio = 0;
        CollimatorOutputFactor = 1.02155;
        RelativeOutputFactor = 0;
        PhantomOutputFactor = 1.06549;
        OFMeasurementDepth = 0;
        OutputFactorInfo = "Interpolated using equivalent square.  OFp = 1.065";
      };
      PhotonModelDescription = "All Field Sizes";
      TarTable ={
        RawData ={
          NumberOfDimensions = 6;
          NumberOfPoints = 0;
          Points[] ={
                      };
        };
        LabelList ={
          #0 ={
            String = "Index";
          };
          #1 ={
            String = "Gantry";
          };
          #2 ={
            String = "Weight";
          };
          #3 ={
            String = "SSD (cm)";
          };
          #4 ={
            String = "Depth (cm)";
          };
          #5 ={
            String = "TAR";
          };
        };
        RowLabelList ={
        };
        LabelFormatList ={
          #0 ={
            String = " %g";
          };
          #1 ={
            String = " %5.1f";
          };
          #2 ={
            String = " %5.1f %%";
          };
          #3 ={
            String = " %5.1f";
          };
          #4 ={
            String = " %5.1f";
          };
          #5 ={
            String = " %5.3f";
          };
        };
      };
      AvgTar = 0;
      StereoDosePerMULookup = 0;
      StereoDoseDiValue = 0;
      BlocksAreLocked = 0;
      IsProtonBeamLocked = 0;
      ProtonBeam ={
      };
      DisplayList ={
        #0 ={
          Name = "Blocked Field";
          Color = "red";
          DashColor2d = "greyscale";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "On";
        };
        #1 ={
          Name = "Wedge Thick End Indicator";
          Color = "red";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
        #2 ={
          Name = "Open Field";
          Color = "red";
          DashColor2d = "greyscale";
          DisplayTransparently = 0;
          Is2dDashOn = 1;
          OnOff2d = "On";
          WireOnOff = "On";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
        #3 ={
          Name = "Wedge";
          Color = "red";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #4 ={
          Name = "Wedge at End of Arc";
          Color = "red";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #5 ={
          Name = "Central Axis Crosshair";
          Color = "red";
          DashColor2d = "greyscale";
          DisplayTransparently = 0;
          Is2dDashOn = 1;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
        #6 ={
          Name = "Arc Rotation Indicator";
          Color = "red";
          DashColor2d = "greyscale";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
        #7 ={
          Name = "Compensator";
          Color = "red";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #8 ={
          Name = "MLC Leaves";
          Color = "greyscale";
          DashColor2d = "";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
        #9 ={
          Name = "Block Plane";
          Color = "red";
          DashColor2d = "";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
        #10 ={
          Name = "Table";
          Color = "greyscale";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #11 ={
          Name = "Gantry";
          Color = "lavender";
          DashColor2d = "red";
          DisplayTransparently = 1;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #12 ={
          Name = "Accelerator";
          Color = "greyscale";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #13 ={
          Name = "Floor";
          Color = "lightblue";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #14 ={
          Name = "Opening Density Matrix";
          Color = "red";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #15 ={
          Name = "";
          Color = "";
          DashColor2d = "";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
      };
      RelyOnBolusNames = "&";
      DoseVolume = \XDR:8\;
      DoseVarVolume = \XDR:9\;
      Weight = 50;
      IsWeightLocked = 0;
      MonitorUnitsValid = 1;
      MonitorUnitsApproximate = 0;
      FieldID = "02";
      SpeedUpCollimator = 0;
      SpeedUpVirtFlouro = 0;
      DisplayMAXLeafMotion = 0;
      BeamWasSplit = 0;
      DoseRate = 600;
      IsCopyOpposeAllowed = 1;
      VerticalJawSync = 0;
      ScenarioDoseVolume0 = \XDR:10\;
      ScenarioDoseVolume1 = \XDR:11\;
      ScenarioDoseVolume2 = \XDR:12\;
      ScenarioDoseVolume3 = \XDR:13\;
      ScenarioDoseVolume4 = \XDR:14\;
      ScenarioDoseVolume5 = \XDR:15\;
      ScenarioDoseVolume6 = \XDR:16\;
      ScenarioDoseVolume7 = \XDR:17\;
      DeserializationCompleted = 0;
    };
    Beam ={
      Name = "03 R lat Brain";
      IsocenterName = "iso";
      PrescriptionName = "Brain";
      UsePoiForPrescriptionPoint = 1;
      PrescriptionPointName = "iso";
      PrescriptionPointDepth = 5;
      PrescriptionPointXOffset = 0;
      PrescriptionPointYOffset = 0;
      SpecifyDosePerMuAtPrescriptionPoint = 0;
      DosePerMuAtPrescriptionPoint = 1;
      MachineNameAndVersion = "RogueOne: 2020-01-01 10:00:00";
      Modality = "Photons";
      MachineEnergyName = "6X";
      DesiredLocalizerName = "Laser";
      ActualLocalizerName = "Laser";
      DisplayLaserMotion = "Table";
      SetBeamType = "Step & Shoot MLC";
      PrevBeamType = "Static";
      ComputationVersion = "Pinnacle v16.0";
      CPManager ={
        CPManagerObject ={
          IsGantryStartStopLocked = 1;
          IsCouchStartStopLocked = 1;
          IsCollimatorStartStopLocked = 1;
          IsLeftRightIndependent = 1;
          IsTopBottomIndependent = 1;
          NumberOfControlPoints = 2;
          ControlPointList ={
            #0 ={
              Gantry = 270;
              Couch = 355;
              Collimator = 0;
              WedgeContext ={
                WedgeName = "No Wedge";
                Orientation = "NoWedge";
                OffsetOrigin = "Patient Surface";
                OffsetDistance = -2.5;
                Angle = "No Wedge";
                MinDeliverableMU = 0;
                MaxDeliverableMU = 1e+30;
              };
              LeftJawPosition = 10;
              RightJawPosition = 10;
              TopJawPosition = 11;
              BottomJawPosition = 6;
              ModifierList ={
                BeamModifier ={
                  Name = "BeamModifier_1";
                  FixToCollimator = 1;
                  AutoBlock = 0;
                  StructureToBlock = "Manual";
                  Margin = 0;
                  InsideMode = "Expose";
                  OutsideMode = "Block";
                  ContourList ={
                    CurvePainter ={
                      Curve ={
                        RawData ={
                          NumberOfDimensions = 2;
                          NumberOfPoints = 28;
                          Points[] ={
                            12.0066,-12.6974,
                            11.4145,-6.25,
                            9.90132,-3.28948,
                            8.71711,-1.9079,
                            8.32237,-1.25,
                            7.79606,-0.723685,
                            6.67763,-5.83777e-07,
                            5.88816,0.592105,
                            5.29605,1.38158,
                            4.57237,2.36842,
                            3.38816,3.1579,
                            1.80921,3.68421,
                            0.625001,4.34211,
                            -0.0986838,5.19737,
                            -0.625,6.84211,
                            -4.63816,6.71053,
                            -6.21711,5.59211,
                            -7.46711,4.27632,
                            -8.84869,2.56579,
                            -10.6908,0.13158,
                            -12.5987,-4.86842,
                            -11.8092,-11.3816,
                            -9.76974,-13.8158,
                            -6.61185,-14.7368,
                            2.26974,-14.8026,
                            7.33553,-14.2763,
                            11.875,-12.7632,
                            12.0066,-12.6974
                          };
                        };
                        LabelList ={
                          #0 ={
                            String = "X";
                          };
                          #1 ={
                            String = "Y";
                          };
                        };
                        RowLabelList ={
                        };
                        LabelFormatList ={
                          #0 ={
                            String = "%6.2f";
                          };
                          #1 ={
                            String = "%6.2f";
                          };
                        };
                      };
                      Color = "";
                      SliceCoordinate = 0;
                      Orientation = "Transverse";
                    };
                  };
                };
              };
              MLCLeafPositions ={
                RawData ={
                  NumberOfDimensions = 2;
                  NumberOfPoints = 60;
                  Points[] ={
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,10,
                    10,9.9,
                    10,9.5,
                    10,9,
                    10,8.6,
                    10,8.4,
                    10,7.8,
                    10,7.1,
                    10,6.4,
                    10,5.8,
                    9.9,5.4,
                    9.5,5,
                    9.1,4.7,
                    8.7,4.1,
                    8.3,3.2,
                    7.9,1.7,
                    7.5,0.8,
                    7,0.3,
                    6.6,-0.1,
                    6,-0.3,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0
                  };
                };
                LabelList ={
                  #0 ={
                    String = "B (X1)";
                  };
                  #1 ={
                    String = "A (X2)";
                  };
                };
                RowLabelList ={
                  #0 ={
                    String = "  1. Y = 19.50 cm";
                  };
                  #1 ={
                    String = "  2. Y = 18.50 cm";
                  };
                  #2 ={
                    String = "  3. Y = 17.50 cm";
                  };
                  #3 ={
                    String = "  4. Y = 16.50 cm";
                  };
                  #4 ={
                    String = "  5. Y = 15.50 cm";
                  };
                  #5 ={
                    String = "  6. Y = 14.50 cm";
                  };
                  #6 ={
                    String = "  7. Y = 13.50 cm";
                  };
                  #7 ={
                    String = "  8. Y = 12.50 cm";
                  };
                  #8 ={
                    String = "  9. Y = 11.50 cm";
                  };
                  #9 ={
                    String = " 10. Y = 10.50 cm";
                  };
                  #10 ={
                    String = " 11. Y =  9.75 cm";
                  };
                  #11 ={
                    String = " 12. Y =  9.25 cm";
                  };
                  #12 ={
                    String = " 13. Y =  8.75 cm";
                  };
                  #13 ={
                    String = " 14. Y =  8.25 cm";
                  };
                  #14 ={
                    String = " 15. Y =  7.75 cm";
                  };
                  #15 ={
                    String = " 16. Y =  7.25 cm";
                  };
                  #16 ={
                    String = " 17. Y =  6.75 cm";
                  };
                  #17 ={
                    String = " 18. Y =  6.25 cm";
                  };
                  #18 ={
                    String = " 19. Y =  5.75 cm";
                  };
                  #19 ={
                    String = " 20. Y =  5.25 cm";
                  };
                  #20 ={
                    String = " 21. Y =  4.75 cm";
                  };
                  #21 ={
                    String = " 22. Y =  4.25 cm";
                  };
                  #22 ={
                    String = " 23. Y =  3.75 cm";
                  };
                  #23 ={
                    String = " 24. Y =  3.25 cm";
                  };
                  #24 ={
                    String = " 25. Y =  2.75 cm";
                  };
                  #25 ={
                    String = " 26. Y =  2.25 cm";
                  };
                  #26 ={
                    String = " 27. Y =  1.75 cm";
                  };
                  #27 ={
                    String = " 28. Y =  1.25 cm";
                  };
                  #28 ={
                    String = " 29. Y =  0.75 cm";
                  };
                  #29 ={
                    String = " 30. Y =  0.25 cm";
                  };
                  #30 ={
                    String = " 31. Y = -0.25 cm";
                  };
                  #31 ={
                    String = " 32. Y = -0.75 cm";
                  };
                  #32 ={
                    String = " 33. Y = -1.25 cm";
                  };
                  #33 ={
                    String = " 34. Y = -1.75 cm";
                  };
                  #34 ={
                    String = " 35. Y = -2.25 cm";
                  };
                  #35 ={
                    String = " 36. Y = -2.75 cm";
                  };
                  #36 ={
                    String = " 37. Y = -3.25 cm";
                  };
                  #37 ={
                    String = " 38. Y = -3.75 cm";
                  };
                  #38 ={
                    String = " 39. Y = -4.25 cm";
                  };
                  #39 ={
                    String = " 40. Y = -4.75 cm";
                  };
                  #40 ={
                    String = " 41. Y = -5.25 cm";
                  };
                  #41 ={
                    String = " 42. Y = -5.75 cm";
                  };
                  #42 ={
                    String = " 43. Y = -6.25 cm";
                  };
                  #43 ={
                    String = " 44. Y = -6.75 cm";
                  };
                  #44 ={
                    String = " 45. Y = -7.25 cm";
                  };
                  #45 ={
                    String = " 46. Y = -7.75 cm";
                  };
                  #46 ={
                    String = " 47. Y = -8.25 cm";
                  };
                  #47 ={
                    String = " 48. Y = -8.75 cm";
                  };
                  #48 ={
                    String = " 49. Y = -9.25 cm";
                  };
                  #49 ={
                    String = " 50. Y = -9.75 cm";
                  };
                  #50 ={
                    String = " 51. Y = -10.50 cm";
                  };
                  #51 ={
                    String = " 52. Y = -11.50 cm";
                  };
                  #52 ={
                    String = " 53. Y = -12.50 cm";
                  };
                  #53 ={
                    String = " 54. Y = -13.50 cm";
                  };
                  #54 ={
                    String = " 55. Y = -14.50 cm";
                  };
                  #55 ={
                    String = " 56. Y = -15.50 cm";
                  };
                  #56 ={
                    String = " 57. Y = -16.50 cm";
                  };
                  #57 ={
                    String = " 58. Y = -17.50 cm";
                  };
                  #58 ={
                    String = " 59. Y = -18.50 cm";
                  };
                  #59 ={
                    String = " 60. Y = -19.50 cm";
                  };
                };
                LabelFormatList ={
                  #0 ={
                    String = "%8.1f";
                  };
                  #1 ={
                    String = "%8.1f";
                  };
                };
              };
              Weight = 0.95;
              WeightLocked = 0;
              PercentOfArc = 0;
              HasSharedModifierList = 0;
              MLCTransForDisplay = 0.0135;
              CArmAngle = 0;
              TargetProjectionValid = 0;
              DoseRate = 0;
              DeliveryTime = 0;
              ODM = "Bad Value!  Programming Error\\\"";
              CPBlockingMask ={
                XDim = 1;
                YDim = 1;
                ZDim = 1;
                XMin = 0;
                YMin = 0;
                ZMin = 0;
                XMax = 0;
                YMax = 0;
                ZMax = 0;
                BinaryData = \XDR:18\;
              };
              CPBlockContourList ={
              };
              CPMLCDisplayBlockContourList ={
              };
              DoseVector = \XDR:19\;
            };
            #1 ={
              Gantry = 270;
              Couch = 355;
              Collimator = 0;
              WedgeContext ={
                WedgeName = "No Wedge";
                Orientation = "NoWedge";
                OffsetOrigin = "Patient Surface";
                OffsetDistance = -2.5;
                Angle = "No Wedge";
                MinDeliverableMU = 0;
                MaxDeliverableMU = 1e+30;
              };
              LeftJawPosition = 10;
              RightJawPosition = 10;
              TopJawPosition = 11;
              BottomJawPosition = 6;
              ModifierList ={
                BeamModifier ={
                  Name = "BeamModifier_1";
                  FixToCollimator = 1;
                  AutoBlock = 0;
                  StructureToBlock = "Manual";
                  Margin = 0;
                  InsideMode = "Expose";
                  OutsideMode = "Block";
                  ContourList ={
                    CurvePainter ={
                      Curve ={
                        RawData ={
                          NumberOfDimensions = 2;
                          NumberOfPoints = 29;
                          Points[] ={
                            1.61184,-5.52632,
                            2.33553,-3.35526,
                            4.375,-1.51316,
                            8.71711,-1.9079,
                            8.32237,-1.25,
                            7.79606,-0.723685,
                            6.67763,-5.83777e-07,
                            5.88816,0.592105,
                            5.29605,1.38158,
                            4.57237,2.36842,
                            3.38816,3.1579,
                            1.80921,3.68421,
                            0.625001,4.34211,
                            -0.0986838,5.19737,
                            -0.625,6.84211,
                            -4.63816,6.71053,
                            -4.17763,5.19737,
                            -7.5329,2.63158,
                            -8.58553,2.36842,
                            -9.30921,-1.25,
                            -8.19079,-5.32895,
                            -6.01974,-8.55263,
                            -3.19079,-9.60526,
                            -0.49342,-9.47368,
                            3.32237,-8.35526,
                            5.42763,-6.71053,
                            2.33553,-6.57895,
                            1.54605,-5.52632,
                            1.61184,-5.39474
                          };
                        };
                        LabelList ={
                          #0 ={
                            String = "X";
                          };
                          #1 ={
                            String = "Y";
                          };
                        };
                        RowLabelList ={
                        };
                        LabelFormatList ={
                          #0 ={
                            String = "%6.2f";
                          };
                          #1 ={
                            String = "%6.2f";
                          };
                        };
                      };
                      Color = "";
                      SliceCoordinate = 0;
                      Orientation = "Transverse";
                    };
                  };
                };
              };
              MLCLeafPositions ={
                RawData ={
                  NumberOfDimensions = 2;
                  NumberOfPoints = 60;
                  Points[] ={
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    4.1,0.2,
                    5.5,1.9,
                    6.2,3.4,
                    6.6,4.1,
                    6.9,4.7,
                    7.2,5.4,
                    7.6,2.1,
                    7.9,1.7,
                    8.2,1.7,
                    8.3,1.9,
                    8.5,2,
                    8.6,2.2,
                    8.7,2.4,
                    8.9,3,
                    9,3.5,
                    9.2,8.6,
                    9.3,8.4,
                    9.2,7.8,
                    9.1,7.1,
                    9,6.4,
                    8.9,5.8,
                    8.8,5.4,
                    8.7,5,
                    8.6,4.7,
                    7.4,4.1,
                    6.7,3.2,
                    6.1,1.7,
                    5.4,0.8,
                    4.8,0.3,
                    4.2,-0.1,
                    4.3,-0.3,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0,
                    0,0
                  };
                };
                LabelList ={
                  #0 ={
                    String = "B (X1)";
                  };
                  #1 ={
                    String = "A (X2)";
                  };
                };
                RowLabelList ={
                  #0 ={
                    String = "  1. Y = 19.50 cm";
                  };
                  #1 ={
                    String = "  2. Y = 18.50 cm";
                  };
                  #2 ={
                    String = "  3. Y = 17.50 cm";
                  };
                  #3 ={
                    String = "  4. Y = 16.50 cm";
                  };
                  #4 ={
                    String = "  5. Y = 15.50 cm";
                  };
                  #5 ={
                    String = "  6. Y = 14.50 cm";
                  };
                  #6 ={
                    String = "  7. Y = 13.50 cm";
                  };
                  #7 ={
                    String = "  8. Y = 12.50 cm";
                  };
                  #8 ={
                    String = "  9. Y = 11.50 cm";
                  };
                  #9 ={
                    String = " 10. Y = 10.50 cm";
                  };
                  #10 ={
                    String = " 11. Y =  9.75 cm";
                  };
                  #11 ={
                    String = " 12. Y =  9.25 cm";
                  };
                  #12 ={
                    String = " 13. Y =  8.75 cm";
                  };
                  #13 ={
                    String = " 14. Y =  8.25 cm";
                  };
                  #14 ={
                    String = " 15. Y =  7.75 cm";
                  };
                  #15 ={
                    String = " 16. Y =  7.25 cm";
                  };
                  #16 ={
                    String = " 17. Y =  6.75 cm";
                  };
                  #17 ={
                    String = " 18. Y =  6.25 cm";
                  };
                  #18 ={
                    String = " 19. Y =  5.75 cm";
                  };
                  #19 ={
                    String = " 20. Y =  5.25 cm";
                  };
                  #20 ={
                    String = " 21. Y =  4.75 cm";
                  };
                  #21 ={
                    String = " 22. Y =  4.25 cm";
                  };
                  #22 ={
                    String = " 23. Y =  3.75 cm";
                  };
                  #23 ={
                    String = " 24. Y =  3.25 cm";
                  };
                  #24 ={
                    String = " 25. Y =  2.75 cm";
                  };
                  #25 ={
                    String = " 26. Y =  2.25 cm";
                  };
                  #26 ={
                    String = " 27. Y =  1.75 cm";
                  };
                  #27 ={
                    String = " 28. Y =  1.25 cm";
                  };
                  #28 ={
                    String = " 29. Y =  0.75 cm";
                  };
                  #29 ={
                    String = " 30. Y =  0.25 cm";
                  };
                  #30 ={
                    String = " 31. Y = -0.25 cm";
                  };
                  #31 ={
                    String = " 32. Y = -0.75 cm";
                  };
                  #32 ={
                    String = " 33. Y = -1.25 cm";
                  };
                  #33 ={
                    String = " 34. Y = -1.75 cm";
                  };
                  #34 ={
                    String = " 35. Y = -2.25 cm";
                  };
                  #35 ={
                    String = " 36. Y = -2.75 cm";
                  };
                  #36 ={
                    String = " 37. Y = -3.25 cm";
                  };
                  #37 ={
                    String = " 38. Y = -3.75 cm";
                  };
                  #38 ={
                    String = " 39. Y = -4.25 cm";
                  };
                  #39 ={
                    String = " 40. Y = -4.75 cm";
                  };
                  #40 ={
                    String = " 41. Y = -5.25 cm";
                  };
                  #41 ={
                    String = " 42. Y = -5.75 cm";
                  };
                  #42 ={
                    String = " 43. Y = -6.25 cm";
                  };
                  #43 ={
                    String = " 44. Y = -6.75 cm";
                  };
                  #44 ={
                    String = " 45. Y = -7.25 cm";
                  };
                  #45 ={
                    String = " 46. Y = -7.75 cm";
                  };
                  #46 ={
                    String = " 47. Y = -8.25 cm";
                  };
                  #47 ={
                    String = " 48. Y = -8.75 cm";
                  };
                  #48 ={
                    String = " 49. Y = -9.25 cm";
                  };
                  #49 ={
                    String = " 50. Y = -9.75 cm";
                  };
                  #50 ={
                    String = " 51. Y = -10.50 cm";
                  };
                  #51 ={
                    String = " 52. Y = -11.50 cm";
                  };
                  #52 ={
                    String = " 53. Y = -12.50 cm";
                  };
                  #53 ={
                    String = " 54. Y = -13.50 cm";
                  };
                  #54 ={
                    String = " 55. Y = -14.50 cm";
                  };
                  #55 ={
                    String = " 56. Y = -15.50 cm";
                  };
                  #56 ={
                    String = " 57. Y = -16.50 cm";
                  };
                  #57 ={
                    String = " 58. Y = -17.50 cm";
                  };
                  #58 ={
                    String = " 59. Y = -18.50 cm";
                  };
                  #59 ={
                    String = " 60. Y = -19.50 cm";
                  };
                };
                LabelFormatList ={
                  #0 ={
                    String = "%8.1f";
                  };
                  #1 ={
                    String = "%8.1f";
                  };
                };
              };
              Weight = 0.05;
              WeightLocked = 0;
              PercentOfArc = 0;
              HasSharedModifierList = 0;
              MLCTransForDisplay = 0.0135;
              CArmAngle = 0;
              TargetProjectionValid = 0;
              DoseRate = 0;
              DeliveryTime = 0;
              ODM = "Bad Value!  Programming Error\\\"";
              CPBlockingMask ={
                XDim = 1;
                YDim = 1;
                ZDim = 1;
                XMin = 0;
                YMin = 0;
                ZMin = 0;
                XMax = 0;
                YMax = 0;
                ZMax = 0;
                BinaryData = \XDR:20\;
              };
              CPBlockContourList ={
              };
              CPMLCDisplayBlockContourList ={
              };
              DoseVector = \XDR:21\;
            };
          };
          GantryIsCCW = 0;
          MLCPushMethod = "Center";
          JawsConformance = "Static";
        };
      };
      ExtendPastTarget = 10;
      ExtendBlockPlanePastTarget = -10;
      ExtendArcPastTarget = 0;
      BevRotationAngle = 180;
      BevIsParallel = 0;
      RotationIndicatorOffset = 10;
      IMRTFilter = "Compensator";
      IMRTWedge = "Hard Wedge";
      IMRTDirection = "Gantry";
      IMRTParameterType = "Intensity Modulation";
      PrevIMRTParameterType = "None";
      PhilipsMLCTreatment = "No Export";
      PhilipsMLCBeamNumber = "No Export";
      ToshibaMLCPlanNumber = "No Export";
      ToshibaMLCBeamNumberString = "No Export";
      UseMLC = 1;
      ClipMLCDisplay = 1;
      SolidMLCDisplay = 0;
      DynamicBlocks = 0;
      Display2d = 0;
      Display3d = 1;
      CircularFieldDiameter = 0;
      ElectronApplicatorName = "None";
      SSD = 92.313;
      AvgSSD = 92.313;
      SSDValid = 1;
      LeftAutoSurroundMargin = 0;
      RightAutoSurroundMargin = 0;
      TopAutoSurroundMargin = 0;
      BottomAutoSurroundMargin = 0;
      AutoSurround = 0;
      BlockingMaskPixelSize = 0.05;
      BlockingMaskCutoffArea = 0.001;
      BlockAndTrayFactor = 0.03;
      TrayNumber = "";
      BlockExportName = "";
      BlockCutterFormat = "Huestis CompuCutter";
      BlockJawOverlap = 1;
      TrayFactor = 0.952;
      Compensator ={
        Name = "";
        ExportName = "";
        TrayNumber = "";
        ExportFormat = "Binary";
        IsValid = 0;
        GeneratedAutomatically = 0;
        ProtonSourceToCompensatorDistance = 50;
        ScaleWidthHeight = 0;
        Width = 5;
        Height = 5;
        ResolutionX = 0.25;
        ResolutionY = 0.25;
        DisplayResolution = 0.5;
        CompensatorHangsDown = 0;
        IncludePolyBase = 1;
        MaxIterations = 5;
        MinAllowableThickness = 0;
        MaxAllowableThickness = 10;
        RoundData = 0;
        RoundingValue = 0.001;
        CutoffHomogeneity = 0.05;
        StartOptimizationWithCurrent = 0;
        ActualHomogeneity = 0;
        OutputFactor = 1;
        Density = 1;
        EdgeOfFieldBorder = 0.5;
        InitialThickness = 0;
        MillingXScaler = 1;
        MillingYScaler = 1;
        MillingZScaler = 1;
        MillingThickness = 4;
        PositiveMilled = 3.58732e-43;
        OptimizeToMinDoseFirstIteration = 1;
        ResampleUsingLinearInterpolation = 1;
        FillOutsideMode = "Mean Thickness";
        FillOutsideThickness = 0;
        DoseCompMode = "Full 3D Dose";
        Type = "Thickness";
        PlaneDepth = 10;
        ArrayObjectName = "03 R lat Brain filter.";
        CenterAtZero = 0;
        Thickness = \XDR_COPY:22\;
        DoseMin = 0;
        DoseMax = 0;
        DoseMean = 0;
        DoseStdDev = 0;
        WetXDim = 0;
        WetYDim = 0;
      };
      CompensatorScaleFactor = 0;
      ODM ={
        Name = "";
        ExportName = "";
        ExportAscii = 0;
        ExportEffectiveODM = 0;
        IsValid = 0;
        EffectiveODMIsValid = 0;
        SourceToODMDistance = 100;
        MUForODM = 0;
        LeftJawPosition = 5;
        RightJawPosition = 5;
        TopJawPosition = 5;
        BottomJawPosition = 5;
        OFForODM = 1;
        TransCorrection = "MLC Transmission";
        GeneratedAutomatically = 0;
        BeamModified = 1;
        ODMVolume .Dimension .X = 20;
        ODMVolume .Dimension .Y = 20;
        ODMVolume .Start .X = -5;
        ODMVolume .Start .Y = -5;
        ODMVolume .VoxelSize .X = 0.5;
        ODMVolume .VoxelSize .Y = 0.5;
        ODMData = \XDR:23\;
      };
      ObjectVersion ={
        WriteVersion = "Pinnacle v16.0";
        CreateVersion = "Pinnacle v16.0";
        LoginName = "candor01";
        CreateTimeStamp = "2020-01-01 10:00:00";
        WriteTimeStamp = "2020-01-01 10:00:00";
        LastModifiedTimeStamp = "";
      };
      DoseEngine ={
        TypeName = "Adaptive Convolve";
        ConvolveHomogeneous = 0;
        FluenceHomogeneous = 0;
        FlatWaterPhantom = 0;
        FlatHomogeneous = 0;
        ElectronHomogeneous = 0;
        FluenceType = "Ray Trace";
        LongStepTuningFactor = 2;
        ShortStepLength = 0.1;
        NumberOfShortSteps = 20;
        SplitFluenceFieldSizeCutoff = 0;
        AzimuthalBinCount = 8;
        ZenithBinCount = 10;
        CumKernelRadialBinWidth = 0.005;
        SiddonCornerCutoff = 1e-07;
        NrdBinWidth = 0.1;
        AllowableDoseDiff = 5;
        HighFluenceCutoff = 20;
        LowFirstDerivCutoff = 10;
        LowSecondDerivCutoff = 20;
        HighFirstDerivCutoff = 5;
        HighSecondDerivCutoff = 10;
        AdaptiveLevels = 3;
        EnergyFlatnessCutoff = 1e-05;
        EnergyFlatnessMinimumDistance = 5.25;
        EnergyFlatnessScalingDistance = 10.5;
        EnergyFlatnessPower = 8;
        RestartIndex = 0;
        SamplesPerBatch = 10000;
        NumberOfHistoriesGoal = 1e+09;
        UncertaintyGoal = 2;
        MaxSeconds = 1e+09;
        CompletedHistories = 0;
        DoseUncertainty = 100;
        PercentDone = 0;
        ElapsedSeconds = 0;
        ElapsedCpuSeconds = 0;
        CpuPercentUtilization = 100;
        PrintBatchFiles = 0;
        PrintDataFile = 0;
        PrintEventFile = 0;
        PrintTrackFile = 0;
        StatisticsOutsideRoi = 1;
      };
      DegreesBetweenSubbeamsForDoseCalc = 5;
      IrregPointList ={
      };
      IrregPrescriptionPointName = "";
      IrregSpecifyMonitorUnits = 0;
      IrregPointPrescriptionDose = 0;
      IrregPointMonitorUnits = 0;
      IrregPointActualMonitorUnits = 0;
      IrregPointPrescribeOverall = 0;
      IrregPointNumberOfFractions = 1;
      FilmImageList ={
        FilmImage ={
          Name = "FilmImage_1";
          Type = "DRR";
          SourceToFilmDistance = 100;
          NormalDRRSpecs ={
            Energy = 0.03;
            CtToDensityTable = "New DRRa";
            BrightnessFactor = 1;
            UseRenderingLimits = 0;
            UseThreshold = 0;
            LowerThreshold = 0;
            UpperThreshold = 1;
            LinearImageLoad = 1;
            ImageWidth = 40;
            ImagePixels = 256;
            StepSize = 0.1;
            RayInterpolationMode = "Smoothest";
          };
          FastDRRSpecs ={
            Energy = 0.03;
            CtToDensityTable = "New DRRa";
            BrightnessFactor = 1;
            UseRenderingLimits = 0;
            UseThreshold = 0;
            LowerThreshold = 0;
            UpperThreshold = 1;
            LinearImageLoad = 1;
            ImageWidth = 40;
            ImagePixels = 128;
            StepSize = 0.6;
            RayInterpolationMode = "Coarse";
          };
          ApplyBoundingBox = 1;
          BoundingBox ={
            Origin ={
              X = -25;
              Y = -24.9023;
              Z = -8.75;
              Name = "";
            };
            Size ={
              X = 49.9999;
              Y = 49.9999;
              Z = 25.25;
              Name = "";
            };
            SlabMode = 0;
            SlabThickness = 3;
            Color = "red";
          };
          Film1Index = 0;
          Film2Index = 0;
          Film1Fraction = 0.5;
          Image .VolumeDisplay .Window = 4094;
          Image .VolumeDisplay .Level = 0;
          Image .VoxelSize .X = 0.15625;
          Image .VoxelSize .Y = 0.15625;
          Image .VoxelSize .Z = 1;
          Image .Dimension .X = 256;
          Image .Dimension .Y = 256;
          Image .Dimension .Z = 1;
          Image .Origin .X = -19.9219;
          Image .Origin .Y = -19.9219;
          Image .Origin .Z = 0;
          DRRValid = 0;
          HighQuality = 1;
          DRRAutoWLMode = 1;
          DCRAutoWLMode = 0;
          ApplyDrrControl = 0;
          DrrControlTable ={
            Name = "";
            NotForDoseComputation = 0;
            CanModifyDoseFlag = 1;
            Table ={
              NumberOfDimensions = 2;
              NumberOfPoints = 0;
              Points[] ={
                              };
            };
            ObjectVersion ={
              WriteVersion = "Pinnacle v16.0";
              CreateVersion = "Pinnacle v16.0";
              LoginName = "candor01";
              CreateTimeStamp = "2020-01-01 10:00:00";
              WriteTimeStamp = "2020-01-01 10:00:00";
              LastModifiedTimeStamp = "";
            };
            InOutDirection = "not set";
            RightLeftDirection = "+X";
            IsoShift = "Laser";
          };
          DrrControlType = "Ramp";
          DrrControlWindow = 550;
          DrrControlLevel = 875;
          DrrControlMaxDensity = 1.5;
          DcrControlTable ={
            Name = "Drr Ramp";
            NotForDoseComputation = 0;
            CanModifyDoseFlag = 0;
            Table ={
              NumberOfDimensions = 2;
              NumberOfPoints = 4;
              Points[] ={
                0,0,
                500,0,
                4500,1,
                4501,1
              };
            };
            ObjectVersion ={
              WriteVersion = "Pinnacle v16.0";
              CreateVersion = "Pinnacle v16.0";
              LoginName = "candor01";
              CreateTimeStamp = "2020-01-01 10:00:00";
              WriteTimeStamp = "2020-01-01 10:00:00";
              LastModifiedTimeStamp = "2020-01-01 10:00:00";
            };
            InOutDirection = "not set";
            RightLeftDirection = "+X";
            IsoShift = "Laser";
          };
          DcrControlType = "Ramp";
          DcrControlWindow = 4000;
          DcrControlLevel = 500;
          CtToOpacityTable ={
            Name = "Skin Wires AP";
            Table ={
              NumberOfDimensions = 2;
              NumberOfPoints = 9;
              Points[] ={
                0,0,
                0,0,
                263,0,
                492,0.384,
                1252,0.384,
                2540,0,
                3204,0,
                4095,0,
                4095,0
              };
            };
            ObjectVersion ={
              WriteVersion = "Pinnacle v16.0";
              CreateVersion = "Pinnacle v16.0";
              LoginName = "candor01";
              CreateTimeStamp = "2020-01-01 10:00:00";
              WriteTimeStamp = "2020-01-01 10:00:00";
              LastModifiedTimeStamp = "2020-01-01 10:00:00";
            };
          };
          UseDCR = 0;
          NumClassifBins = 0;
          ClassifList ={
          };
          DCRContrast = 1610;
          DCRBrightness = -700;
          ImageData = \XDR:24\;
        };
      };
      MonitorUnitInfo ={
        PrescriptionDose = 126.56;
        SourceToPrescriptionPointDistance = 100;
        TotalTransmissionFraction = 1;
        TransmissionDescription = "";
        PrescriptionPointDepth = 7.68694;
        PrescriptionPointRadDepth = 8.12132;
        DepthToActualPoint = 7.68694;
        SSDToActualPoint = 92.313;
        RadDepthToActualPoint = 8.12134;
        PrescriptionPointRadDepthValid = 1;
        PrescriptionPointOffAxisDistance = 1.78814e-06;
        UnblockedFieldAreaAtSAD = 340;
        UnblockedFieldPerimeterAtSAD = 74;
        BlockedFieldAreaAtSAD = 284.75;
        IntersectFieldAreaAtSAD = 274.821;
        NormalizedDose = 1.08824;
        OffAxisRatio = 0;
        CollimatorOutputFactor = 1.02155;
        RelativeOutputFactor = 0;
        PhantomOutputFactor = 1.06549;
        OFMeasurementDepth = 0;
        OutputFactorInfo = "Interpolated using equivalent square.  OFp = 1.065";
      };
      PhotonModelDescription = "All Field Sizes";
      TarTable ={
        RawData ={
          NumberOfDimensions = 6;
          NumberOfPoints = 0;
          Points[] ={
                      };
        };
        LabelList ={
          #0 ={
            String = "Index";
          };
          #1 ={
            String = "Gantry";
          };
          #2 ={
            String = "Weight";
          };
          #3 ={
            String = "SSD (cm)";
          };
          #4 ={
            String = "Depth (cm)";
          };
          #5 ={
            String = "TAR";
          };
        };
        RowLabelList ={
        };
        LabelFormatList ={
          #0 ={
            String = " %g";
          };
          #1 ={
            String = " %5.1f";
          };
          #2 ={
            String = " %5.1f %%";
          };
          #3 ={
            String = " %5.1f";
          };
          #4 ={
            String = " %5.1f";
          };
          #5 ={
            String = " %5.3f";
          };
        };
      };
      AvgTar = 0;
      StereoDosePerMULookup = 0;
      StereoDoseDiValue = 0;
      BlocksAreLocked = 0;
      IsProtonBeamLocked = 0;
      ProtonBeam ={
      };
      DisplayList ={
        #0 ={
          Name = "Blocked Field";
          Color = "green";
          DashColor2d = "greyscale";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "On";
        };
        #1 ={
          Name = "Wedge Thick End Indicator";
          Color = "green";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
        #2 ={
          Name = "Open Field";
          Color = "green";
          DashColor2d = "greyscale";
          DisplayTransparently = 0;
          Is2dDashOn = 1;
          OnOff2d = "On";
          WireOnOff = "On";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
        #3 ={
          Name = "Wedge";
          Color = "green";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #4 ={
          Name = "Wedge at End of Arc";
          Color = "green";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #5 ={
          Name = "Central Axis Crosshair";
          Color = "green";
          DashColor2d = "greyscale";
          DisplayTransparently = 0;
          Is2dDashOn = 1;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
        #6 ={
          Name = "Arc Rotation Indicator";
          Color = "green";
          DashColor2d = "greyscale";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
        #7 ={
          Name = "Compensator";
          Color = "green";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #8 ={
          Name = "MLC Leaves";
          Color = "greyscale";
          DashColor2d = "";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
        #9 ={
          Name = "Block Plane";
          Color = "green";
          DashColor2d = "";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
        #10 ={
          Name = "Table";
          Color = "greyscale";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #11 ={
          Name = "Gantry";
          Color = "lavender";
          DashColor2d = "red";
          DisplayTransparently = 1;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #12 ={
          Name = "Accelerator";
          Color = "greyscale";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #13 ={
          Name = "Floor";
          Color = "lightblue";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #14 ={
          Name = "Opening Density Matrix";
          Color = "green";
          DashColor2d = "red";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "On";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "On";
          WashOnOff = "Off";
        };
        #15 ={
          Name = "";
          Color = "";
          DashColor2d = "";
          DisplayTransparently = 0;
          Is2dDashOn = 0;
          OnOff2d = "Off";
          WireOnOff = "Off";
          PathOnOff = "Off";
          SolidOnOff = "Off";
          WashOnOff = "Off";
        };
      };
      RelyOnBolusNames = "&";
      DoseVolume = \XDR:25\;
      DoseVarVolume = \XDR:26\;
      Weight = 50;
      IsWeightLocked = 0;
      MonitorUnitsValid = 1;
      MonitorUnitsApproximate = 0;
      FieldID = "03";
      SpeedUpCollimator = 0;
      SpeedUpVirtFlouro = 0;
      DisplayMAXLeafMotion = 0;
      BeamWasSplit = 0;
      DoseRate = 600;
      IsCopyOpposeAllowed = 1;
      VerticalJawSync = 0;
      ScenarioDoseVolume0 = \XDR:27\;
      ScenarioDoseVolume1 = \XDR:28\;
      ScenarioDoseVolume2 = \XDR:29\;
      ScenarioDoseVolume3 = \XDR:30\;
      ScenarioDoseVolume4 = \XDR:31\;
      ScenarioDoseVolume5 = \XDR:32\;
      ScenarioDoseVolume6 = \XDR:33\;
      ScenarioDoseVolume7 = \XDR:34\;
      DeserializationCompleted = 0;
    };
  };
  BrachyManager ={
    DefaultSegmentTipTineLength = 0.5;
    SourceGroupList ={
    };
    SeedArray ={
      TemplateName = "BrachyTemplate_1";
      Color = "Yellow";
      SingleSeedCathColor = "Yellow";
      MultiSeedCathColor = "Red";
      CoordSystem ={
        OriginX = 1.66;
        OriginY = 19.38;
        OriginZ = 0;
        XAxisX = 7.66;
        XAxisY = 19.38;
        XAxisZ = 0;
        YAxisX = 1.66;
        YAxisY = 24.88;
        YAxisZ = 0;
        ZAxisX = 1.66;
        ZAxisY = 19.38;
        ZAxisZ = 0.5;
        XLength = 12;
        YLength = 11;
        ZLength = 1;
      };
      PositionDefined = 1;
      IsLocked = 0;
    };
    SourceList ={
    };
    CatheterList ={
    };
    FilmAP = 1;
    FilmRL = 1;
    BrachyFilm1 ={
      Name = "AP Film";
      SFD = 100;
      SAD = 100;
      Couch = 0;
      Gantry = 0;
      Collimator = 0;
      Magnification = 1;
      ShiftDistance = 25;
      Target ={
        RawData ={
          NumberOfDimensions = 2;
          NumberOfPoints = 0;
          Points[] ={
                      };
        };
        LabelList ={
          #0 ={
            String = "X";
          };
          #1 ={
            String = "Y";
          };
        };
        RowLabelList ={
        };
        LabelFormatList ={
          #0 ={
            String = " %g";
          };
          #1 ={
            String = " %g";
          };
        };
      };
    };
    BrachyFilm2 ={
      Name = "LR Film";
      SFD = 100;
      SAD = 100;
      Couch = 0;
      Gantry = 0;
      Collimator = 0;
      Magnification = 1;
      ShiftDistance = 25;
      Target ={
        RawData ={
          NumberOfDimensions = 2;
          NumberOfPoints = 0;
          Points[] ={
                      };
        };
        LabelList ={
          #0 ={
            String = "X";
          };
          #1 ={
            String = "Y";
          };
        };
        RowLabelList ={
        };
        LabelFormatList ={
          #0 ={
            String = " %g";
          };
          #1 ={
            String = " %g";
          };
        };
      };
    };
    ReconstructionList ={
    };
    MachineNameAndVersion = "None";
    SourcePositionList = "Cath Tip to Source Center";
    NumberOfSourcesToLoad = 10;
    DoubleLoadTip = 0;
    DoubleLoadBase = 0;
    TipToFirstSourceSpace = 0.5;
    InterSourceSpace = 2;
    TemplateRows = 4;
    TemplateColumns = 4;
    TemplateRowSpace = 2;
    TemplateColumnSpace = 2;
    TemplateCatheterLength = 30;
    TemplateOffsetGap = 0;
    TemplateAutoLoadCatheters = 0;
    TemplateGuideCathCentered = 1;
    ReconstructionErrorTolerance = 0.5;
    FilmReconstructionList = "Orthogonal";
    TargetReconstructionList = "POIs";
    CommonAxisErrorTolerance = 0.5;
    HighlightCurrentSource = 1;
    HighlightCurrentCath = 0;
    DrawCurrentCathOnly = 0;
    StereoShiftIsVertical = 1;
  };
  BrachyPrescriptionMethod = "Dose Rate";
  BrachyImplantDateAndTime ={
    UTC_ISODateAndTimeString = "2020-01-01 10:00:00Z";
  };
  BrachyImplantDuration = 24;
  BrachyImplantDurationUnits = "Hours";
  MaximumDoseForSeeds = 0;
  PrintSubbeams = 1;
  PrintPois = 1;
  PrintRois = 1;
  PrintBrachyByCatheter = 0;
  PrintBrachyByGroup = 0;
  PrintMLC = 1;
  PrintMLCIrreg = 0;
  UseTrialForTreatment = 1;
  TargetOptGoalList ={
  };
  OAROptGoalList ={
  };
  APEngineSettings ={
    MaxIterations = 50;
    IsEngineTypeBiological = 1;
    TuningBalance = 11;
    DoseFallOffMargin = 2.6;
    HotspotMaximumGoal = 107;
    UseColdSpotROI = 1;
  };
  PlanarDoseList ={
  };
  EPIDDoseList ={
  };
  BeamPageSelection = "Modality";
  DisplayState = "Normal";
  BrachyDoseComputationVersion = "Unknown";
  BrachyDose = \XDR:35\;
  BeamPoiWeightOptimizer ={
    PointList ={
    };
    SimplexOptimizer ={
      MaxIterations = 10000;
      Alpha = 1;
      Beta = 0.5;
      Gamma = 2;
      NumDimensions = 3;
      SolutionTolerance = 0.0001;
      SimplexScale = 50;
      NumberOfRestarts = 5;
    };
    NegativeMUPenalty = 1000;
    ModifyPrescription = 0;
    Grouping = "No Grouping";
    NumberOfBeamsPenalty = 0;
  };
  ObjectVersion ={
    WriteVersion = "Pinnacle v16.0";
    CreateVersion = "Unknown";
    LoginName = "candor01";
    CreateTimeStamp = "Unknown";
    WriteTimeStamp = "2020-01-01 10:00:00";
    LastModifiedTimeStamp = "Unknown";
  };
  MLCExport ={
    PatientID = "";
    ExportFormat = "Varian";
    VarianVersion = "F";
    VarianTolerance = 0.2;
    PhilipsMLCName = "MLC_1";
    PhilipsAccessory = 0;
    PhilipsFitmentCode = 1;
    ToshibaDoseRate = "Normal";
  };
  CoordRefPoint ={
    Name = "Coord Ref";
    XCoord = -0.0977211;
    YCoord = -4.19617e-05;
    ZCoord = 3.5;
    XRotation = 0;
    YRotation = 0;
    ZRotation = 0;
    Radius = 1;
    Color = "green";
    CoordSys = "CT";
    CoordinateFormat = "%6.2f";
    Display2d = "Off";
    Display3d = "Off";
    ObjectVersion ={
      WriteVersion = "Pinnacle v16.0";
      CreateVersion = "Pinnacle v16.0";
      LoginName = "candor01";
      CreateTimeStamp = "2020-01-01 10:00:00";
      WriteTimeStamp = "2020-01-01 10:00:00";
      LastModifiedTimeStamp = "";
    };
    VolumeName = "LAST^FIRST^M";
    PoiInterpretedType = "MARKER";
    PoiDisplayOnOtherVolumes = 1;
    IsLocked = 0;
  };
  UseCoordRefPoint = 0;
  CourseID = 99;
  ToleranceTable = 0;
  AlwaysDisplay2dCouchPosition = 0;
  AlwaysDisplay3dCouchPosition = 0;
  ExportPlanarDoseAscii = 0;
  PrintIMRTSummary = 0;
  PrintIMPTSummary = 0;
  PrimaryVOI ={
    Origin ={
      X = -25;
      Y = -24.9023;
      Z = -8.75;
      Name = "";
    };
    Size ={
      X = 49.9999;
      Y = 49.9999;
      Z = 25.25;
      Name = "";
    };
    SlabMode = 0;
    SlabThickness = 3;
    Color = "skyblue";
  };
  MultipleMachines = 0;
  CheckCtToDensityExtension = 0;
  SeriesNumber = 1;
  SeriesDescription = "";
  CTScanner = "York Cancer Center";
  HasDose = 0;
  IncludeDICOMCoordInReport = 0;
  IncludeShiftsFromFirstInReport = 0;
  IsAbsoluteLaserMode = 1;
  LastLaserTransmissionMode = "--";
  APError = 0;
  LRError = 0;
  ISError = 0;
  DepthError = 0;
  FluenceDoseSpread = 4;
  SingleGaussianDoseSpread = 4;
  DoubleGaussianDoseSpread = 3;
  NuclearDoseSpread = 3;
  MinDoseThreshold = 1e-06;
  IsRO = 0;
  AntPostWeight = 25;
  LateralWeight = 25;
  InfSupWeight = 25;
  DepthWeight = 25;
  NominalWeight = 50;
  PDM ={
    PDVolList ={
    };
    ScaleMode = 1;
  };
};
