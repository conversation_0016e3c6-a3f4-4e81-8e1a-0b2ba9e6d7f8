#!/usr/bin/env python3
"""
Simple test script to verify plan selection functionality.
"""

import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui.components.patient_plans_panel import PatientPlansPanel
import ttkbootstrap as ttk  # type: ignore
from unittest.mock import MagicMock


def test_plan_selection():
    """Test the plan selection functionality."""

    # Create a mock root window
    root = ttk.Window()

    # Create the patient plans panel
    panel = PatientPlansPanel(root)

    # Test that the panel initializes correctly
    assert hasattr(panel, "plan_buttons")
    assert hasattr(panel, "all_plans")
    assert len(panel.plan_buttons) == 0
    assert len(panel.all_plans) == 0

    # Test adding a plan
    parent = ttk.Frame(panel)
    mock_plan = MagicMock()
    mock_plan.name = "Test Plan"
    mock_patient = MagicMock()
    mock_patient.patient_name = "Test Patient"

    plan_frame = panel.add_plan(parent, "Test Plan", mock_plan, mock_patient)

    # Verify the plan was stored
    assert "Test Plan" in panel.plan_buttons
    assert len(panel.all_plans) == 1
    assert panel.all_plans[0][0] == "Test Plan"

    # Test plan selection by name
    callback_called = False

    def test_callback(patient, plan):
        nonlocal callback_called
        callback_called = True
        assert patient.patient_name == "Test Patient"
        assert plan.name == "Test Plan"

    panel.on_plan_selected = test_callback

    # Test selection by name
    result = panel.select_plan_by_name("Test Plan")
    assert result == True
    assert callback_called

    # Test selection by index
    callback_called = False
    result = panel.select_plan_by_index(0)
    assert result == True
    assert callback_called

    # Test selection of non-existent plan
    result = panel.select_plan_by_name("Non-existent Plan")
    assert result == False

    # Test selection of invalid index
    result = panel.select_plan_by_index(999)
    assert result == False

    # Test clear functionality
    panel.clear_patients()
    assert len(panel.plan_buttons) == 0
    assert len(panel.all_plans) == 0

    print("All plan selection tests passed!")

    # Clean up
    root.destroy()


if __name__ == "__main__":
    test_plan_selection()
