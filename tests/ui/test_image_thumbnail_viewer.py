"""
Test utility for loading and displaying binary image data (e.g., CT image slices) as thumbnails using ttkbootstrap and DirectoryFileService.
"""

import os
from pathlib import Path
from typing import Optional
import ttkbootstrap as ttk  # type: ignore
from PIL import Image, ImageTk
import numpy as np

from ui.theme import setup_theme
from pinnacle_io.api import PinnacleReader


class ImageThumbnailViewer:
    """
    A simple ttkbootstrap window to load a binary image stack from test data using DirectoryFileService
    and display slices as thumbnails. Includes slider and entry to select slice index.
    """

    def __init__(self, test_data_dir: Optional[str] = None) -> None:
        """
        Initialize the viewer window, load the image stack, and set up UI controls.

        Args:
            test_data_dir (Optional[str]): Path to the test data directory. If None, uses a default path.
        """
        self.root = ttk.Window(
            title="Image Thumbnail Viewer", themename="litera", size=(700, 700)
        )
        self.slice_var: ttk.IntVar = ttk.IntVar()
        setup_theme(self.root.style)
        self.image_label = None
        self.thumbnail = None
        self.photo = None
        self.slider = None
        self.slice_entry = None
        self.slice_count = 1
        self.arr_stack_uint16 = None
        self.global_min = 0
        self.global_max = 1

        # Use default test data path if not provided
        if test_data_dir is None:
            # Default to tests/test_data/archive_01/Institution_1/Mount_0/Patient_1/
            test_data_dir = os.path.join(
                os.path.dirname(__file__),
                "..",
                "tests",
                "test_data",
                "archive_01",
                "Institution_1",
                "Mount_0",
                "Patient_1",
            )
        self.test_data_dir = os.path.abspath(test_data_dir)
        self.reader = PinnacleReader(self.test_data_dir)

        self._load_image_stack()
        self._create_controls()
        self.root.eval("tk::PlaceWindow . center")

    def _load_image_stack(self) -> None:
        """
        Load the binary image stack, store original uint16 data, and compute global window/level for consistent display.
        """
        img_path = "ImageSet_0.img"
        try:
            img_bytes = self.service.read_binary_file(img_path)
            arr = np.frombuffer(img_bytes, dtype=np.uint16)
            # Guess number of slices (assume 512x512 per slice)
            if arr.size % (512 * 512) != 0:
                raise ValueError(f"Unexpected image size: {arr.size}")
            self.slice_count = arr.size // (512 * 512)
            arr = arr.reshape((self.slice_count, 512, 512))
            self.arr_stack_uint16 = arr  # Store original stack
            # Compute global window/level (min/max) for all slices
            self.global_min = arr.min()
            self.global_max = arr.max()
        except Exception as e:
            self.arr_stack_uint16 = None
            self.slice_count = 1
            self.global_min = 0
            self.global_max = 1
            print(f"Error loading image stack: {e}")

    def _create_controls(self) -> None:
        """Create UI controls for loading and displaying image thumbnails."""
        frame = ttk.Frame(self.root)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        ttk.Label(frame, text="Select and display a CT image slice as thumbnail:").pack(
            pady=10
        )

        # Slider for slice index
        slider_frame = ttk.Frame(frame)
        slider_frame.pack(pady=5)
        self.slider = ttk.Scale(
            slider_frame,
            from_=0,
            to=self.slice_count - 1,
            orient="horizontal",
            length=400,
            variable=self.slice_var,
            command=self._on_slider_change,
        )
        self.slider.pack(side="left", padx=5)

        # Entry for slice index (below slider)
        entry_frame = ttk.Frame(frame)
        entry_frame.pack(pady=5)
        self.slice_entry = ttk.Entry(
            entry_frame, width=5, textvariable=self.slice_var, justify="center"
        )
        self.slice_entry.pack()
        self.slice_entry.bind("<Return>", self._on_entry_change)

        self.image_label = ttk.Label(frame)
        self.image_label.pack(pady=20)

        # Bind mouse wheel events for scrolling slices (Windows, Mac, Linux)
        self.image_label.bind("<MouseWheel>", self._on_mousewheel)  # Windows, Mac
        self.image_label.bind(
            "<Button-4>", self._on_mousewheel_linux
        )  # Linux scroll up
        self.image_label.bind(
            "<Button-5>", self._on_mousewheel_linux
        )  # Linux scroll down

        # Show initial slice
        self.show_slice(0)

    def _on_slider_change(self, event: object = None) -> None:
        """Update image and entry when slider is moved. Snap to integer values."""
        idx = int(round(float(self.slice_var.get())))  # type: ignore
        self.slice_var.set(idx)
        if self.slice_entry is not None:
            self.slice_entry.delete(0, "end")
            self.slice_entry.insert(0, str(idx))
        self.show_slice(idx)

    def _on_entry_change(self, event: object = None) -> None:
        """Update image and slider when entry is changed."""
        try:
            idx = int(self.slice_entry.get()) if self.slice_entry is not None else 0
        except Exception:
            idx = 0
        idx = max(0, min(self.slice_count - 1, idx))
        self.slice_var.set(idx)
        if self.slider is not None:
            self.slider.set(idx)
        self.show_slice(idx)

    def _on_mousewheel(self, event) -> None:
        """Handle mouse wheel scroll for Windows/Mac."""
        delta = event.delta
        if delta > 0:
            self._scroll_slice(-1)
        elif delta < 0:
            self._scroll_slice(1)

    def _on_mousewheel_linux(self, event) -> None:
        """Handle mouse wheel scroll for Linux (Button-4/5)."""
        if event.num == 4:
            self._scroll_slice(-1)
        elif event.num == 5:
            self._scroll_slice(1)

    def _scroll_slice(self, direction: int) -> None:
        """Scroll through slices by direction (-1 for up, 1 for down)."""
        idx = self.slice_var.get()
        idx_new = max(0, min(self.slice_count - 1, idx + direction))
        if idx_new != idx:
            self.slice_var.set(idx_new)
            if self.slider is not None:
                self.slider.set(idx_new)
            if self.slice_entry is not None:
                self.slice_entry.delete(0, "end")
                self.slice_entry.insert(0, str(idx_new))
            self.show_slice(idx_new)

    def show_slice(self, idx: int) -> None:
        """
        Display the specified slice index as a thumbnail, using global window/level.
        Args:
            idx (int): Slice index to display.
        """
        if (
            not hasattr(self, "arr_stack_uint16")
            or self.arr_stack_uint16 is None
            or idx < 0
            or idx >= self.slice_count
        ):
            if self.image_label is not None:
                self.image_label.configure(
                    text=f"Error: No image or invalid index", image=""
                )
            return
        arr = self.arr_stack_uint16[idx]
        # Apply global window/level (global min/max)
        min_val = self.global_min
        max_val = self.global_max
        arr_clipped = np.clip(arr, min_val, max_val)
        arr8 = (
            (arr_clipped - min_val)
            / (max_val - min_val if max_val != min_val else 1)
            * 255
        ).astype("uint8")
        img = Image.fromarray(arr8, mode="L")
        self.thumbnail = img.copy()
        self.thumbnail.thumbnail((512, 512))
        self.photo = ImageTk.PhotoImage(self.thumbnail)
        if self.image_label is not None:
            self.image_label.configure(image=self.photo, text="")

    def run(self) -> None:
        """
        Start the viewer main loop.
        """
        self.root.mainloop()


if __name__ == "__main__":
    viewer = ImageThumbnailViewer()
    viewer.run()
