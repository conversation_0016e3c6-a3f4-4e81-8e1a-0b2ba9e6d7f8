import pytest
import tkinter as tk
import ttkbootstrap as ttk  # type: ignore
from unittest.mock import Mock
import sys
import os
import importlib.util

# Import HeaderPanel from the correct path
spec = importlib.util.spec_from_file_location(
    "header_panel", os.path.join(os.path.dirname(__file__), "../../ui/components/header_panel.py")
)
if spec is None or spec.loader is None:
    raise ImportError("Could not load HeaderPanel module from ui/components/header_panel.py")
header_panel_module = importlib.util.module_from_spec(spec)
sys.modules["header_panel"] = header_panel_module
spec.loader.exec_module(header_panel_module)
HeaderPanel = header_panel_module.HeaderPanel

@pytest.fixture
def root():
    root = ttk.Window(themename="litera")
    yield root
    root.destroy()

@pytest.fixture
def mock_callbacks():
    return {
        "on_load_tar": <PERSON><PERSON>(),
        "on_load_zip": <PERSON>ck(),
        "on_select_directory": <PERSON>ck(),
    }

def test_headerpanel_initialization(root, mock_callbacks):
    hp = HeaderPanel(
        root,
        on_load_tar=mock_callbacks["on_load_tar"],
        on_load_zip=mock_callbacks["on_load_zip"],
        on_select_directory=mock_callbacks["on_select_directory"],
    )
    assert hp.on_load_tar == mock_callbacks["on_load_tar"]
    assert hp.on_load_zip == mock_callbacks["on_load_zip"]
    assert hp.on_select_directory == mock_callbacks["on_select_directory"]
    assert isinstance(hp, ttk.Frame)

def test_set_source_path_updates_stringvar(root):
    hp = HeaderPanel(root)
    hp.set_source_path("/some/path")
    # Access the private variable for test
    assert hp._source_path_var.get() == "/some/path"
    hp.set_source_path("")
    assert hp._source_path_var.get() == ""
    hp.set_source_path(None)
    assert hp._source_path_var.get() == ""

def test_button_callbacks_triggered(root, mock_callbacks):
    hp = HeaderPanel(
        root,
        on_load_tar=mock_callbacks["on_load_tar"],
        on_load_zip=mock_callbacks["on_load_zip"],
        on_select_directory=mock_callbacks["on_select_directory"],
    )
    # The button_frame is the second child (after the section label)
    children = hp.winfo_children()
    assert len(children) >= 2, "HeaderPanel should have at least two children (label, button_frame)"
    button_frame = children[1]
    button_widgets = [w for w in button_frame.winfo_children() if isinstance(w, ttk.Button)]
    assert len(button_widgets) == 3, f"Expected 3 buttons, found {len(button_widgets)}: {[w.cget('text') for w in button_widgets]}"
    # Simulate button presses
    button_widgets[0].invoke()
    mock_callbacks["on_load_tar"].assert_called_once()
    button_widgets[1].invoke()
    mock_callbacks["on_load_zip"].assert_called_once()
    button_widgets[2].invoke()
    mock_callbacks["on_select_directory"].assert_called_once() 