import pytest
import ttkbootstrap as ttk  # type: ignore
from unittest.mock import MagicMock
from ui.components.patient_plans_panel import PatientPlansPanel


# Mock Patient and Plan classes with required attributes
class MockPlan:
    def __init__(self, name, primary_ct_image_set_id="ct1", plan_id="1"):
        self.name = name
        self.primary_ct_image_set_id = primary_ct_image_set_id
        self.plan_id = plan_id


class MockPatient:
    def __init__(self, mrn, name, plans=None, patient_path="/tmp/patient"):
        self.medical_record_number = mrn
        self.last_and_first_name = name
        self.plan_list = plans or []
        self.patient_path = patient_path


@pytest.fixture
def root_window():
    root = ttk.Window(themename="litera")
    yield root
    root.destroy()


@pytest.fixture
def patient_plans_panel(root_window):
    # Create PatientPlansPanel without needing to mock PinnacleReader
    # since individual panels now use readers directly
    panel = PatientPlansPanel(root_window)
    return panel


def test_initialization(patient_plans_panel):
    assert isinstance(patient_plans_panel.patients, dict)
    assert patient_plans_panel.patient_list.winfo_children() == []


def test_add_patient(patient_plans_panel):
    patient = MockPatient("p1", "John Doe", plans=[MockPlan("Plan A")])
    patient_plans_panel.add_patient(patient)
    assert "p1" in patient_plans_panel.patients
    # Check that patient frame exists
    patient_frame = patient_plans_panel.patients["p1"]
    assert patient_frame.winfo_exists()
    # Check that plan frame exists (should be a child of patient_frame)
    plan_frames = [w for w in patient_frame.winfo_children() if isinstance(w, ttk.Frame)]
    assert plan_frames  # At least one plan frame


def test_add_multiple_patients(patient_plans_panel):
    patient1 = MockPatient("p1", "John Doe")
    patient2 = MockPatient("p2", "Jane Smith")
    patient_plans_panel.add_patient(patient1)
    patient_plans_panel.add_patient(patient2)
    assert set(patient_plans_panel.patients.keys()) == {"p1", "p2"}


def test_clear_patients(patient_plans_panel):
    patient = MockPatient("p1", "John Doe")
    patient_plans_panel.add_patient(patient)
    patient_plans_panel.clear_patients()
    assert patient_plans_panel.patients == {}
    assert patient_plans_panel.patient_list.winfo_children() == []


def test_add_plan(patient_plans_panel):
    parent = ttk.Frame(patient_plans_panel)
    plan = MockPlan("Plan B")
    patient = MockPatient("p1", "John Doe")
    plan_frame = patient_plans_panel.add_plan(parent, plan.name, plan, patient)
    assert plan_frame.winfo_exists()
    # Check that the label text matches the plan name
    button = [w for w in plan_frame.winfo_children() if isinstance(w, ttk.Button)][0]
    assert "Plan B" in button.cget("text")
