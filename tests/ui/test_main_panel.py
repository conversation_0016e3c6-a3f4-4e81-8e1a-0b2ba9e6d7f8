import pytest
import tkinter as tk
import ttkbootstrap as ttk  # type: ignore
from ui.components.main_panel import MainPanel

def run_main_panel_demo():
    root = ttk.Window(themename="flatly")
    root.title("MainPanel Demo")
    root.geometry("900x600")
    panel = MainPanel(root)
    panel.pack(fill="both", expand=True)
    # Set some test data
    class DummyPatient:
        last_and_first_name = "John Doe"
    class DummyPlan:
        name = "Test Plan"
    panel.update_plan_details(patient=DummyPatient(), plan=DummyPlan())
    panel.trial_combobox['values'] = ["Trial 1", "Trial 2", "Trial 3"]
    panel.trial_combobox.set("Trial 1")
    root.mainloop()

if __name__ == "__main__":
    run_main_panel_demo()

def test_main_panel_runs():
    # This test is for pytest compatibility; it will not run the GUI in CI
    assert True 