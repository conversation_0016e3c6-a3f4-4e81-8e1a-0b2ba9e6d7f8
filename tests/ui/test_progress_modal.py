import ttkbootstrap as ttk  # type: ignore

import time
import threading

from ui.components.progress_modal import ProgressModal
from ui.theme import setup_theme


class ProgressModalTest:
    def __init__(self):
        # Initialize UI dependencies here to avoid a pytest collection error

        self.root = ttk.Window(
            title="Progress Modal Test", themename="litera", size=(800, 600)
        )
        setup_theme(self.root.style)

        # Create progress modal
        self.progress_modal = ProgressModal(self.root)

        # Create test controls
        self.create_controls()

        # Center window
        self.root.eval("tk::PlaceWindow . center")

    def create_controls(self):
        """Create test control buttons"""
        controls = ttk.Frame(self.root)
        controls.pack(expand=True)

        ttk.Button(
            controls, text="Test Simple Progress", command=self.test_simple_progress
        ).pack(pady=5)

        ttk.But<PERSON>(
            controls, text="Test Multi-step Process", command=self.test_multi_step
        ).pack(pady=5)

        ttk.<PERSON><PERSON>(
            controls, text="Test Indefinite Progress", command=self.test_indefinite
        ).pack(pady=5)

    def test_simple_progress(self):
        """Test simple progress increments"""

        def run_progress():
            self.progress_modal.show(
                title="Simple Progress Test", operation="Testing simple operation..."
            )

            for i in range(0, 101, 10):
                if not self.progress_modal.is_visible:
                    break
                self.progress_modal.set_progress(i)
                time.sleep(0.5)

            if self.progress_modal.is_visible:
                self.progress_modal.hide()

        self.progress_modal.set_cancel_callback(self.progress_modal.hide)
        threading.Thread(target=run_progress, daemon=True).start()

    def test_multi_step(self):
        """Test multi-step process"""

        def run_steps():
            steps = [
                "Loading data...",
                "Processing images...",
                "Converting formats...",
                "Saving results...",
                "Cleaning up...",
            ]

            self.progress_modal.show(
                title="Multi-step Process Test", operation="Testing multiple steps"
            )
            self.progress_modal.set_total_steps(len(steps))

            for step in steps:
                if not self.progress_modal.is_visible:
                    break
                self.progress_modal.increment_step(step)
                time.sleep(1)

            if self.progress_modal.is_visible:
                self.progress_modal.hide()

        self.progress_modal.set_cancel_callback(self.progress_modal.hide)
        threading.Thread(target=run_steps, daemon=True).start()

    def test_indefinite(self):
        """Test progress with no specific percentage"""
        self.progress_modal.show(
            title="Testing",
            operation="Testing background task...",
            mode="indeterminate",
        )

        def stop_after_delay():
            time.sleep(999)
            if self.progress_modal.is_visible:
                self.progress_modal.hide()

        self.progress_modal.set_cancel_callback(self.progress_modal.hide)

        threading.Thread(target=stop_after_delay, daemon=True).start()

    def run(self):
        """Start the test"""
        self.root.mainloop()


if __name__ == "__main__":
    test = ProgressModalTest()
    test.run()
