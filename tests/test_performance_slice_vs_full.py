"""
Performance test comparing reading a single image slice vs reading the entire 3D dataset.
"""

import time
import pytest
import numpy as np
from pathlib import Path

from pinnacle_io.readers.image_set_reader import ImageSetReader
from pinnacle_io.services.file_reader import FileReader


class TestImageSlicePerformance:
    """Test performance of reading image slices vs full 3D datasets."""

    def test_slice_vs_full_dataset_performance(self):
        """Test performance comparison between reading a single slice vs entire 3D dataset."""
        test_data_path = Path(__file__).parent / "test_data" / "01"
        image_set_header = test_data_path / "Institution_1" / "Mount_0" / "Patient_1" / "ImageSet_0.header"

        # Skip test if test data doesn't exist
        if not image_set_header.exists():
            pytest.skip("Test data not available")

        # Initialize reader service
        reader_service = FileReader(test_data_path)

        # Test 1: Read single slice (slice 10)
        print("\n=== PERFORMANCE COMPARISON ===")
        slice_number = 10

        start_time = time.perf_counter()
        single_slice_data = ImageSetReader.read_image_slice(
            str(test_data_path / "Institution_1" / "Mount_0" / "Patient_1" / "ImageSet_0.img"),
            slice_number,
            file_service=reader_service,
        )
        slice_read_time = time.perf_counter() - start_time

        print(f"Single slice ({slice_number}) read time: {slice_read_time:.6f} seconds")

        # Test 2: Read entire 3D dataset
        start_time = time.perf_counter()
        full_image_set = ImageSetReader.read_image_set(
            str(test_data_path / "Institution_1" / "Mount_0" / "Patient_1" / "ImageSet_0.img"),
            image_index=None,
            image_header=None,
            file_service=reader_service,
        )
        full_read_time = time.perf_counter() - start_time

        print(f"Full 3D dataset read time: {full_read_time:.6f} seconds")

        # Calculate performance difference
        if slice_read_time > 0:
            if slice_read_time < full_read_time:
                speed_improvement = full_read_time / slice_read_time
                print(f"Reading a single slice is {speed_improvement:.2f}x faster than reading the entire 3D dataset")
            else:
                speed_ratio = slice_read_time / full_read_time
                print(f"Reading the entire 3D dataset is {speed_ratio:.2f}x faster than reading a single slice")
        else:
            print("Slice read was too fast to measure accurately")

        # Verify we got actual data
        assert single_slice_data is not None, "Single slice data should not be None"
        assert full_image_set is not None, "Full image set should not be None"
        assert full_image_set.pixel_data is not None, "Full image set pixel data should not be None"

        # Verify dimensions make sense
        if isinstance(single_slice_data, np.ndarray):
            print(f"Single slice data shape: {single_slice_data.shape}")
            print(f"Single slice data type: {single_slice_data.dtype}")

        if full_image_set.pixel_data is not None:
            print(f"Full dataset shape: {full_image_set.pixel_data.shape}")
            print(f"Full dataset data type: {full_image_set.pixel_data.dtype}")

            # Extract same slice from full dataset for comparison
            full_dataset_slice = full_image_set.get_slice_data(slice_number)
            if full_dataset_slice is not None and single_slice_data is not None:
                # Verify they match (approximately, accounting for potential data type differences)
                if single_slice_data.shape == full_dataset_slice.shape:
                    print(f"Slice data shapes match: {single_slice_data.shape}")
                else:
                    print(f"Slice data shapes differ: {single_slice_data.shape} vs {full_dataset_slice.shape}")

        # Performance assertions
        assert slice_read_time > 0, "Slice read time should be measurable"
        assert full_read_time > 0, "Full read time should be measurable"
        # Note: The actual performance depends on implementation details like header caching
        # The key finding is the measured performance difference between the two approaches

        print("=== END PERFORMANCE COMPARISON ===\n")

    def test_multiple_slice_vs_full_performance(self):
        """Test performance of reading multiple slices vs reading full dataset once."""
        test_data_path = Path(__file__).parent / "test_data" / "01"
        image_set_header = test_data_path / "Institution_1" / "Mount_0" / "Patient_1" / "ImageSet_0.header"

        # Skip test if test data doesn't exist
        if not image_set_header.exists():
            pytest.skip("Test data not available")

        reader_service = FileReader(test_data_path)

        print("\n=== MULTIPLE SLICE PERFORMANCE COMPARISON ===")

        # Test reading 5 individual slices
        slice_numbers = [5, 10, 15, 20, 25]
        image_path = str(test_data_path / "Institution_1" / "Mount_0" / "Patient_1" / "ImageSet_0.img")

        start_time = time.perf_counter()
        individual_slices = []
        for slice_num in slice_numbers:
            slice_data = ImageSetReader.read_image_slice(image_path, slice_num, file_service=reader_service)
            individual_slices.append(slice_data)
        multiple_slice_time = time.perf_counter() - start_time

        print(f"Reading {len(slice_numbers)} individual slices time: {multiple_slice_time:.6f} seconds")

        # Test reading full dataset once and extracting slices
        start_time = time.perf_counter()
        full_image_set = ImageSetReader.read_image_set(
            image_path, image_index=None, image_header=None, file_service=reader_service
        )
        extracted_slices = []
        for slice_num in slice_numbers:
            slice_data = full_image_set.get_slice_data(slice_num)
            extracted_slices.append(slice_data)
        full_then_extract_time = time.perf_counter() - start_time

        print(
            f"Reading full dataset then extracting {len(slice_numbers)} slices time: {full_then_extract_time:.6f} seconds"
        )

        # Calculate which approach is faster
        if multiple_slice_time < full_then_extract_time:
            improvement = full_then_extract_time / multiple_slice_time
            print(f"Reading individual slices is {improvement:.2f}x faster for {len(slice_numbers)} slices")
        else:
            improvement = multiple_slice_time / full_then_extract_time
            print(f"Reading full dataset then extracting is {improvement:.2f}x faster for {len(slice_numbers)} slices")

        print("=== END MULTIPLE SLICE PERFORMANCE COMPARISON ===\n")

        # Verify we got data
        assert len(individual_slices) == len(slice_numbers)
        assert len(extracted_slices) == len(slice_numbers)
        assert multiple_slice_time > 0
        assert full_then_extract_time > 0
