"""
MainView module for the DICOM Converter UI.
Handles all UI logic, file/directory dialogs, and manages PinnacleReader initialization.
"""

import os
import ttkbootstrap as ttk  # type: ignore
from tkinter import filedialog
import threading
import time
import logging
from typing import Optional, Callable, Any, Literal
from pinnacle_io import Institution
from pinnacle_io.api import PinnacleReader
from pinnacle_io.models import Patient, Plan

from .components import HeaderPanel, PatientPlansPanel, MainPanel
from .components.progress_modal import ProgressModal
from .theme import setup_theme, PRIMARY_COLOR

# Configure logging. Ensure logs directory exists
log_dir = os.path.join(os.path.dirname(__file__), "logs")
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, "ui.log")

# Configure root logger
root_logger = logging.getLogger()
root_logger.setLevel(logging.INFO)

# Clear any existing handlers
for handler in root_logger.handlers[:]:
    root_logger.removeHandler(handler)

# Create formatter
formatter = logging.Formatter(
    "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)

# Console handler (stdout)
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_handler.setFormatter(formatter)
root_logger.addHandler(console_handler)

# File handler (overwrite each time)
file_handler = logging.FileHandler(log_file, mode='w')
file_handler.setLevel(logging.INFO)
file_handler.setFormatter(formatter)
root_logger.addHandler(file_handler)

logger = logging.getLogger(__name__)


class MainView:
    """
    MainView is responsible for all UI logic, including file/directory dialogs,
    setting the source path, and displaying status/progress. It manages PinnacleReader
    initialization and coordinates data flow between UI components.
    """

    MIN_PROGRESS_DISPLAY_SECONDS = (
        0.0  # Minimum time to show progress bar (can be changed for testing)
    )

    def __init__(
        self,
        archive_path: Optional[str] = None,
        patient_selection: Optional[str] = None,
        plan_selection: Optional[str] = None,
        trial_selection: Optional[str] = None,
    ) -> None:
        """
        Initialize the main view and optionally auto-load a source if provided via CLI.

        Args:
            archive_path (Optional[str]): Optional path to a tar file, zip file, or directory to auto-load.
            patient_selection (Optional[str]): Optional patient name or index to select after loading.
            plan_selection (Optional[str]): Optional plan name or index to select after loading.
            trial_selection (Optional[str]): Optional trial name or index to select after loading.
        """
        # Create the main window
        self.root = ttk.Window(
            title="DICOM Converter - Patient Data Processing",
            size=(1200, 800),
            resizable=(True, True),
            minsize=(900, 600),
        )

        # Main window configuration parameters
        # Minimum delay: The minimum amount of time, in seconds, to view the modal window
        self.minimum_delay: float = 0.0

        # Initialize state variables
        self.patient_selection = patient_selection
        self.plan_selection = plan_selection
        self.trial_selection = trial_selection if trial_selection is not None else "0"
        self.pinnacle_reader = None
        self.current_patient = None
        self.current_plan = None

        # Setup custom theme/styles
        setup_theme(self.root.style)
        self.root.configure(bg=PRIMARY_COLOR)
        # self.root.overrideredirect(True)

        # Configure grid weights
        self.root.grid_rowconfigure(2, weight=1)
        self.root.grid_columnconfigure(1, weight=1)

        # Create components
        self._init_components()

        # Center window on screen
        self._center_window()

        # If CLI args were provided, auto-load the file/directory
        if archive_path:
            self._load_archive(archive_path)

    def set_source_path(self, path: str) -> None:
        """Set the source path in the header panel.

        Args:
            path: The path to display as the source.
        """
        self.header.set_source_path(path)

    def show_progress(
        self, title: str, operation: str, mode: Literal["determinate", "indeterminate"]
    ) -> None:
        """Show the progress modal with the given title, operation, and mode.

        Args:
            title: The title for the progress modal.
            operation: The operation description.
            mode: The progress mode ('determinate' or 'indeterminate').
        """
        self.progress_modal.show(title=title, operation=operation, mode=mode)

    def hide_progress(self) -> None:
        """Hide the progress modal.
        
        Closes the progress modal window and returns focus to the main interface.
        """
        self.progress_modal.hide()

    def after(self, ms: int, func: Callable[[], Any]) -> None:
        """Schedule a function to be called after a given number of milliseconds.

        Args:
            ms: Milliseconds to wait before calling the function.
            func: The function to call.
        """
        self.root.after(ms, func)

    def get_minimum_delay(self) -> float:
        """Get the minimum delay for showing the progress modal.

        Returns:
            The minimum delay in seconds.
        """
        return self.minimum_delay

    def _center_window(self) -> None:
        """Center the window on the screen.
        
        Centers the main application window horizontally and positions it near the top
        of the screen for optimal visibility.
        """
        screen_width = self.root.winfo_screenwidth()
        # screen_height = self.root.winfo_screenheight()
        x = (screen_width - 1200) // 2
        y = 10  # (screen_height - 800) // 2
        self.root.geometry(f"1200x800+{x}+{y}")

    def _init_components(self) -> None:
        """Initialize and place all UI components.
        
        Creates and configures all major UI components including:
        - Header panel with file loading controls
        - Progress modal for background operations
        - Patient plans panel for navigation
        - Main panel with CT viewer and trial controls
        """
        # Header panel (white, minimal)
        self.header = HeaderPanel(
            self.root,
            on_load_tar=self._on_load_tar,
            on_load_zip=self._on_load_zip,
            on_select_directory=self._on_select_directory,
        )
        self.header.grid(
            row=1, column=0, columnspan=2, sticky="nsew", padx=10, pady=(10, 0)
        )

        # Progress modal (centered, hidden by default)
        self.progress_modal = ProgressModal(self.root)
        self.progress_modal.place(relx=0.5, rely=0.5, anchor="center")
        self.progress_modal.hide()

        # Patient plans panel (white, subtle border)
        self.patient_plans_panel = PatientPlansPanel(
            self.root, 
            on_plan_selected=self.on_plan_selected
        )
        self.patient_plans_panel.grid(
            row=2, column=0, sticky="nsew", padx=(10, 0), pady=10
        )

        # Main panel (white, more padding)
        self.main_panel = MainPanel(
            self.root,
            trial_selection=self.trial_selection,
            on_export_dicom=self._on_export_dicom,
            on_save_dicom=self._on_save_dicom,
        )
        self.main_panel.grid(row=2, column=1, sticky="nsew", padx=10, pady=10)

    def _load_archive(self, archive_path: str) -> None:
        """Load an archive by automatically detecting its type (tar, zip, or directory).

        Args:
            archive_path: Path to the archive file or directory.
            
        Note:
            Automatically detects file type based on extension and loads appropriately:
            - .tar/.tar.gz/.tgz/.tar.bz2 files are loaded as TAR archives
            - .zip files are loaded as ZIP archives
            - Directories are loaded directly
            - Unknown extensions default to directory loading
        """
        if os.path.isdir(archive_path):
            self._load_source("dir", archive_path)
        elif archive_path.lower().endswith((".tar", ".tar.gz", ".tgz", ".tar.bz2")):
            self._load_source("tar", archive_path)
        elif archive_path.lower().endswith(".zip"):
            self._load_source("zip", archive_path)
        else:
            # Default to treating as directory if extension is unknown
            self._load_source("dir", archive_path)

    def _load_source(self, source_type: str, path: str) -> None:
        """Load a source (tar, zip, or directory) in a background thread.
        
        Shows the progress bar first, then loads, then sets the source path and hides 
        the progress bar. Ensures the progress bar is visible for at least 
        MIN_PROGRESS_DISPLAY_SECONDS.

        Args:
            source_type: The type of source ('tar', 'zip', or 'dir').
            path: The path to the source file or directory.
            
        Note:
            This method runs the actual loading in a background thread to prevent
            UI blocking. Progress is shown during the operation and patient/plan
            data is automatically loaded into the UI upon successful completion.
        """
        logger.info(f"Starting to load {source_type} source: {path}")

        if source_type == "tar":
            title = "Loading TAR File"
            operation = "Extracting files..."
        elif source_type == "zip":
            title = "Loading ZIP File"
            operation = "Extracting files..."
        elif source_type == "dir":
            title = "Loading Directory"
            operation = "Scanning files..."
        else:
            logger.error(f"Unknown source type: {source_type}")
            return

        self.show_progress(title=title, operation=operation, mode="indeterminate")
        start_time = time.monotonic()

        def background_load() -> None:
            try:
                logger.info(f"{title} in background thread. {operation}")
                self._load_pinnacle_reader(path)
                if self.pinnacle_reader is None:
                    logger.error("Failed to load pinnacle reader")
                    return
                
                logger.info("Service loaded successfully, reading institution data")
                institution = self.pinnacle_reader.get_institution()
                logger.info(
                    f"Institution parsed successfully. Found {len(institution.patient_lite_list)} patients"
                )

                self.patient_plans_panel.set_pinnacle_reader(self.pinnacle_reader)
                self.main_panel.set_pinnacle_reader(self.pinnacle_reader)
                self.patient_plans_panel.clear_patients()
                logger.info("Cleared existing patients from panel")

                for patient_lite in institution.patient_lite_list:
                    patient = self.pinnacle_reader.get_patient(
                        institution=institution,
                        patient=patient_lite
                    )
                    logger.info(
                        f"Patient parsed: {patient.last_and_first_name} (MRN: {patient.medical_record_number})"
                    )
                    logger.info(f"Patient has {len(patient.plan_list)} plans")

                    # Log plan details
                    for j, plan in enumerate(patient.plan_list):
                        logger.info(f"  Plan {j}: {plan.name} (ID: {plan.plan_id})")

                    self.patient_plans_panel.add_patient(institution, patient)
                    logger.info("Added patient to panel")

                # UI updates must be done in the main thread
                self.root.after(0, lambda: self.set_source_path(path))

                # Auto-select patient if specified, otherwise select first patient
                if self.patient_selection is not None:
                    logger.info(f"Patient selection specified: {self.patient_selection}")
                    patient_selection = self.patient_selection
                    self.root.after_idle(lambda: self._select_patient(institution, patient_selection))
                else:
                    logger.info("No patient selection specified, selecting first patient by default")
                    # Select first patient (index 0) by default
                    self.root.after_idle(lambda: self._select_patient(institution, "0"))

            except Exception as e:
                logger.error(f"Error during background load: {e}", exc_info=True)
                self.root.after(
                    0,
                    lambda: self.set_source_path(
                        "Error: Failed to load Pinnacle data."
                    ),
                )
            finally:
                elapsed = time.monotonic() - start_time
                remaining = max(0, self.MIN_PROGRESS_DISPLAY_SECONDS - elapsed)
                logger.info(f"Background load completed in {elapsed:.2f}s")
                self.root.after(int(remaining * 1000), self.hide_progress)

        threading.Thread(target=background_load, daemon=True).start()

    def _load_pinnacle_reader(self, path: str) -> None:
        """Initialize the PinnacleReader with the given path.

        Args:
            path: Path to the Pinnacle data source.
            
        Note:
            Creates a new PinnacleReader instance that can handle TAR archives,
            ZIP files, or directory structures containing Pinnacle data.
        """
        self.pinnacle_reader = PinnacleReader(path)

    def _on_load_tar(self) -> None:
        """Callback method to select and load a TAR file.
        
        Opens a file dialog for the user to select a TAR archive file, then
        initiates the loading process if a file is selected.
        """
        file_path = filedialog.askopenfilename(
            title="Select TAR File",
            filetypes=[
                ("TAR files", "*.tar *.tar.gz *.tgz *.tar.bz2"),
                ("All files", "*.*"),
            ],
        )
        if file_path:
            self._load_archive(file_path)

    def _on_load_zip(self) -> None:
        """Callback method to select and load a ZIP file.
        
        Opens a file dialog for the user to select a ZIP archive file, then
        initiates the loading process if a file is selected.
        """
        file_path = filedialog.askopenfilename(
            title="Select ZIP File",
            filetypes=[("ZIP files", "*.zip"), ("All files", "*.*")],
        )
        if file_path:
            self._load_archive(file_path)

    def _on_select_directory(self) -> None:
        """Callback method to handle directory selection.
        
        Opens a directory dialog for the user to select a folder containing
        Pinnacle data, then initiates the loading process if a directory is selected.
        """
        dir_path = filedialog.askdirectory(title="Select Directory")
        if dir_path:
            self._load_source("dir", dir_path)

    def on_plan_selected(self, institution: Institution, patient: Patient, plan: Plan) -> None:
        """Handle plan selection and update the main panel.
        
        Args:
            institution: The selected Institution object from pinnacle_io.models.
            patient: The selected Patient object from pinnacle_io.models.
            plan: The selected Plan object from pinnacle_io.models.
        """
        logger.info(
            f"Plan clicked/selected: {plan.name} for patient: {getattr(patient, 'last_and_first_name', None)}"
        )

        # Store current patient and plan
        self.current_patient = patient
        self.current_plan = plan

        # Update the main panel with the selected plan and trial
        self.main_panel.on_plan_selected(
            institution,
            patient,
            plan,
            self.trial_selection
        )

    def _select_plan(self, institution: Institution, plan_identifier: str) -> None:
        """Select a plan by name or index.

        Args:
            plan_identifier: Plan name or index (0-based) to select.
            
        Note:
            If the identifier is numeric, it's treated as a zero-based index.
            Otherwise, it's treated as a plan name for exact matching.
        """
        logger.info(f"Attempting to select plan: {plan_identifier}")

        try:
            # Try to parse as index first
            if plan_identifier.isdigit():
                plan_index = int(plan_identifier)
                logger.info(f"Treating '{plan_identifier}' as plan index: {plan_index}")
                logger.info(
                    f"Available plans in panel: {len(self.patient_plans_panel.all_plans)}"
                )

                success = self.patient_plans_panel.select_plan_by_index(institution, plan_index)
                if success:
                    logger.info(f"Successfully selected plan at index {plan_index}")
                else:
                    logger.warning(
                        f"Plan index {plan_index} not found. Available plans: {len(self.patient_plans_panel.all_plans)}"
                    )
                    if self.patient_plans_panel.all_plans:
                        available_plan_names = [
                            name for name, _, _ in self.patient_plans_panel.all_plans
                        ]
                        logger.info(f"Available plan names: {available_plan_names}")
            else:
                # Treat as plan name
                logger.info(f"Treating '{plan_identifier}' as plan name")
                logger.info(
                    f"Available plan buttons: {list(self.patient_plans_panel.plan_buttons.keys())}"
                )

                success = self.patient_plans_panel.select_plan_by_name(institution, plan_identifier)
                if success:
                    logger.info(
                        f"Successfully selected plan by name: {plan_identifier}"
                    )
                else:
                    available_plans = list(self.patient_plans_panel.plan_buttons.keys())
                    logger.warning(
                        f"Plan '{plan_identifier}' not found. Available plans: {available_plans}"
                    )
        except (ValueError, IndexError) as e:
            logger.error(f"Failed to select plan '{plan_identifier}': {e}")

    def _select_patient(self, institution: Institution, patient_identifier: str) -> None:
        """Select a patient by name or index, then auto-select plan.

        Args:
            institution: The selected Institution object from pinnacle_io.models.
            patient_identifier: Patient name or index (0-based) to select.
            
        Note:
            If the identifier is numeric, it's treated as a zero-based index.
            Otherwise, it's treated as a patient name (last_and_first_name) for exact matching.
            After patient selection, automatically attempts to select a plan.
        """
        logger.info(f"Attempting to select patient: {patient_identifier}")

        try:
            # Try to parse as index first
            if patient_identifier.isdigit():
                patient_index = int(patient_identifier)
                logger.info(f"Treating '{patient_identifier}' as patient index: {patient_index}")
                logger.info(
                    f"Available patients in panel: {len(self.patient_plans_panel.patients)}"
                )

                success = self.patient_plans_panel.select_patient_by_index(patient_index)
                if success:
                    logger.info(f"Successfully selected patient at index {patient_index}")
                    # After selecting patient, auto-select plan
                    self._auto_select_plan(institution)
                else:
                    logger.warning(
                        f"Patient index {patient_index} not found. Available patients: {len(self.patient_plans_panel.patients)}"
                    )
                    if self.patient_plans_panel.patients:
                        available_patient_names = [
                            p.last_and_first_name for p in self.patient_plans_panel.patients
                        ]
                        logger.info(f"Available patient names: {available_patient_names}")
            else:
                # Treat as patient name
                logger.info(f"Treating '{patient_identifier}' as patient name")
                
                success = self.patient_plans_panel.select_patient_by_name(patient_identifier)
                if success:
                    logger.info(
                        f"Successfully selected patient by name: {patient_identifier}"
                    )
                    # After selecting patient, auto-select plan
                    self._auto_select_plan(institution)
                else:
                    available_patients = [p.last_and_first_name for p in self.patient_plans_panel.patients]
                    logger.warning(
                        f"Patient '{patient_identifier}' not found. Available patients: {available_patients}"
                    )
        except (ValueError, IndexError) as e:
            logger.error(f"Failed to select patient '{patient_identifier}': {e}")

    def _auto_select_plan(self, institution: Institution) -> None:
        """Auto-select plan after patient selection.
        
        Automatically selects a plan based on the plan_selection parameter
        provided during initialization, or defaults to the first plan (index 0)
        if no specific plan selection was specified.
        """
        if self.plan_selection is not None:
            logger.info(f"Plan selection specified: {self.plan_selection}")
            plan_sel = self.plan_selection  # Type narrowing for mypy
            self.root.after_idle(lambda: self._select_plan(institution, plan_sel))
        else:
            logger.info("No plan selection specified, selecting first plan by default")
            # Select first plan (index 0) by default
            self.root.after_idle(lambda: self._select_plan(institution, "0"))

    def _on_export_dicom(self) -> None:
        """Handle Export DICOM from MainPanel.
        
        Shows progress modal and runs DICOM export in background thread.
        Prompts user to select export directory and converts Pinnacle data
        to DICOM format using the DicomConverterApp.
        
        Note:
            Requires a patient and plan to be selected. Shows error dialog
            if prerequisites are not met.
        """
        from tkinter import messagebox
        import threading
        from dicom_converter.main import DicomConverterApp
        import os

        patient = self.current_patient
        plan = self.current_plan
        if patient is None or plan is None:
            messagebox.showerror("Export Error", "No patient or plan selected.")
            return

        patient_path = getattr(patient, "patient_path", None)
        if not patient_path or not os.path.isdir(patient_path):
            messagebox.showerror(
                "Export Error", f"Patient path not found: {patient_path}"
            )
            return

        input_dir = os.path.dirname(patient_path)

        export_dir = filedialog.askdirectory(
            title="Select DICOM Export Directory",
            initialdir=self.header.get_source_path(),
        )
        if not export_dir:
            return  # User cancelled
        if not os.path.isdir(export_dir):
            messagebox.showerror(
                "Export Error", f"Selected directory does not exist: {export_dir}"
            )
            return

        def do_export():
            try:
                self.root.after(
                    0,
                    lambda: self.show_progress(
                        title="Exporting DICOM...",
                        operation="Converting Pinnacle data to DICOM...",
                        mode="indeterminate",
                    ),
                )
                app = DicomConverterApp.convert_directory(input_dir, export_dir)
                app.convert()
                self.root.after(0, self.hide_progress)
                messagebox.showinfo(
                    "Export Complete",
                    f"DICOM export completed successfully to {export_dir}",
                )
            except Exception as e:
                self.root.after(0, self.hide_progress)
                messagebox.showerror("Export Error", f"DICOM export failed: {e}")

        threading.Thread(target=do_export, daemon=True).start()

    def _on_save_dicom(self) -> None:
        """Handle Save DICOM from MainPanel.
        
        Shows progress modal and runs DICOM save in background thread.
        Prompts user to select save directory and converts Pinnacle data
        to DICOM format using the DicomConverterApp.
        
        Note:
            Requires a patient and plan to be selected. Shows error dialog
            if prerequisites are not met.
        """
        from tkinter import messagebox
        import threading
        from dicom_converter.main import DicomConverterApp
        import os

        patient = self.current_patient
        plan = self.current_plan
        if patient is None or plan is None:
            messagebox.showerror("Save Error", "No patient or plan selected.")
            return

        patient_path = getattr(patient, "patient_path", None)
        if not patient_path or not os.path.isdir(patient_path):
            messagebox.showerror(
                "Save Error", f"Patient path not found: {patient_path}"
            )
            return

        input_dir = os.path.dirname(patient_path)

        save_dir = filedialog.askdirectory(
            title="Select Directory to Save DICOM Files",
            initialdir=self.header.get_source_path(),
        )
        if not save_dir:
            return  # User cancelled
        if not os.path.isdir(save_dir):
            messagebox.showerror(
                "Save Error", f"Selected directory does not exist: {save_dir}"
            )
            return

        def do_save():
            try:
                self.root.after(
                    0,
                    lambda: self.show_progress(
                        title="Saving DICOM...",
                        operation="Converting Pinnacle data to DICOM files...",
                        mode="indeterminate",
                    ),
                )
                app = DicomConverterApp.convert_directory(input_dir, save_dir)
                app.convert()
                self.root.after(0, self.hide_progress)
                messagebox.showinfo(
                    "Save Complete",
                    f"DICOM files saved successfully to {save_dir}",
                )
            except Exception as e:
                self.root.after(0, self.hide_progress)
                messagebox.showerror("Save Error", f"DICOM save failed: {e}")

        threading.Thread(target=do_save, daemon=True).start()

    def run(self) -> None:
        """Start the application main loop.
        
        Begins the tkinter event loop to display the UI and handle user interactions.
        This method blocks until the user closes the application window.
        """
        self.root.mainloop()


if __name__ == "__main__":
    app = MainView()
    app.run()
