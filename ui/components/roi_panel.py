import ttkbootstrap as ttk  # type: ignore
from ttkbootstrap.scrolled import Scrolled<PERSON>rame  # type: ignore
import tkinter as tk
from tkinter import Widget
import os
from typing import List, Optional, Any
from dicom_converter.utils.constants import HEX_COLORS
from pinnacle_io import Patient, Plan, Institution


class ROIsPanel(ttk.Frame):
    """ROI (Region of Interest) panel for displaying and managing ROI structures.
    
    This panel provides an interface for viewing and selecting ROIs from a treatment plan.
    ROIs are displayed with checkboxes, color indicators, and names in a scrollable list.
    
    Attributes:
        pinnacle_reader: PinnacleReader instance for data access
        check_vars: List of BooleanVar objects for ROI checkbox states
    """
    
    def __init__(self, parent: Widget, pinnacle_reader: Optional[Any] = None, *args: Any, **kwargs: Any) -> None:
        """Initialize the ROIsPanel.
        
        Args:
            parent: Parent widget for this panel
            pinnacle_reader: Optional PinnacleReader instance for data access
            *args: Variable length argument list for ttk.Frame
            **kwargs: Arbitrary keyword arguments for ttk.Frame
        """
        super().__init__(parent, *args, **kwargs)
        self.pinnacle_reader = pinnacle_reader
        self.scrolled_frame = ScrolledFrame(self, autohide=True)
        self.scrolled_frame.pack(fill="both", expand=True)
        self.check_vars: List[tk.BooleanVar] = []
        # Note: Overwrite the parameter with None, will be set properly via set_pinnacle_reader
        self.pinnacle_reader: Optional[Any] = None

    def clear_rois(self) -> None:
        """Clear all ROI widgets and reset checkbox variables.
        
        Destroys all child widgets in the scrolled frame and clears the
        checkbox variable list.
        """
        for widget in self.scrolled_frame.winfo_children():
            widget.destroy()
        self.check_vars.clear()

    def set_pinnacle_reader(self, pinnacle_reader: Any) -> None:
        """Set the PinnacleReader instance for this panel.
        
        Args:
            pinnacle_reader: PinnacleReader instance for accessing ROI data
        """
        self.pinnacle_reader = pinnacle_reader

    def add_roi(self, name: str, color: str) -> None:
        """Add a new ROI to the panel with the given name and color.
        
        Args:
            name: Name of the ROI to add
            color: Color string (must be a valid key in HEX_COLORS)
        """
        var = tk.BooleanVar(value=True)  # New ROIs are checked by default
        color_hex = HEX_COLORS.get(color.lower(), "#888888")
        frame = ttk.Frame(self.scrolled_frame)
        frame.pack(fill="x", padx=4, pady=2)
        
        cb = ttk.Checkbutton(frame, variable=var)
        cb.pack(side="left", anchor="w")
        
        color_canvas = tk.Canvas(frame, width=16, height=16, highlightthickness=0, bd=0)
        color_canvas.create_rectangle(2, 2, 14, 14, fill=color_hex, outline=color_hex)
        color_canvas.pack(side="left", padx=(0, 2))
        
        name_label = ttk.Label(frame, text=name)
        name_label.pack(side="left", anchor="w")
        
        self.check_vars.append(var)
        
        # # Make sure the new ROI is visible by scrolling to it
        # self.scrolled_frame.update_idletasks()
        # self.scrolled_frame.yview_moveto(1.0)

    def load_rois(self, institution: Institution, patient: Patient, plan: Plan) -> None:
        """Load and display ROIs for the selected patient and plan.
        
        Retrieves ROI data from the plan.roi file and creates UI elements
        for each ROI including checkboxes, color indicators, and name labels.
        
        Args:
            institution: Institution object with institution_path attribute
            patient: Patient object with patient_path attribute
            plan: Plan object with plan_id attribute
        """
        self.clear_rois()
        if not hasattr(patient, "patient_path") or not hasattr(plan, "plan_id"):
            return
        plan_path = os.path.join(patient.patient_path, f"Plan_{plan.plan_id}")
        try:
            if self.pinnacle_reader is None:
                return
            roi_list = self.pinnacle_reader.get_rois(institution, patient, plan)
            for roi in roi_list:
                var = tk.BooleanVar(value=False)
                color = getattr(roi, "color", "gray")
                color_hex = HEX_COLORS.get(color, "#888888")
                frame = ttk.Frame(self.scrolled_frame)
                frame.pack(fill="x", padx=4, pady=2)
                cb = ttk.Checkbutton(frame, variable=var)
                cb.pack(side="left", anchor="w")
                color_canvas = tk.Canvas(frame, width=16, height=16, highlightthickness=0, bd=0)
                color_canvas.create_rectangle(2, 2, 14, 14, fill=color_hex, outline=color_hex)
                color_canvas.pack(side="left", padx=(0, 2))
                name_label = ttk.Label(frame, text=roi.name)
                name_label.pack(side="left", anchor="w")
                self.check_vars.append(var)
        except Exception as e:
            print(f"Failed to load ROIs from {plan_path}: {e}")
