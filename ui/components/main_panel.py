from tkinter import messagebox, Widget, Toplevel
from typing import Optional, Callable, Any, List, Union
import ttkbootstrap as ttk  # type: ignore
from ttkbootstrap.tooltip import ToolTip  # type: ignore
from ttkbootstrap.constants import *  # type: ignore
import logging
import os

from pinnacle_io import Institution, Patient, PinnacleReader, Plan, Trial

from ui.theme import *
from ui.components.roi_panel import ROIsPanel
from ui.components.poi_panel import POIsPanel
from ui.components.beam_panel import BeamsPanel
from ui.components.ct_viewer import CTViewer

logger = logging.getLogger(__name__)


class MainPanel(ttk.Frame):
    """Main panel component containing CT viewer and related controls.
    
    This panel provides the primary interface for viewing CT images and managing
    trials, ROIs, POIs, and beam information for a selected plan.
    
    Attributes:
        trial_selection: Optional trial identifier for initial selection
        current_plan: Currently selected treatment plan
        current_patient: Currently selected patient
        pinnacle_reader: PinnacleReader instance for data access
        on_export_dicom: Callback function for DICOM export operations
        on_save_dicom: Callback function for DICOM save operations
    """
    
    def __init__(
        self, 
        parent: ttk.Window | Toplevel | Widget, 
        trial_selection: Optional[Union[str, int]] = None, 
        on_export_dicom: Optional[Callable[[], None]] = None, 
        on_save_dicom: Optional[Callable[[], None]] = None
    ) -> None:
        """Initialize the MainPanel.
        
        Args:
            parent: Parent widget for this panel
            trial_selection: Optional trial identifier for initial selection
            on_export_dicom: Optional callback function for DICOM export
            on_save_dicom: Optional callback function for DICOM save
        """
        super().__init__(parent, style="CustomMain.TFrame")
        self.trial_selection: Optional[Union[str, int]] = trial_selection
        self.current_plan: Any = None
        self.current_patient: Any = None
        self.pinnacle_reader: Optional[PinnacleReader] = None
        self.on_export_dicom: Optional[Callable[[], None]] = on_export_dicom
        self.on_save_dicom: Optional[Callable[[], None]] = on_save_dicom
        self._trials: List[Any] = []
        
        # UI components (initialized in _create_widgets)
        self.paned: ttk.PanedWindow
        self.sidebar: ttk.Frame
        self.details_header: ttk.Label
        self.trial_combobox: ttk.Combobox
        self.save_button: ttk.Button
        self.export_button: ttk.Button
        self.notebook: ttk.Notebook
        self.rois_panel: ROIsPanel
        self.pois_panel: POIsPanel
        self.beams_panel: BeamsPanel
        self.ct_viewer: CTViewer
        
        self.configure(style="CustomMain.TFrame", padding=0)
        self._create_widgets()

    def set_pinnacle_reader(self, pinnacle_reader: PinnacleReader) -> None:
        """Set the PinnacleReader instance for this panel.
        
        Args:
            pinnacle_reader: PinnacleReader instance for data access
        """
        self.pinnacle_reader = pinnacle_reader
        self.rois_panel.set_pinnacle_reader(pinnacle_reader)
        self.pois_panel.set_pinnacle_reader(pinnacle_reader)

    def _create_widgets(self) -> None:
        """Create and configure all UI widgets for the main panel.
        
        Creates the main layout with sidebar containing trial selection,
        export buttons, and tabbed panels for ROIs/POIs/Beams, plus
        the CT viewer in the main area.
        """
        logger.debug("Creating MainPanel widgets.")
        # Create a horizontal PanedWindow
        self.paned = ttk.PanedWindow(self, orient=HORIZONTAL)
        self.paned.pack(fill="both", expand=True)

        # Left pane: sidebar
        self.sidebar = ttk.Frame(self.paned, width=300, style="CustomMain.TFrame")
        self.sidebar.pack_propagate(False)
        self.paned.add(self.sidebar, weight=1)

        # Plan label at top
        self.details_header = ttk.Label(
            self.sidebar, text="No Plan Selected", style="CustomHeader.TLabel"
        )
        self.details_header.pack(side="top", anchor="w", padx=16, pady=(10, 8))

        # Trial label and combobox
        trial_frame = ttk.Frame(self.sidebar, style="CustomMain.TFrame")
        trial_frame.pack(side="top", fill="x", padx=16, pady=(0, 8))
        ttk.Label(trial_frame, text="Trial:", style="CustomHeader.TLabel").pack(
            side="left"
        )
        self.trial_combobox = ttk.Combobox(trial_frame, values=[], style="TCombobox")
        self.trial_combobox.pack(side="left", fill="x", expand=True, padx=(8, 0))
        self.trial_combobox.bind("<<ComboboxSelected>>", self._on_trial_selected)
        logger.debug("Trial combobox and export button created.")

        # Export DICOM buttons
        export_frame = ttk.Frame(self.sidebar, style="CustomMain.TFrame")
        export_frame.pack(side="top", fill="x", padx=16, pady=(8, 16))

        # Try to use unicode icons, fallback to text only if not supported
        try:
            save_icon = "\U0001f4be"  # Floppy disk icon
            send_icon = "\U0001f4e4"  # Outbox tray icon
        except Exception:
            save_icon = ""
            send_icon = ""

        # Save DICOM button (left side)
        self.save_button = ttk.Button(
            export_frame,
            text=f"{save_icon} Save DICOM" if save_icon else "Save DICOM",
            style="CustomPrimary.TButton",
            command=self._handle_save_dicom,
        )
        self.save_button.pack(side="left", fill="x", expand=True, padx=(0, 4))

        # Export DICOM button (right side)
        self.export_button = ttk.Button(
            export_frame,
            text=f"{send_icon} Export DICOM" if send_icon else "Export DICOM",
            style="CustomPrimary.TButton",
            command=self._handle_export_dicom,
        )
        self.export_button.pack(side="left", fill="x", expand=True, padx=(4, 0))

        # Add tooltips
        self._create_tooltips()

        # Notebook for ROIs, POIs, Beams
        self.notebook = ttk.Notebook(self.sidebar, style="TNotebook")
        self.notebook.pack(side="top", fill="both", expand=True, padx=16, pady=(0, 16))
        self.notebook.config(width=300)
        self.rois_panel = ROIsPanel(self.notebook, self.pinnacle_reader)
        self.pois_panel = POIsPanel(self.notebook, self.pinnacle_reader)
        self.beams_panel = BeamsPanel(self.notebook)
        self.notebook.add(self.rois_panel, text="ROIs")
        self.notebook.add(self.pois_panel, text="POIs")
        self.notebook.add(self.beams_panel, text="Beams")

        # Right pane: CT viewer
        self.ct_viewer = CTViewer(self.paned)
        self.paned.add(self.ct_viewer, weight=3)

    def update_plan_details(self, patient: Any = None, plan: Any = None) -> None:
        """Update the plan details header display.
        
        Args:
            patient: Patient object (currently unused)
            plan: Plan object with name attribute
        """
        logger.info(
            f"Updating plan details header. Plan: {getattr(plan, 'name', None)}"
        )
        if plan:
            self.details_header.configure(text=f"Plan: {plan.name}")
        else:
            self.details_header.configure(text="No Plan Selected")

    def on_plan_selected(
        self, 
        institution: Institution,
        patient: Patient, 
        plan: Plan, 
        trial_selection: Optional[Union[str, int]] = None
    ) -> None:
        """Handle plan selection from the main view.
        
        Updates the current patient and plan, loads CT images, and populates
        all related panels with trial, ROI, and POI data.
        
        Args:
            patient: Patient object containing patient information
            plan: Plan object containing treatment plan details
            trial_selection: Optional trial identifier for initial selection
        """
        logger.info(
            f"MainPanel received plan selection: {plan.name} for patient: {getattr(patient, 'last_and_first_name', None)}"
        )
        self.current_patient = patient
        self.current_plan = plan
        self.update_plan_details(patient=patient, plan=plan)
        logger.debug("Clearing ROIs and loading CT image for new plan selection.")
        self.clear_rois()
        self.load_ct_image_set(institution, patient, plan)  # Load CT image set
        logger.debug("Loading trials, ROIs, and POIs for selected plan.")
        self.load_trials(institution, patient, plan, trial_selection)
        self.load_rois(institution, patient, plan)
        self.load_pois(institution, patient, plan)

    def load_trials(
        self, 
        institution: Institution,
        patient: Patient, 
        plan: Plan, 
        trial_selection: Optional[Union[str, int]] = None
    ) -> None:
        """Load and populate trials for the selected plan.
        
        Reads trial data from the plan.Trial file and populates the trial
        combobox with available trials. Selects a default trial based on
        the trial_selection parameter or defaults to the first trial.
        
        Args:
            patient: Patient object with patient_path attribute
            plan: Plan object with plan_id attribute
            trial_selection: Optional trial identifier (name or index)
        """
        self._trials = []

        if self.pinnacle_reader is None:
            logger.error("PinnacleReader not initialized.")
            return
        
        if not hasattr(patient, "patient_path") or not patient.patient_path:
            logger.error("Patient path not found.")
            return
        
        if not hasattr(plan, "plan_id") or plan.plan_id is None:
            logger.error("Plan ID not found.")
            return
        
        logger.info(
            f"Loading trials for patient: {getattr(patient, 'last_and_first_name', None)}, plan: {getattr(plan, 'name', None)}"
        )

        # Load plan.Trial file and populate trial combobox
        plan_path = os.path.join(patient.patient_path, f"Plan_{plan.plan_id}")
        try:
            trials = self.pinnacle_reader.get_trials(institution, patient, plan)
            self._trials = trials
            trial_names = [trial.name for trial in trials]
            self.trial_combobox["values"] = trial_names
            logger.info(f"Found {len(trials)} trials: {trial_names}")
            # Determine which trial to select
            selected_trial = None
            if trial_selection is not None:
                if isinstance(trial_selection, int):
                    if 0 <= trial_selection < len(trials):
                        selected_trial = trials[trial_selection]
                elif trial_selection.isdigit():
                    idx = int(trial_selection)
                    if 0 <= idx < len(trials):
                        selected_trial = trials[idx]
                else:
                    selected_trial = next(
                        (
                            trial
                            for trial in trials
                            if trial.name == trial_selection
                        ),
                        None,
                    )
            if not selected_trial and trials:
                selected_trial = trials[0]

            self.trial_combobox.set(selected_trial.name if selected_trial else "")
            if selected_trial:
                logger.info(f"Defaulting to trial: {selected_trial.name}")
                self.show_trial(selected_trial)
            logger.info(
                f"Loaded {len(trial_names)} trials, selected: {selected_trial.name if selected_trial else None}"
            )
        except Exception as e:
            logger.error(
                f"Failed to load or parse plan.Trial file at {plan_path}: {e}"
            )
            self.trial_combobox["values"] = []
            self.trial_combobox.set("")
            self._trials = []

    def _on_trial_selected(self, event: Any = None) -> None:
        """Handle trial selection change in combobox.
        
        Args:
            event: Tkinter event object (unused)
        """
        selected_name = self.trial_combobox.get()
        logger.info(f"Trial combobox selection changed to: {selected_name}")
        selected_trial = next(
            (
                trial
                for trial in getattr(self, "_trials", [])
                if trial.name == selected_name
            ),
            None,
        )
        if selected_trial:
            logger.info(
                f"Found selected trial: {selected_trial.name}, updating display."
            )
            self.show_trial(selected_trial)
        else:
            logger.warning(
                f"Selected trial '{selected_name}' not found in loaded trials."
            )

    def load_beams(self, trial: Trial) -> None:
        """Load beam information for the specified trial.
        
        Args:
            trial: Trial object containing beam data
        """
        logger.debug(f"Loading beams for trial: {getattr(trial, 'name', None)}")
        self.beams_panel.load_beams(trial)

    def load_rois(self, institution: Institution, patient: Patient, plan: Plan) -> None:
        """Load ROI (Region of Interest) data for the selected plan.
        
        Args:
            institution: Institution object with institution_path attribute
            plan: Plan object with plan_id attribute
        """
        logger.debug(
            f"Loading ROIs for patient: {getattr(patient, 'last_and_first_name', None)}, plan: {getattr(plan, 'name', None)}"
        )
        self.rois_panel.load_rois(institution, patient, plan)

    def load_pois(self, institution: Institution, patient: Patient, plan: Plan) -> None:
        """Load POI (Point of Interest) data for the selected plan.
        
        Args:
            institution: Institution object with institution_path attribute
            patient: Patient object with patient_path attribute
            plan: Plan object with plan_id attribute
        """
        logger.debug(
            f"Loading POIs for patient: {getattr(patient, 'last_and_first_name', None)}, plan: {getattr(plan, 'name', None)}"
        )
        self.pois_panel.load_pois(institution, patient, plan)

    def load_ct_image_set(self, institution: Institution, patient: Patient, plan: Plan) -> None:
        """Load the planning CT image set for the selected plan.
        
        Retrieves the primary CT image set from the plan and loads it into
        the CT viewer for display and interaction.
        
        Args:
            institution: Institution object with institution_path attribute
            patient: Patient object with patient_path attribute
            plan: Plan object with primary_ct_image_set_id attribute
        """
        if not self.pinnacle_reader or not patient or not plan:
            logger.warning("Cannot load CT: missing pinnacle_reader, patient, or plan")
            self.ct_viewer.clear_image()
            return

        try:
            logger.info(f"Loading planning CT for plan: {plan.name}")

            # Get the primary CT image set ID from the plan
            if (
                not hasattr(plan, "primary_ct_image_set_id")
                or plan.primary_ct_image_set_id is None
            ):
                logger.warning(f"Plan {plan.name} has no primary CT image set ID")
                self.ct_viewer.clear_image()
                return

            # Load the image set using the pinnacle_reader
            image_set = self.pinnacle_reader.get_image_set(
                institution, patient, plan.primary_ct_image_set_id
            )

            if image_set:
                # Use CT image set ID as unique identifier for caching check
                # This ensures plans sharing the same planning CT reuse the same image and preserve view state
                ct_identifier = f"{patient.patient_path}_{plan.primary_ct_image_set_id}"
                self.ct_viewer.load_ct_image_set(image_set, ct_identifier)
                logger.info(
                    f"Successfully loaded CT with {len(image_set.image_info_list) if image_set.image_info_list else 'unknown'} slices"
                )
            else:
                logger.warning(
                    f"Failed to load planning CT for plan {plan.name} with image set ID {plan.primary_ct_image_set_id}"
                )
                self.ct_viewer.clear_image()

        except Exception as e:
            logger.error(f"Failed to load planning CT for plan {plan.name}: {e}")
            self.ct_viewer.clear_image()

    def set_ct_image(self, image: Any = None) -> None:
        """Set the CT image display (legacy method).
        
        Args:
            image: Image data to display, or None to clear
        """
        logger.debug(f"Setting CT image. Image provided: {image is not None}")
        if image:
            self.ct_viewer.set_image(image)
        else:
            self.ct_viewer.clear_image()

    def add_roi(self, name: str, color: str) -> None:
        """Add an ROI to the structures list.
        
        Args:
            name: Name of the ROI
            color: Color identifier for the ROI
        """
        logger.debug(f"Adding ROI: {name}, color: {color}")
        self.rois_panel.add_roi(name, color)

    def clear_rois(self) -> None:
        """Clear all ROIs from the panel."""
        logger.debug("Clearing all ROIs from the panel.")
        self.rois_panel.clear_rois()

    def show_trial(self, trial: Any) -> None:
        """Display trial information and update all relevant panels.
        
        Updates the trial combobox selection and refreshes the beams panel
        with data from the specified trial.
        
        Args:
            trial: Trial object with name attribute
        """
        logger.info(f"Displaying trial: {getattr(trial, 'name', None)}")
        # Set combobox value if needed
        if self.trial_combobox.get() != getattr(trial, "name", ""):
            self.trial_combobox.set(getattr(trial, "name", ""))
        # Update beams panel
        logger.debug(f"Updating beams panel for trial: {getattr(trial, 'name', None)}")
        self.load_beams(trial)
        # Optionally, update other panels or display trial details here
        # Example: display trial name in a label, or show more trial info
        # (Extend here as needed for your UI)

    def _create_tooltips(self) -> None:
        """Create tooltips for the export buttons.
        
        Attempts to create tooltips using ttkbootstrap.tooltip.ToolTip.
        Falls back gracefully if the tooltip module is not available.
        """
        try:
            ToolTip(self.save_button, text="Save DICOM files to a local directory")
            ToolTip(
                self.export_button, text="Export DICOM files to a DICOM destination"
            )
        except ImportError:
            # Fallback if ToolTip is not available
            pass

    def _handle_save_dicom(self) -> None:
        """Handle Save DICOM button click.
        
        Calls the configured save callback if available, otherwise shows
        a placeholder dialog.
        """
        logger.info("Save DICOM button clicked.")
        if self.on_save_dicom:
            self.on_save_dicom()
        else:
            # Placeholder dialog if no handler is set
            messagebox.showinfo(
                "Save DICOM",
                "Save DICOM functionality will be implemented here.\n\nThis will save DICOM files to a selected directory.",
            )

    def _handle_export_dicom(self) -> None:
        """Handle Export DICOM button click.
        
        Calls the configured export callback if available, otherwise shows
        a placeholder dialog.
        """
        logger.info("Export DICOM button clicked.")
        if self.on_export_dicom:
            self.on_export_dicom()
        else:
            # Placeholder dialog if no handler is set
            messagebox.showinfo(
                "Export DICOM",
                "Export DICOM functionality will be implemented here.\n\nThis will export DICOM files to a DICOM destination.",
            )
