import ttkbootstrap as ttk  # type: ignore
from tkinter import Toplevel, Widget
from typing import Literal, Optional, Callable


class ProgressModal(ttk.Frame):
    """Progress modal dialog for displaying operation progress and status.
    
    This modal provides a user interface for showing progress during long-running
    operations such as file loading, conversion, or data processing. It supports
    both determinate and indeterminate progress modes.
    
    Attributes:
        is_visible: Boolean flag indicating if the modal is currently displayed
        progress: Current progress value (0-100)
        current_step: Description of the current processing step
        total_steps: Total number of steps in the operation
    """
    
    def __init__(self, parent: ttk.Window | Toplevel | Widget) -> None:
        """Initialize the ProgressModal.
        
        Args:
            parent: Parent widget for this modal dialog
        """
        super().__init__(
            parent, style="CustomProgress.TFrame", borderwidth=1, relief="solid"
        )

        # Modal state
        self.is_visible: bool = False
        self.progress: float = 0.0
        self.current_step: str = ""
        self.total_steps: int = 0

        self.configure(style="CustomProgress.TFrame", padding=0)
        self._create_widgets()
        self.hide()  # Start hidden

    def _create_widgets(self) -> None:
        """Create and configure all UI widgets for the progress modal.
        
        Creates the modal layout with title bar, operation description,
        progress bar with percentage label, and cancel button.
        """
        # Title bar (blue, flat)
        title_frame = ttk.Frame(self, style="CustomPrimary.TFrame")
        title_frame.pack(side="top", fill="x")

        self.title_label = ttk.Label(
            title_frame, text="Processing...", style="CustomTitle.TLabel"
        )
        self.title_label.pack(side="left", padx=16, pady=8)

        # Content area (flat, more whitespace)
        content_frame = ttk.Frame(self, style="CustomMain.TFrame")
        content_frame.pack(side="top", fill="both", expand=True, padx=32, pady=24)

        # Operation description
        self.operation_label = ttk.Label(
            content_frame, text="", style="CustomHeader.TLabel"
        )
        self.operation_label.pack(side="top", anchor="w", pady=(0, 5))

        self.step_label = ttk.Label(content_frame, text="", style="CustomSmall.TLabel")
        self.step_label.pack(side="top", anchor="w", pady=(0, 10))

        # Progress bar
        self.progress_bar = ttk.Progressbar(
            content_frame,
            style="CustomPrimary.Horizontal.TProgressbar",
            mode="determinate",
            length=400,
        )
        self.progress_bar.pack(side="top", fill="x", pady=(0, 5))

        self.progress_label = ttk.Label(
            content_frame, text="0%", style="CustomLabel.TLabel"
        )
        self.progress_label.pack(side="top", pady=(0, 10))

        # Cancel button
        self.cancel_button = ttk.Button(
            content_frame,
            text="Cancel",
            style="CustomPrimary.TButton",
            command=self._on_cancel,
        )
        self.cancel_button.pack(side="bottom", pady=10)

    def show(
        self,
        title: str = "Processing...",
        operation: str = "",
        mode: Literal["determinate", "indeterminate"] = "determinate",
    ) -> None:
        """Show the modal with given title and operation.

        Args:
            title: The title to display in the modal header
            operation: The operation description to display
            mode: Progress bar mode - determinate shows percentage, indeterminate shows animation
        """
        self.title_label.configure(text=title)
        self.operation_label.configure(text=operation)
        self.progress = 0
        self.progress_bar["value"] = 0
        self.step_label.configure(text="")

        # Configure progress bar mode
        self.progress_bar.configure(mode=mode)
        if mode == "indeterminate":
            self.progress_label.configure(text="")
            self.progress_bar.start()
        else:
            self.progress_label.configure(text="0%")
            self.progress_bar.stop()
            self.progress_bar.configure(mode="determinate")

        if not self.is_visible:
            self.place(relx=0.5, rely=0.5, anchor="center")
            self.lift()
            self.is_visible = True

    def hide(self) -> None:
        """Hide the modal and cleanup progress bar state.
        
        Stops any running progress bar animation, resets to determinate mode,
        and removes the modal from the display.
        """
        if self.is_visible:
            self.progress_bar.stop()
            self.progress_bar.configure(mode="determinate")
            self.place_forget()
            self.is_visible = False

    def set_progress(self, value: float, step: Optional[str] = None) -> None:
        """Update progress bar value and optionally the current step description.
        
        Args:
            value: Progress value between 0-100
            step: Optional description of the current processing step
        """
        self.progress = min(max(value, 0), 100)  # Clamp between 0-100
        self.progress_bar["value"] = self.progress
        self.progress_label.configure(text=f"{int(self.progress)}%")

        if step:
            self.current_step = step
            self.step_label.configure(text=step)

    def set_total_steps(self, total: int) -> None:
        """Set the total number of steps for progress calculation.
        
        Args:
            total: Total number of steps in the operation
        """
        self.total_steps = total

    def increment_step(self, step_description: Optional[str] = None) -> None:
        """Increment progress by one step based on total_steps.
        
        Automatically calculates the progress increment based on the total
        number of steps set via set_total_steps().
        
        Args:
            step_description: Optional description of the completed step
        """
        if self.total_steps > 0:
            increment = 100.0 / self.total_steps
            self.set_progress(self.progress + increment, step_description)

    def _on_cancel(self) -> None:
        """Handle cancel button click.
        
        Default implementation hides the modal. Override this method or use
        set_cancel_callback() to provide custom cancel handling.
        """
        # Override this in subclass or set callback
        self.hide()

    def set_cancel_callback(self, callback: Callable[[], None]) -> None:
        """Set the callback function for the cancel button.
        
        Args:
            callback: Function to call when the cancel button is clicked
        """
        self.cancel_button.configure(command=callback)
