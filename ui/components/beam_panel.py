import ttkbootstrap as ttk  # type: ignore
from ttkbootstrap.scrolled import ScrolledFrame  # type: ignore
import tkinter as tk
from tkinter import Widget
from typing import List, Optional, Any
from dicom_converter.utils.constants import HEX_COLORS


class BeamsPanel(ttk.Frame):
    """Beams panel for displaying and managing radiation beam information.
    
    This panel provides an interface for viewing and selecting beams from a treatment trial.
    Beams are displayed with checkboxes, color indicators, field IDs, and names in a scrollable list.
    
    Attributes:
        pinnacle_reader: PinnacleReader instance for data access (currently not used)
        check_vars: List of BooleanVar objects for beam checkbox states
    """
    
    def __init__(self, parent: Widget, *args: Any, **kwargs: Any) -> None:
        """Initialize the BeamsPanel.
        
        Args:
            parent: Parent widget for this panel
            *args: Variable length argument list for ttk.Frame
            **kwargs: Arbitrary keyword arguments for ttk.Frame
        """
        super().__init__(parent, *args, **kwargs)
        self.scrolled_frame = ScrolledFrame(self, autohide=True)
        self.scrolled_frame.pack(fill="both", expand=True)
        self.check_vars: List[tk.BooleanVar] = []
        self.pinnacle_reader: Optional[Any] = None

    def clear_beams(self) -> None:
        """Clear all beam widgets and reset checkbox variables.
        
        Destroys all child widgets in the scrolled frame and clears the
        checkbox variable list.
        """
        for widget in self.scrolled_frame.winfo_children():
            widget.destroy()
        self.check_vars.clear()

    def set_pinnacle_reader(self, pinnacle_reader: Any) -> None:
        """Set the PinnacleReader instance for this panel.
        
        Args:
            pinnacle_reader: PinnacleReader instance for accessing beam data
        """
        self.pinnacle_reader = pinnacle_reader

    def load_beams(self, trial: Any) -> None:
        """Load and display beams for the selected trial.
        
        Retrieves beam data from the trial's beam_list attribute and creates UI elements
        for each beam including checkboxes, color indicators, field IDs, and names.
        
        Args:
            trial: Trial object with beam_list attribute containing beam objects
        """
        self.clear_beams()
        if not hasattr(trial, "beam_list"):
            return
        for beam in getattr(trial, "beam_list", []):
            var = tk.BooleanVar(value=False)
            color = getattr(beam, "color", "gray")
            color_hex = HEX_COLORS.get(color, "#888888")
            field_id = getattr(beam, "field_id", getattr(beam, "id", "?"))
            name = getattr(beam, "name", "Unnamed")
            frame = ttk.Frame(self.scrolled_frame)
            frame.pack(fill="x", padx=4, pady=2)
            cb = ttk.Checkbutton(frame, variable=var)
            cb.pack(side="left", anchor="w")
            color_canvas = tk.Canvas(frame, width=16, height=16, highlightthickness=0, bd=0)
            color_canvas.create_rectangle(2, 2, 14, 14, fill=color_hex, outline=color_hex)
            color_canvas.pack(side="left", padx=(0, 2))
            label_text = f"{field_id} - {name}"
            name_label = ttk.Label(frame, text=label_text)
            name_label.pack(side="left", anchor="w")
            self.check_vars.append(var)
