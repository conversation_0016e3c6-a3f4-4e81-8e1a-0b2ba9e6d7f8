import tkinter as tk
from tkinter import Toplevel, Widget
from typing import Optional, Callable
import ttkbootstrap as ttk  # type: ignore
from ttkbootstrap.constants import *  # type: ignore

from ui.theme import *


class HeaderPanel(ttk.Frame):
    """Header panel component for loading patient data from various sources.
    
    This panel provides the primary interface for loading patient data from
    TAR files, ZIP files, or directories. It displays the current source path
    and provides action buttons for each data loading method.
    
    Attributes:
        on_load_tar: Optional callback function for loading TAR files
        on_load_zip: Optional callback function for loading ZIP files  
        on_select_directory: Optional callback function for selecting directories
    """
    
    def __init__(
        self, 
        parent: ttk.Window | Toplevel | Widget, 
        on_load_tar: Optional[Callable[[], None]] = None, 
        on_load_zip: Optional[Callable[[], None]] = None, 
        on_select_directory: Optional[Callable[[], None]] = None
    ) -> None:
        """Initialize the HeaderPanel.
        
        Args:
            parent: Parent widget for this panel
            on_load_tar: Optional callback function for loading TAR files
            on_load_zip: Optional callback function for loading ZIP files
            on_select_directory: Optional callback function for selecting directories
        """
        super().__init__(parent, style="CustomHeader.TFrame")

        self.on_load_tar = on_load_tar
        self.on_load_zip = on_load_zip
        self.on_select_directory = on_select_directory

        self._source_path_var = tk.StringVar(value="")

        self.configure(style="CustomHeader.TFrame", padding=(0, 10, 0, 10))

        self._create_widgets()

    def set_source_path(self, path: Optional[str]) -> None:
        """Set the source path label text.
        
        Args:
            path: The source path string to display, or None to clear
        """
        self._source_path_var.set(path or "")
    
    def get_source_path(self) -> str:
        """Get the current source path value.
        
        Returns:
            The current source path string, or empty string if not set.
        """
        return self._source_path_var.get()

    def _create_widgets(self) -> None:
        """Create and configure all UI widgets for the header panel.
        
        Creates the main layout with section label, action buttons for loading
        different data sources (TAR, ZIP, directory), and displays the current
        source path.
        """
        # Section label (theme style)
        ttk.Label(self, text="Load Patient Data", style="CustomHeader.TLabel").pack(
            side="top", anchor="w", padx=16, pady=(10, 8)
        )

        # Button row
        button_frame = ttk.Frame(self, style="CustomHeader.TFrame")
        button_frame.pack(side="top", anchor="w", padx=20, pady=(0, 0))

        # Load buttons using custom style
        ttk.Button(
            button_frame,
            text="Load TAR File",
            style="CustomPrimary.TButton",
            command=self.on_load_tar or (lambda: None),
        ).pack(side="left", padx=(0, 10))

        ttk.Button(
            button_frame,
            text="Load ZIP File",
            style="CustomPrimary.TButton",
            command=self.on_load_zip or (lambda: None),
        ).pack(side="left", padx=(0, 10))

        ttk.Button(
            button_frame,
            text="Select Directory",
            style="CustomPrimary.TButton",
            command=self.on_select_directory or (lambda: None),
        ).pack(side="left")

        # Source path row
        source_frame = ttk.Frame(self, style="CustomHeader.TFrame")
        source_frame.pack(side="top", anchor="w", padx=20, pady=(8, 0), fill="x")

        ttk.Label(source_frame, text="Source:", style="CustomHeader.TLabel").pack(
            side="left"
        )

        ttk.Label(
            source_frame, textvariable=self._source_path_var, style="CustomSmall.TLabel"
        ).pack(side="left", padx=(8, 0))
