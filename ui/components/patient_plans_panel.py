from pinnacle_io import ImageSet, Institution, PinnacleReader
from pinnacle_io.models import Patient, Plan
from typing import List, Optional, Callable, Tuple
from tkinter import Toplevel, Widget
import ttkbootstrap as ttk  # type: ignore
from PIL import Image, ImageTk
import numpy as np
import logging
from ui.theme import *

# Configure logging
logger = logging.getLogger(__name__)


class PatientPlansPanel(ttk.Frame):
    """Panel for displaying patients and their treatment plans.
    
    This panel provides a hierarchical view of patients and their associated
    treatment plans, with CT image thumbnails and interactive plan selection.
    
    Attributes:
        pinnacle_reader: Optional PinnacleReader instance for data access.
        patients: List of Patient objects in display order.
        patient_frames: Dictionary mapping patient IDs to their UI frames.
        plan_buttons: Dictionary mapping plan names to their UI elements.
        all_plans: List of all plans in order for index-based selection.
        on_plan_selected: Callback function for plan selection events.
    """
    
    def __init__(self, 
        parent: ttk.Window | Toplevel | Widget, 
        on_plan_selected: Optional[Callable[[Institution, Patient, Plan], None]] = None
    ) -> None:
        """Initialize the PatientPlansPanel.
        
        Args:
            parent: Parent tkinter widget.
            on_plan_selected: Optional callback function called when a plan is selected.
                             Signature: (institution: Institution, patient: Patient, plan: Plan) -> None
        """
        super().__init__(parent, style="CustomSidebar.TFrame")
        self.pinnacle_reader: Optional[PinnacleReader] = None
        self.patients: List[Patient] = []  # Store patient objects in order for index-based selection
        self.patient_frames: dict[int, ttk.Frame] = {}  # Store patient frames for UI management
        self.plan_buttons: dict[str, Tuple[ttk.Button, Patient, Plan]] = {}  # Store plan buttons for selection
        self.all_plans: List[Tuple[str, Patient, Plan]] = []  # Store all plans in order for index-based selection
        self.on_plan_selected = on_plan_selected
        self.configure(width=320, style="CustomSidebar.TFrame", padding=(0, 0, 0, 0))
        self.pack_propagate(False)
        self._create_widgets()
        self._tk_image_refs = []  # Store image references to avoid garbage collection

    def _create_widgets(self) -> None:
        """Create and layout the main UI widgets for the panel.
        
        Creates the header label and patient list container frame.
        """
        # Header (gray, modern font)
        ttk.Label(self, text="Patients & Plans", style="CustomHeader.TLabel").pack(
            side="top", anchor="w", padx=16, pady=(10, 8)
        )
        # Patient list (flat, no border)
        self.patient_list = ttk.Frame(self, style="CustomSidebar.TFrame")
        self.patient_list.pack(side="top", fill="both", expand=True, padx=8, pady=0)

    def set_pinnacle_reader(self, reader: PinnacleReader) -> None:
        """Set the PinnacleReader instance for this panel.
        
        Args:
            reader: The PinnacleReader instance to use for accessing medical data.
        """
        self.pinnacle_reader = reader

    def _generate_ct_thumbnail(
        self, institution: Institution, patient: Patient, image_set: ImageSet | int, size: Tuple[int, int] = (100, 100)
    ) -> Optional[Image.Image]:
        """
        Generate a thumbnail from the middle slice of a planning CT image set.

        Args:
            institution (Institution): The institution object
            patient (Patient): The patient object
            image_set (ImageSet | int): The planning CT image set ID
            size (tuple): Thumbnail size (width, height)

        Returns:
            PIL Image object or None if generation fails
        """
        if isinstance(image_set, ImageSet):
            image_set_id = image_set.id
        else:
            image_set_id = image_set

        try:
            if not self.pinnacle_reader:
                logger.warning("No pinnacle_reader available for thumbnail generation")
                return None

            logger.info(
                f"Generating thumbnail for ImageSet_{image_set_id} using pinnacle_reader"
            )

            # Use pinnacle_reader to get the image set
            image_header = self.pinnacle_reader.get_image_header(
                institution,
                patient,
                image_set
            )

            # image_set = self.pinnacle_reader.get_image_set(patient_path, planning_ct_id)
            if not image_header:
                logger.warning(
                    f"Failed to load image set {image_set_id} via pinnacle_reader"
                )
                return None

            # Get the middle slice index
            num_slices = image_header.z_dim or 0
            middle_slice_idx = num_slices // 2

            logger.info(
                f"Generating thumbnail for ImageSet_{image_set_id}, slice {middle_slice_idx}/{num_slices}"
            )
            slice_data = self.pinnacle_reader.get_image_slice(
                institution,
                patient,
                image_set,
                middle_slice_idx,
                image_header
            )

            # Apply window/level for display
            # Use default CT window/level values
            window_center = 40  # Typical soft tissue window center
            window_width = 400  # Typical soft tissue window width

            # Pinnacle CT numbers start at 0 for air instead of -1000
            min_val = 1000 + window_center - (window_width / 2)
            max_val = 1000 + window_center + (window_width / 2)

            # Clip and normalize to 0-255 range
            slice_clipped = np.clip(slice_data, min_val, max_val)
            slice_normalized = (
                (slice_clipped - min_val) / (max_val - min_val) * 255
            ).astype(np.uint8)

            # Create PIL image
            pil_image = Image.fromarray(slice_normalized, mode="L")

            # Create thumbnail
            pil_image.thumbnail(size, Image.Resampling.LANCZOS)

            logger.info(
                f"Successfully generated thumbnail for ImageSet_{image_set_id}"
            )
            return pil_image

        except Exception as e:
            logger.error(
                f"Error generating thumbnail for ImageSet_{image_set_id}: {e}"
            )
            return None

    def add_patient(self, institution: Institution, patient: Patient) -> None:
        """Add a patient to the panel with their associated plans.
        
        Creates UI elements for the patient and organizes their treatment plans
        by planning CT image set. Generates CT thumbnails and plan buttons.
        
        Args:
            institution: The Institution object for the patient.
            patient: The Patient object to add to the panel.
        """
        if not hasattr(patient, "patient_id") or not patient.patient_id:
            logger.warning(f"Skipping patient {patient} - no valid patient ID")
            return
        
        logger.info(
            f"Adding patient to panel: {patient.last_and_first_name} (MRN: {patient.medical_record_number})"
        )

        patient_frame = ttk.Frame(self.patient_list, style="CustomSidebar.TFrame")
        patient_frame.pack(side="top", fill="x", padx=0, pady=8)
        ttk.Label(
            patient_frame,
            text=f"{patient.last_and_first_name} ({patient.medical_record_number})",
            style="CustomAccent.TLabel",
        ).pack(side="top", anchor="w", padx=0, pady=(0, 2))
        self.patients.append(patient)  # Store patient object in order
        self.patient_frames[patient.patient_id] = patient_frame

        plans = patient.plan_list
        if not plans:
            logger.info(f"No plans found for patient {patient.medical_record_number}")
            return

        logger.info(
            f"Processing {len(plans)} plans for patient {patient.medical_record_number}"
        )

        plans_by_planning_ct: dict[int, list[Plan]] = {}
        for plan in plans:
            if plan.primary_ct_image_set_id is None:
                continue

            if plan.primary_ct_image_set_id not in plans_by_planning_ct:
                plans_by_planning_ct[plan.primary_ct_image_set_id] = []
            plans_by_planning_ct[plan.primary_ct_image_set_id].append(plan)

        logger.info(f"Plans grouped by CT: {len(plans_by_planning_ct)} CT groups")

        for planning_ct_id in plans_by_planning_ct:
            patient_path = patient.patient_path if patient.patient_path else None
            if isinstance(patient_path, str) and patient_path:
                logger.info(
                    f"Adding planning CT {planning_ct_id} with {len(plans_by_planning_ct[planning_ct_id])} plans"
                )
                self.add_planning_ct(
                    institution,
                    patient_frame,
                    patient,
                    planning_ct_id,
                    plans_by_planning_ct[planning_ct_id],
                )
            else:
                logger.warning(
                    f"Skipping planning CT {planning_ct_id} - no valid patient path"
                )

        logger.info(f"Finished adding patient {patient.medical_record_number} to panel")

    def add_planning_ct(
        self,
        institution: Institution,
        patient_frame: ttk.Frame,
        patient: Patient,
        planning_ct_id: int,
        plans: List[Plan]
    ) -> None:
        """Add a planning CT thumbnail and associated plans to the patient frame.

        Args:
            patient_frame: The parent frame for the patient.
            patient: Patient object for additional context.
            planning_ct_id: The planning CT image set ID.
            plans: List of Plan objects for this CT.
        """
        thumb_frame = ttk.Frame(
            patient_frame, style="CustomSidebarThumb.TFrame", width=100, height=100
        )
        thumb_frame.pack(side="left", padx=0, pady=0)
        thumb_frame.pack_propagate(False)

        # Generate thumbnail from middle slice of planning CT
        image_set = plans[0].primary_ct_image_set or plans[0].primary_ct_image_set_id
        thumb_image = self._generate_ct_thumbnail(
            institution,
            patient,
            image_set,
            size=(100, 100)
        )

        if thumb_image is not None:
            tk_image = ImageTk.PhotoImage(thumb_image)
            label = ttk.Label(thumb_frame, image=tk_image)
            # Store reference to avoid garbage collection (attach to self)
            self._tk_image_refs.append(tk_image)
            label.pack(expand=True)
        else:
            # Fallback to text label if thumbnail generation fails
            ttk.Label(
                thumb_frame,
                text=f"ImageSet_{planning_ct_id}",
                style="CustomSmall.TLabel",
            ).pack(expand=True)

        plans_frame = ttk.Frame(patient_frame, style="CustomSidebar.TFrame")
        plans_frame.pack(side="left", fill="x", expand=True, padx=8)
        ttk.Label(plans_frame, text="Plans:", style="CustomSmall.TLabel").pack(
            side="top", anchor="w"
        )
        if plans:
            for plan in plans:
                if plan.name:
                    self.add_plan(plans_frame, institution, patient, plan)

    def add_plan(self, parent: Widget, institution: Institution, patient: Patient, plan: Plan) -> ttk.Frame:
        """Add a treatment plan button to the specified parent widget.
        
        Creates a clickable button for the plan that triggers the selection callback
        when clicked. Stores the plan information for later retrieval.
        
        Args:
            parent: The parent widget to contain the plan button.
            institution: The Institution object for the patient.
            patient: The Patient object that owns this plan.
            plan: The Plan object containing plan data.
            
        Returns:
            The created plan frame widget.
        """
        if not hasattr(plan, "name") or not plan.name:
            logger.warning(f"Skipping plan {plan} - no valid plan name")
            plan_name = f"Plan_{plan.id}"
        else:
            plan_name = plan.name
        logger.info(f"Adding plan to panel: {plan_name}")

        plan_frame = ttk.Frame(parent, style="CustomSidebar.TFrame")
        plan_frame.pack(side="top", fill="x", pady=1)
        button = ttk.Button(
            plan_frame,
            text=f"\u25b6 {plan_name}",
            style="link",
            command=lambda: (
                self.on_plan_selected(institution, patient, plan) if self.on_plan_selected else None
            ),
        )
        button.pack(side="left")
        button.bind("<Enter>", lambda e: button.config(cursor="hand2"))
        button.bind("<Leave>", lambda e: button.config(cursor=""))

        # Store plan information for selection
        self.plan_buttons[plan_name] = (button, patient, plan)
        self.all_plans.append((plan_name, patient, plan))

        logger.info(
            f"Plan '{plan_name}' added to panel. Total plans: {len(self.all_plans)}"
        )

        return plan_frame

    def clear_patients(self) -> None:
        """Clear all patients and plans from the panel.
        
        Removes all UI elements and clears all internal data structures.
        This effectively resets the panel to its initial empty state.
        """
        logger.info("Clearing all patients and plans from panel")
        logger.info(
            f"Before clear: {len(self.patients)} patients, {len(self.patient_frames)} patient frames, {len(self.plan_buttons)} plan buttons, {len(self.all_plans)} total plans"
        )

        for widget in self.patient_list.winfo_children():
            widget.destroy()
        self.patients.clear()
        self.patient_frames.clear()
        self.plan_buttons.clear()
        self.all_plans.clear()

        logger.info("Panel cleared successfully")

    def select_plan_by_name(self, institution: Institution, plan_name: str) -> bool:
        """Select a plan by its name.

        Searches for a plan with the specified name and triggers the selection
        callback if found. This method provides programmatic plan selection.

        Args:
            plan_name: The name of the plan to select.

        Returns:
            True if plan was found and selected, False otherwise.
        """
        logger.info(f"Attempting to select plan by name: '{plan_name}'")
        logger.info(f"Available plan buttons: {list(self.plan_buttons.keys())}")

        if plan_name in self.plan_buttons:
            _, patient, plan = self.plan_buttons[plan_name]
            logger.info(f"Found plan button for '{plan_name}', triggering selection")

            if self.on_plan_selected:
                self.on_plan_selected(institution, patient, plan)
            logger.info(f"Successfully selected plan by name: '{plan_name}'")
            return True
        else:
            logger.warning(f"Plan '{plan_name}' not found in available buttons")
            return False

    def select_plan_by_index(self, institution: Institution, plan_index: int) -> bool:
        """Select a plan by its index (0-based).

        Selects a plan from the ordered list of all plans. The index corresponds
        to the order in which plans were added to the panel.

        Args:
            plan_index: The index of the plan to select (0-based).

        Returns:
            True if plan was found and selected, False otherwise.
        """
        logger.info(f"Attempting to select plan by index: {plan_index}")
        logger.info(f"Total plans available: {len(self.all_plans)}")

        if 0 <= plan_index < len(self.all_plans):
            plan_name, _, _ = self.all_plans[plan_index]
            logger.info(f"Found plan at index {plan_index}: '{plan_name}'")
            return self.select_plan_by_name(institution, plan_name)
        else:
            logger.warning(
                f"Plan index {plan_index} out of range. Available indices: 0-{len(self.all_plans)-1}"
            )
            return False

    def select_patient_by_name(self, patient_name: str) -> bool:
        """Select a patient by name (last_and_first_name).

        Searches for a patient with the specified name. This method provides
        patient-level selection for UI highlighting or navigation.

        Args:
            patient_name: The name of the patient to select (last_and_first_name format).

        Returns:
            True if patient was found, False otherwise.
        """
        logger.info(f"Attempting to select patient by name: '{patient_name}'")

        for patient in self.patients:
            if patient.last_and_first_name == patient_name:
                logger.info(
                    f"Found patient by name: '{patient_name}' (ID: {patient.patient_id})"
                )
                return True

        available_names = [p.last_and_first_name for p in self.patients]
        logger.warning(
            f"Patient '{patient_name}' not found. Available patients: {available_names}"
        )
        return False

    def select_patient_by_index(self, patient_index: int) -> bool:
        """Select a patient by index (0-based).

        Selects a patient from the ordered list of all patients. The index
        corresponds to the order in which patients were added to the panel.

        Args:
            patient_index: The index of the patient to select (0-based).

        Returns:
            True if patient was found, False otherwise.
        """
        logger.info(f"Attempting to select patient by index: {patient_index}")
        logger.info(f"Total patients available: {len(self.patients)}")

        if 0 <= patient_index < len(self.patients):
            patient = self.patients[patient_index]
            logger.info(
                f"Found patient at index {patient_index}: '{patient.last_and_first_name}'"
            )
            return True
        else:
            logger.warning(
                f"Patient index {patient_index} out of range. Available indices: 0-{len(self.patients)-1}"
            )
            return False
