import ttkbootstrap as ttk  # type: ignore

# Centralized color and font constants
PRIMARY_COLOR = "#1d9ce5"
HEADER_FONT = ("Segoe UI", 11, "bold")
TITLE_FONT = ("Segoe UI", 15, "bold")
LABEL_FONT = ("Segoe UI", 10)
LABEL_BOLD_FONT = ("Segoe UI", 10, "bold")
SMALL_FONT = ("Segoe UI", 9)
SMALL_BOLD_FONT = ("Segoe UI", 9, "bold")
GRAY_TEXT = "#495057"
LIGHT_GRAY = "#686d72"
ACCENT_BLUE = "#007bff"
WHITE = "white"


def setup_theme(style: ttk.Style):
    # Frames
    style.configure("CustomMain.TFrame", background=WHITE)
    style.configure("CustomSidebar.TFrame", background=WHITE)
    style.configure(
        "CustomSidebarThumb.TFrame",
        background=WHITE,
        borderwidth=1,
        relief="solid",
        bordercolor=LIGHT_GRAY,
    )
    style.configure("CustomHeader.TFrame", background=WHITE)
    style.configure("CustomPrimary.TFrame", background=PRIMARY_COLOR)
    style.configure("CustomROI.TFrame", background=WHITE)
    style.configure("CustomTrial.TFrame", background=WHITE)
    style.configure("CTViewer.TFrame", background="black")
    style.configure(
        "CustomProgress.TFrame",
        background=WHITE,
        borderwidth=1,
        relief="solid",
        bordercolor=PRIMARY_COLOR,
    )

    # Labels
    style.configure(
        "CustomHeader.TLabel", font=HEADER_FONT, foreground=GRAY_TEXT, background=WHITE
    )
    style.configure(
        "CustomTitle.TLabel",
        font=TITLE_FONT,
        foreground=WHITE,
        background=PRIMARY_COLOR,
    )
    style.configure(
        "CustomAccent.TLabel",
        font=LABEL_BOLD_FONT,
        foreground=ACCENT_BLUE,
        background=WHITE,
    )
    style.configure(
        "CustomLabel.TLabel", font=LABEL_FONT, foreground=GRAY_TEXT, background=WHITE
    )
    style.configure(
        "CustomLabelBold.TLabel",
        font=LABEL_BOLD_FONT,
        foreground=GRAY_TEXT,
        background=WHITE,
    )
    style.configure(
        "CustomSmall.TLabel", font=SMALL_FONT, foreground=LIGHT_GRAY, background=WHITE
    )
    style.configure(
        "CustomSmallBold.TLabel",
        font=SMALL_BOLD_FONT,
        foreground=GRAY_TEXT,
        background=WHITE,
    )

    # Buttons
    style.configure(
        "CustomPrimary.TButton",
        font=LABEL_FONT,
        padding=(15, 8),
        foreground=WHITE,
        background=PRIMARY_COLOR,
    )
    style.configure(
        "CustomSecondary.TButton",
        font=LABEL_FONT,
        padding=(15, 8),
        foreground=GRAY_TEXT,
        background=LIGHT_GRAY,
    )
    style.configure(
        "CustomLight.TButton",
        font=LABEL_FONT,
        foreground=PRIMARY_COLOR,
        background=WHITE,
    )

    # Progressbar
    style.configure(
        "CustomPrimary.Horizontal.TProgressbar",
        troughcolor=WHITE,
        bordercolor=PRIMARY_COLOR,
        background=PRIMARY_COLOR,
        lightcolor=PRIMARY_COLOR,
        darkcolor=PRIMARY_COLOR,
    )

    # Additional customizations as needed for your widgets
