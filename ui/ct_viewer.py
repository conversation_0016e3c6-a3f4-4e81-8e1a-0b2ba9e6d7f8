import tkinter as tk
from ttkbootstrap import Frame
from PIL import Image, ImageTk
import numpy as np

class CTViewer(Frame):
    """
    A ttkbootstrap Frame for visualizing and interacting with a stack of DICOM CT slices.
    Accepts a list of pydicom Dataset objects (one per slice).
    Enables scrolling, zooming, panning, and window/level adjustment.
    """
    def __init__(self, master, dicom_slices, *args, **kwargs):
        super().__init__(master, *args, **kwargs)
        self.configure(style="CTViewer.TFrame")
        self.dicom_slices = sorted(dicom_slices, key=lambda ds: ds.SliceLocation)
        self.num_slices = len(dicom_slices)
        self.slice_index = self.num_slices // 2
        self.zoom = 1.0
        self.pan_x = 0
        self.pan_y = 0
        self._drag_start = None
        self._wlv_start = None
        self._window, self._level = self._get_initial_window_level()
        self._cache = {}
        self._tk_img = None  # Maintain reference to PhotoImage
        self._prepare_images()

        self.canvas = tk.Canvas(self, bg='black', highlightthickness=0)
        self.canvas.pack(fill=tk.BOTH, expand=True)
        self._show_slice()
        self._bind_events()

    def _prepare_images(self):
        """Convert all slices to displayable PIL Images and cache them."""
        self._cache.clear()
        for idx, ds in enumerate(self.dicom_slices):
            arr = ds.pixel_array.astype(np.int16)
            intercept = getattr(ds, 'RescaleIntercept', 0)
            slope = getattr(ds, 'RescaleSlope', 1)
            arr = arr * slope + intercept
            img = self._window_level_image(arr, self._window, self._level)
            pil_img = Image.fromarray(img).convert('L')
            self._cache[idx] = pil_img

    def _get_initial_window_level(self):
        ds = self.dicom_slices[0]
        window = getattr(ds, 'WindowWidth', 400)
        level = getattr(ds, 'WindowCenter', 40)
        # WindowWidth/WindowCenter may be stored as sequence
        if isinstance(window, (list, tuple)):
            window = window[0]
        if isinstance(level, (list, tuple)):
            level = level[0]
        return float(window), float(level)

    def _window_level_image(self, arr, window, level):
        minval = level - window/2
        maxval = level + window/2
        wl_img = np.clip((arr - minval) * 255.0 / (maxval - minval), 0, 255)
        return wl_img.astype(np.uint8)

    def _show_slice(self):
        pil_img = self._cache[self.slice_index]
        # Apply zoom
        w, h = pil_img.size
        zoomed = pil_img.resize((int(w*self.zoom), int(h*self.zoom)), Image.NEAREST)
        # Create Tk image
        self._tk_img = ImageTk.PhotoImage(zoomed, master=self.canvas)
        self.canvas.delete('all')
        # Center with pan
        canvas_w = self.canvas.winfo_width()
        canvas_h = self.canvas.winfo_height()
        
        # Create black background rectangle to ensure black background
        if canvas_w > 1 and canvas_h > 1:
            self.canvas.create_rectangle(0, 0, canvas_w, canvas_h, fill="black", outline="")
        
        x = canvas_w//2 - zoomed.width//2 + int(self.pan_x)
        y = canvas_h//2 - zoomed.height//2 + int(self.pan_y)
        self.canvas.create_image(x, y, anchor='nw', image=self._tk_img)

    def _bind_events(self):
        self.canvas.bind('<Configure>', lambda e: self._show_slice())
        self.canvas.bind('<Enter>', self._on_enter)
        self.canvas.bind('<Leave>', self._on_leave)

    def _on_enter(self, event):
        self.canvas.bind('<MouseWheel>', self._on_mousewheel)
        self.canvas.bind('<ButtonPress-2>', self._on_middle_press)
        self.canvas.bind('<B2-Motion>', self._on_middle_drag)
        self.canvas.bind('<ButtonRelease-2>', self._on_middle_release)
        self.canvas.bind('<Control-MouseWheel>', self._on_ctrl_mousewheel)
        self.canvas.bind('<ButtonPress-3>', self._on_right_press)
        self.canvas.bind('<B3-Motion>', self._on_right_drag)
        self.canvas.bind('<ButtonRelease-3>', self._on_right_release)

    def _on_leave(self, event):
        self.canvas.unbind('<MouseWheel>')
        self.canvas.unbind('<ButtonPress-2>')
        self.canvas.unbind('<B2-Motion>')
        self.canvas.unbind('<ButtonRelease-2>')
        self.canvas.unbind('<Control-MouseWheel>')
        self.canvas.unbind('<ButtonPress-3>')
        self.canvas.unbind('<B3-Motion>')
        self.canvas.unbind('<ButtonRelease-3>')

    def _on_mousewheel(self, event):
        # Scroll through slices
        if event.delta > 0:
            self.slice_index = min(self.slice_index + 1, self.num_slices - 1)
        else:
            self.slice_index = max(self.slice_index - 1, 0)
        self._show_slice()

    def _on_ctrl_mousewheel(self, event):
        # Zoom in/out with zoom-to-cursor
        if not self._cache:
            return
            
        # Get cursor position in canvas coordinates
        cursor_x = event.x
        cursor_y = event.y
        
        # Get canvas dimensions
        canvas_w = self.canvas.winfo_width()
        canvas_h = self.canvas.winfo_height()
        
        # Get current image size
        pil_img = self._cache[self.slice_index]
        img_w, img_h = pil_img.size
        
        # Calculate current zoomed image size
        zoomed_w = int(img_w * self.zoom)
        zoomed_h = int(img_h * self.zoom)
        
        # Calculate current image position (top-left corner)
        current_img_x = canvas_w//2 - zoomed_w//2 + int(self.pan_x)
        current_img_y = canvas_h//2 - zoomed_h//2 + int(self.pan_y)
        
        # Convert cursor position to image coordinates (0-1 normalized)
        if zoomed_w > 0 and zoomed_h > 0:
            image_rel_x = (cursor_x - current_img_x) / zoomed_w
            image_rel_y = (cursor_y - current_img_y) / zoomed_h
        else:
            image_rel_x = 0.5
            image_rel_y = 0.5
        
        # Store old zoom
        old_zoom = self.zoom
        
        # Apply zoom
        if event.delta > 0:
            self.zoom = min(self.zoom * 1.1, 10)
        else:
            self.zoom = max(self.zoom / 1.1, 0.1)
        
        # Adjust pan to keep cursor point fixed if zoom changed
        if self.zoom != old_zoom:
            # Calculate new zoomed image size
            new_zoomed_w = int(img_w * self.zoom)
            new_zoomed_h = int(img_h * self.zoom)
            
            # Calculate where the image point should be in the new zoom
            target_pixel_x = image_rel_x * new_zoomed_w
            target_pixel_y = image_rel_y * new_zoomed_h
            
            # Calculate new image position to keep cursor point fixed
            new_img_x = cursor_x - target_pixel_x
            new_img_y = cursor_y - target_pixel_y
            
            # Convert to pan values (offset from center)
            center_x = canvas_w//2 - new_zoomed_w//2
            center_y = canvas_h//2 - new_zoomed_h//2
            
            self.pan_x = new_img_x - center_x
            self.pan_y = new_img_y - center_y
        
        self._show_slice()

    def _on_middle_press(self, event):
        self._drag_start = (event.x, event.y, self.pan_x, self.pan_y)

    def _on_middle_drag(self, event):
        if self._drag_start:
            dx = event.x - self._drag_start[0]
            dy = event.y - self._drag_start[1]
            self.pan_x = self._drag_start[2] + dx
            self.pan_y = self._drag_start[3] + dy
            self._show_slice()

    def _on_middle_release(self, event):
        self._drag_start = None

    def _on_right_press(self, event):
        self._wlv_start = (event.x, event.y, self._window, self._level)

    def _on_right_drag(self, event):
        if self._wlv_start:
            dx = event.x - self._wlv_start[0]
            dy = event.y - self._wlv_start[1]
            # Arbitrary scaling factors for window/level adjustment
            new_window = max(1, self._wlv_start[2] + dx * 2)
            new_level = self._wlv_start[3] + dy * 2
            self._window = new_window
            self._level = new_level
            self._prepare_images()
            self._show_slice()

    def _on_right_release(self, event):
        self._wlv_start = None