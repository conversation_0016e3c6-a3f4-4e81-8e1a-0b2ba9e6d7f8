<context>
# Overview
The DICOM Converter application is a specialized GUI tool designed for medical professionals and technicians who need to process and convert patient radiotherapy data. It addresses the challenge of efficiently managing and exporting DICOM format medical imaging data, particularly for radiation therapy planning. The application provides an intuitive interface for loading patient data from various sources, visualizing CT images with overlays, and exporting selected trials to DICOM format.

# Core Features

1. Multi-Source Data Loading
- What it does: Provides three methods to load patient data (TAR, ZIP, directory)
- Why it's important: Offers flexibility in handling different data storage formats
- How it works: Uses file dialogs and dedicated loading handlers for each format

2. Patient Management & Visualization
- What it does: Displays hierarchical patient list with CT thumbnails and plan selection
- Why it's important: Enables efficient navigation and organization of patient data
- How it works: Left sidebar provides scrollable list with interactive plan selection

3. CT Image Display & Annotation
- What it does: Shows CT images with ROI/dose overlays and color-coded contours
- Why it's important: Allows verification of treatment plans and structures
- How it works: Main panel displays image with interactive overlays and side panels for ROI/POI details

4. Trial Selection & Export
- What it does: Enables selection and export of specific trials to DICOM format
- Why it's important: Facilitates transfer of treatment plans to other systems
- How it works: Checkbox-based selection with detailed trial information and batch export capability

5. Progress Tracking
- What it does: Shows operation progress with cancel functionality
- Why it's important: Provides feedback during long-running operations
- How it works: Modal overlay with progress bar and status updates

# User Experience

User Personas:
1. Medical Physicists
- Primary users who need to review and export treatment plans
- Require accurate visualization of CT data and structures
- Need efficient batch processing capabilities

2. Radiation Therapists
- Secondary users who review patient plans
- Focus on CT visualization and structure verification
- Need intuitive navigation between patients and plans

Key User Flows:
1. Data Loading
- Click load button (TAR/ZIP/Directory)
- Select data source via file dialog
- Wait for loading with progress feedback
- View loaded patients in sidebar

2. Plan Review
- Select patient from sidebar
- View CT thumbnail
- Click plan from available options
- Review CT image with overlays
- Examine ROI/POI details

3. Trial Export
- Select desired trials via checkboxes
- Review trial details
- Click export button
- Monitor progress
- Cancel if needed

UI/UX Considerations:
- Clean, modern interface using ttkbootstrap theme
- Hierarchical organization of patient data
- Color coding for ROIs and selection states
- Interactive elements with visual feedback
- Progress monitoring for long operations
</context>
<PRD>
# Technical Architecture

System Components:
1. Main Window (MainWindow)
- Core application container
- Manages overall state and data flow
- Coordinates between components

2. UI Components
- TitleBarComponent: Application header
- HeaderPanelComponent: Data loading controls
- PatientPanelComponent: Patient/plan navigation
- MainPanelComponent: CT visualization and trial management
- ProgressModal: Operation progress tracking

3. Data Models
- Patient data structures
- Plan and trial representations
- ROI/POI models

APIs and Integrations:
1. File System Integration
- TAR file handling
- ZIP file processing
- Directory traversal

2. DICOM Processing
- Image loading and display
- Structure set handling
- Plan data management

Infrastructure Requirements:
- Python runtime environment
- tkinter and ttkbootstrap for UI
- Supporting libraries for file handling
- DICOM processing capabilities

# Development Roadmap

Phase 1 - Core Infrastructure
1. Basic window setup
2. UI component framework
3. File system integration
4. Data model implementation

Phase 2 - Patient Management
1. Patient list implementation
2. CT thumbnail generation
3. Plan selection mechanism
4. Basic navigation

Phase 3 - Visualization
1. CT image display
2. ROI overlay system
3. POI visualization
4. Color coding implementation

Phase 4 - Trial Management
1. Trial selection interface
2. Trial detail display
3. Export functionality
4. Progress tracking

Phase 5 - Enhancement & Polish
1. Performance optimization
2. UI refinements
3. Additional file format support
4. Advanced visualization features

# Logical Dependency Chain

Foundation (Must Complete First):
1. Main window framework
2. File loading system
3. Basic data models

Initial Visual Milestone:
1. Patient list display
2. Basic CT viewing
3. Simple plan selection

Feature Building Blocks:
1. Enhanced visualization
   - ROI support
   - POI display
   - Color coding
2. Trial management
   - Selection interface
   - Export capability
3. Progress tracking
   - Modal implementation
   - Cancel functionality

# Risks and Mitigations

Technical Challenges:
1. Performance with large datasets
   - Mitigation: Implement efficient data loading
   - Use background processing
   - Optimize image handling

2. Cross-platform compatibility
   - Mitigation: Use tkinter for native widgets
   - Test on multiple platforms
   - Handle platform-specific paths

3. Memory management
   - Mitigation: Implement cleanup procedures
   - Use efficient data structures
   - Monitor memory usage

MVP Considerations:
1. Focus on essential features first:
   - Basic patient loading
   - Simple CT visualization
   - Core trial export

2. Ensure extensibility:
   - Modular design
   - Clear interfaces
   - Documentation

Resource Constraints:
1. Development efficiency
   - Use existing libraries when possible
   - Implement reusable components
   - Focus on maintainable code

# Appendix

Technical Specifications:
1. UI Framework
   - tkinter/ttkbootstrap
   - Custom styling
   - Event-driven architecture

2. File Formats
   - TAR archive support
   - ZIP file handling
   - Directory structure

3. Data Processing
   - Threading for background operations
   - Queue-based result handling
   - Progress tracking system
</PRD>