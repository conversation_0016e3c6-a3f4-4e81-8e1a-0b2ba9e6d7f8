# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

`pinnacle_io` is a Python library for reading and writing Pinnacle radiotherapy treatment planning files using structured SQLAlchemy models. The library provides a unified interface for accessing Pinnacle data from directories, tar files, zip files, and in-memory archives.

**Key Features:**
- **ID-Based Loading**: Modern API supporting intuitive ID-based data access (recommended)
- **Legacy Path Support**: Backward compatibility with path-based methods  
- **Unified Interface**: Single API for all file formats (directory, tar, zip)
- **Type Safety**: Full type hints and SQLAlchemy models

## Development Commands

### Setup and Installation
```bash
# Install in development mode
pip install -e .

# Install development dependencies
pip install -r requirements-dev.txt
```

### Testing
```bash
# Run all tests
pytest

# Run tests with coverage
pytest --cov=pinnacle_io --cov-report=html

# Run specific test file
pytest tests/test_api.py

# Run specific test class or method
pytest tests/models/test_dose.py::TestDose::test_init_with_spatial_objects
```

### Code Quality
```bash
# Format code with black
black pinnacle_io/ tests/

# Sort imports with isort
isort pinnacle_io/ tests/

# Type checking with mypy
mypy pinnacle_io/

# Run pyright type checker (configured in pyrightconfig.json)
pyright
```

## Architecture Overview

### Core Components

1. **Unified API (`pinnacle_io.api`)**
   - `read(path, target_file=None)` - Main entry point for reading data
   - `write(path, obj)` - Main entry point for writing data (directory-only)
   - Auto-detects source type (directory, tar, zip) and returns appropriate data objects

2. **Reader Services (`pinnacle_io.services`)**
   - `BaseReaderService` - Abstract base class defining unified file access interface
   - `FileReader` - Directory-based file access
   - `TarFileReader` - Tar archive file access (supports both file paths and in-memory)
   - `ZipFileReader` - Zip archive file access (supports both file paths and in-memory)

3. **Data Models (`pinnacle_io.models`)**
   - SQLAlchemy-based models inheriting from `PinnacleBase` or `VersionedBase`
   - Key models: Institution, Patient, Plan, ImageSet, Trial, ROI, Dose, Machine, etc.
   - Models support creation from dictionaries with automatic type conversion

4. **Reader Classes (`pinnacle_io.readers`)**
   - Specialized readers for each file type (InstitutionReader, PatientReader, etc.)
   - Accept reader service for flexible file access
   - Support both ID-based (`read_from_ids()`) and path-based (`read_from_path()`) methods
   - Provide `parse()` methods for consistency

5. **Writer Classes (`pinnacle_io.writers`)**
   - Convert model objects back to Pinnacle file formats
   - Support directory-based writing

### File Type Mappings

| File Pattern | Data Object | Reader Service Method |
|--------------|-------------|----------------------|
| Institution | Institution | get_institution() |
| Patient | Patient | get_patient() |
| ImageSet_#.header | ImageSet | get_image_set() |
| ImageSet_#.ImageInfo | ImageInfo | get_image_info() |
| plan.Trial | List[Trial] | get_trials() |
| plan.roi | List[ROI] | get_rois() |
| plan.Points | List[Point] | get_points() |
| plan.PatientSetup | PatientSetup | get_patient_setup() |
| plan.Pinnacle.Machines | List[Machine] | get_machines() |
| plan.Trial.binary.### | Dose | get_dose() |

### Key Design Patterns

- **ID-Based Loading**: Modern API uses semantic IDs (institution=1, patient=1, plan=1) instead of file paths
- **Service Injection**: Reader classes accept reader services for flexible file access
- **Abstract Base Classes**: BaseReaderService defines uniform interface across IO types
- **Factory Pattern**: API automatically detects and creates appropriate reader service
- **SQLAlchemy Models**: Structured data representation with automatic type conversion
- **Backward Compatibility**: Path-based methods still supported alongside modern ID-based access

## Testing Structure

- **Unit Tests**: Located in `tests/` with modular structure matching source code
- **Integration Tests**: `tests/test_integration.py` for end-to-end workflows
- **API Tests**: `tests/test_api.py` for top-level API functions
- **Test Data**: Real Pinnacle files in `tests/test_data/` (both raw and archived formats)
- **Fixtures**: Database setup and shared test utilities in `tests/conftest.py`

## Configuration Files

- **pyproject.toml**: Main project configuration with dependencies, black, isort, and mypy settings
- **pyrightconfig.json**: Type checking configuration for pyright/pylance
- **setup.py**: Minimal setuptools configuration (delegates to pyproject.toml)

## Development Notes

- **Recent Refactors**: 
  - Unified IO interface completed (see `docs/unified_io_refactor_plan.md`)
  - ID-based loading implemented across all readers (see `docs/id-based-loading-refactor.md`)
- All reader classes support both ID-based and path-based access patterns
- Service injection pattern implemented for flexible file access
- Comprehensive test coverage with 80+ tests across all components  
- Type checking enforced with strict mypy/pyright configuration
- Code formatting standardized with black (88 char line length)

## Usage Examples

### Modern ID-Based Approach (Recommended)
```python
from pinnacle_io import PinnacleReader

reader = PinnacleReader("/path/to/pinnacle/data")

# Read data using intuitive IDs
patient = reader.get_patient(institution=1, patient=1)
image_set = reader.get_image_set(institution=1, patient=1, image_set=0)
trials = reader.get_trials(institution=1, patient=1, plan=1)
rois = reader.get_rois(institution=1, patient=1, plan=1)

# Dose requires trial object
dose = reader.get_dose(institution=1, patient=1, plan=1, trial=trials[0])
```

### Legacy Path-Based Approach (Still Supported)
```python
# Backward compatibility maintained
patient = reader.get_patient("Institution_1/Mount_0/Patient_1")
trials = reader.get_trials("Institution_1/Mount_0/Patient_1/Plan_1")
```