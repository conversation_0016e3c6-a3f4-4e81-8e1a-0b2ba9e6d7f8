# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

SimpleDicomConverter is a Python application that converts Pinnacle radiotherapy treatment planning files to standardized DICOM format. The application supports converting four DICOM types: CT Images, RT Structure Sets, RT Plans, and RT Dose files.

## Architecture

### Core Components

- **`dicom_converter/`** - Main conversion engine
  - `main.py` - Main application entry point and DicomConverterApp class
  - `converters/` - Modular DICOM converters (ImageConverter, StructureConverter, PlanConverter, DoseConverter)
  - `utils/` - Utilities including UID management, configuration, and constants
- **`ui/`** - GUI interface built with ttkbootstrap
  - `main_view.py` - Main GUI window and UI logic
  - `components/` - UI components (panels, viewers, modals)
- **`pinnacle_io`** - External library for reading Pinnacle files (not in this repo)

### Key Architectural Patterns

The application uses **PinnacleReader** from the pinnacle_io library to provide uniform access to Pinnacle files regardless of whether they're in TAR archives, ZIP files, or directories.

The **DicomConverterApp** class is being refactored to support:
- Class methods for loading from different source types (`convert_tar`, `convert_zip`, `convert_directory`)
- **DicomUidManager** for centralized UID management across all DICOM files
- Model-based conversion using pinnacle_io library objects

### Data Flow

1. **Load** - PinnacleReader loads Institution → Patient → Plan → Trial data
2. **Convert** - Individual converters create DICOM files with shared UIDs
3. **Export** - DICOM files saved with proper cross-references

## Common Commands

### Development
```bash
# Run GUI application
python run.py

# Run with command line arguments
python run.py --tar data.tar --plan 0
python run.py --zip data.zip --plan "BRAIN"
python run.py --dir /path/to/data --plan 1

# Legacy CLI usage
python -m dicom_converter.main [patient_folder] [input_folder] [output_folder]
```

### Testing
```bash
# Run all tests
pytest tests/

# Run specific test file
pytest tests/converters/test_plan_converter.py

# Run tests with verbose output
pytest -v tests/
```

### Code Quality
```bash
# Format code
black .

# Lint code
pylint dicom_converter/ ui/

# Type checking
mypy dicom_converter/ ui/
```

## Key Dependencies

- **pydicom** - DICOM file handling
- **pinnacle_io** - External library for reading Pinnacle files
- **ttkbootstrap** - Modern tkinter GUI framework
- **numpy** - Numerical operations
- **matplotlib** - CT visualization

## Important Notes

- The codebase is undergoing active refactoring from a monolithic script to a modular architecture
- See `docs/dicom_converter_refactor_plan.md` for detailed refactoring roadmap
- The DicomUidManager class ensures proper UID coordination across all DICOM files
- PinnacleReader abstracts input sources (TAR/ZIP/directory) for uniform data access
- All converters inherit from DicomConverter base class for consistent behavior

## Testing Data

Test data is located in `tests/test_data/` and includes:
- `archive_01.tar` - Sample Pinnacle data archive
- `archive_01/` - Extracted directory structure with Institution/Patient/Plan hierarchy# CLAUDE.md

This file provides guidance to Claude Code (claude.ai/code) when working with code in this repository.

## Project Overview

SimpleDicomConverter is a Python application that converts Pinnacle radiotherapy planning system files into standardized DICOM format. The application transforms Pinnacle text and binary files into four DICOM types: CT Images, RT Structure Sets, RT Plans, and RT Dose files.

## Development Commands

### Run the Application

**GUI Mode (Main Interface):**
```bash
python run.py
```

**GUI Mode with Archive Loading:**
```bash
python run.py path/to/archive.tar
python run.py path/to/archive.zip
python run.py path/to/directory
```

**Command Line Mode:**
```bash
python -m dicom_converter.main patient_folder input_folder output_folder
```

### Testing

**Run all tests:**
```bash
pytest tests/
```

**Run specific test module:**
```bash
pytest tests/converters/test_image_converter.py
```

**Run single test:**
```bash
pytest tests/converters/test_image_converter.py::TestImageConverter::test_convert
```

### Development Setup

**Install dependencies:**
```bash
pip install -r requirements.txt
```

**Activate virtual environment:**
```bash
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows
```

## Architecture Overview

### Core Package Structure

The application follows a modular architecture with clear separation of concerns:

- **`dicom_converter/`**: Core conversion engine
  - **`converters/`**: DICOM conversion modules (Image, Structure, Plan, Dose)
  - **`io/`**: File reading/writing operations with Pinnacle-specific readers
  - **`models/`**: Data models representing medical entities
  - **`utils/`**: Utility functions and configuration management

- **`ui/`**: User interface components built with ttkbootstrap
  - **`components/`**: Reusable UI components
  - **`main_view.py`**: Main application window
  - **`ct_viewer.py`**: CT image visualization with ROI overlays

- **`tests/`**: Comprehensive test suite with test data

### Key Design Patterns

**Converter Pattern**: Base `DicomConverter` class with specialized converters for each DICOM modality:
- `ImageConverter`: Handles CT image conversion
- `StructureConverter`: Processes ROI/structure data
- `PlanConverter`: Converts treatment plans
- `DoseConverter`: Handles dose distribution data

**Reader Pattern**: Hierarchical file readers for Pinnacle formats:
- `PinnacleFileReader`: Base class for hierarchical data parsing
- Specific readers for each file type (Patient, Plan, Trial, ROI, etc.)

**Model Pattern**: Domain models representing medical entities:
- `Patient`: Patient demographics and medical record
- `Plan`: Treatment plan with beams and parameters
- `ImageSet`: CT image metadata and pixel data
- `Trial`: Detailed treatment configuration

### Data Flow Architecture

1. **File Reading**: Pinnacle files → Readers → Domain Models
2. **Data Processing**: Models → Converters → DICOM Datasets
3. **File Writing**: DICOM Datasets → Output Files

### Configuration Management

The application uses YAML configuration (`dicom_converter/config.yaml`) for:
- DICOM UIDs and implementation details
- Equipment information and software versions
- ROI color definitions and MLC configurations
- CT image parameters and dose settings

## Key Components

### Base Converter Classes

**`DicomConverter`**: Abstract base class providing common DICOM functionality
- File meta dataset creation
- Common DICOM elements (patient, study, series)
- Dataset saving with automatic file naming

**`PlanBaseConverter`**: Base for converters requiring plan data
- Plan loading and retrieval
- Image set loading for planning CT

**`TrialBaseConverter`**: Base for converters requiring trial data
- Trial loading from plan files
- Beam configuration processing

### File I/O Architecture

**Reader Hierarchy**:
- `FileReader`: Base with caching support
- `ModelReader`: Interface for model conversion
- `PinnacleFileReader`: Hierarchical Pinnacle data parsing

**Specific Readers**:
- `PatientFileReader`: Patient demographics
- `ImageHeaderFileReader`: CT metadata
- `TrialFileReader`: Treatment plan configuration
- `ROIFileReader`: Structure/contour data
- `BinaryDoseFileReader`: Dose volume data

### UI Architecture

**Main Components**:
- `MainView`: Primary application window with patient loading
- `CTViewer`: CT visualization with ROI/dose overlays
- `PatientPlansPanel`: Patient and plan selection interface
- `ProgressModal`: Background operation progress tracking

## Important Implementation Notes

### Patient Position Handling

The application supports four patient positions with specific coordinate transformations:
- HFS (Head First Supine)
- HFP (Head First Prone)  
- FFS (Feet First Supine)
- FFP (Feet First Prone)

Each position requires specific coordinate system transformations when converting from Pinnacle to DICOM coordinate systems.

### File Dependencies

The converter requires these Pinnacle files in specific locations:
- `Patient` file in patient folder
- `plan.Points`, `plan.roi`, `plan.Trial` in plan folders
- `plan.Trial.binary.xxx` files for dose data
- `ImageSet_X.header` and `ImageSet_X.ImageInfo` for CT data

### DICOM Output Structure

Generated DICOM files follow naming conventions:
- CT images: `CT.[SOPInstanceUID].dcm`
- Structure sets: `RS.[SOPInstanceUID].dcm`
- Treatment plans: `RP.[SOPInstanceUID].dcm`
- Dose files: `RD.[SOPInstanceUID].dcm`

### Error Handling Patterns

The application uses Python logging throughout with consistent error handling:
- File reading errors are logged with context
- Conversion errors include patient/plan information
- UI errors are handled with user-friendly messages

## Testing Architecture

**Test Structure**:
- Unit tests for each converter module
- Integration tests for file I/O operations
- Model tests for data validation
- UI tests for component behavior

**Test Data**:
- Sample Pinnacle archives in `tests/test_data/`
- Both TAR and ZIP format test archives
- Complete patient datasets with all required files

## Legacy Components

**`createdcm.py`**: Original monolithic converter script
- Contains comprehensive conversion logic
- Uses global variables (80+) for state management
- Serves as reference implementation for new modular design

**`testbench.py`**: Development testing script
- DICOM file comparison utilities
- CT viewer testing with dual datasets
- Used for validating conversion accuracy

This legacy code should be referenced for understanding conversion algorithms but not modified directly. The modular architecture in `dicom_converter/` is the active codebase.