"""
Dose model for Pinnacle IO.

This module provides the Dose data model for representing dose distribution data.
"""

from typing import <PERSON>, <PERSON><PERSON>, TYPE_CHECKING
import numpy as np
import numpy.typing as npt
from sqlalchemy import Column, Integer, String, Float, ForeignKey
from sqlalchemy.orm import Mapped, relationship

from pinnacle_io.models.pinnacle_base import PinnacleBase

if TYPE_CHECKING:
    from pinnacle_io.models.trial import DoseGrid, Trial
    from pinnacle_io.models.beam import Beam


class Dose(PinnacleBase):
    """
    Model representing a dose distribution in the Pinnacle treatment planning system.

    This class encapsulates all dose-related information including dose grid properties,
    dose calculation parameters, and relationships to other treatment planning entities.
    It supports various dose types (PHYSICAL, EFFECTIVE) and units (GY, CGY).

    The dose can be associated with different levels of the treatment hierarchy:
    - Beam-specific dose (for individual beam contributions)
    - Trial dose (for composite dose across all beams in a trial)
    - Plan dose (for the final treatment plan dose)

    Key Features:
    - Supports both grid-based and point-based dose representation
    - Handles dose grid transformations and coordinate systems
    - Manages relationships with DoseGrid, Beam, and Trial entities
    - Provides methods for dose grid manipulation and analysis

    Relationships:
        - dose_grid (DoseGrid): The parent DoseGrid containing spatial information
        - beam (Beam): Associated Beam for beam-specific doses (optional)
        - trial (Trial): Associated Trial for trial-level doses (optional)
        - max_dose_point (MaxDosePoint): Point of maximum dose (one-to-one)
    """

    __tablename__: str = "Dose"

    # Basic information
    dose_id: Mapped[str | None] = Column("DoseID", String, nullable=True)
    dose_type: Mapped[str | None] = Column("DoseType", String, nullable=True, default="PHYSICAL")  # PHYSICAL, EFFECTIVE, etc.
    dose_unit: Mapped[str | None] = Column("DoseUnit", String, nullable=True, default="GY")  # GY, CGY, etc.

    # Data type information
    datatype: Mapped[int | None] = Column("Datatype", Integer, nullable=True, default=1)
    bitpix: Mapped[int | None] = Column("Bitpix", Integer, nullable=True, default=32)
    bytes_pix: Mapped[int | None] = Column("BytesPix", Integer, nullable=True, default=4)
    vol_max: Mapped[float | None] = Column("VolMax", Float, nullable=True, default=0.0)
    vol_min: Mapped[float | None] = Column("VolMin", Float, nullable=True, default=0.0)
    dose_comment: Mapped[str | None] = Column("DoseComment", String, nullable=True, default="")
    dose_grid_scaling: Mapped[float | None] = Column("DoseGridScaling", Float, nullable=True, default=1.0)
    dose_summation_type: Mapped[str | None] = Column("DoseSummationType", String, nullable=True, default="PLAN")  # PLAN, BEAM, etc.

    # References to other objects
    referenced_plan_id: Mapped[str | None] = Column("ReferencedPlanID", String, nullable=True)

    # For storing serialized beam numbers
    _referenced_beam_numbers: Mapped[str | None] = Column("ReferencedBeamNumbers", String, nullable=True)

    # Parent relationships:
    # All Dose instances should be associated with a trial.dose_grid
    dose_grid_id: Mapped[int | None] = Column("DoseGridID", Integer, ForeignKey("DoseGrid.ID"), nullable=True)
    dose_grid: Mapped["DoseGrid | None"] = relationship(
        "DoseGrid",
        back_populates="dose_list",
        lazy="joined",  # Optimize loading as dose grid info is frequently needed
    )

    # For beam dose, associate the dose with the given beam
    beam_id: Mapped[int | None] = Column("BeamID", Integer, ForeignKey("Beam.ID"), nullable=True)
    beam: Mapped["Beam | None"] = relationship(
        "Beam",
        back_populates="dose",
        lazy="joined",  # Optimize loading as beam info is frequently needed
    )

    # For trial dose (i.e., the sum of beam doses), associate the dose with the given trial
    trial_id: Mapped[int | None] = Column("TrialID", Integer, ForeignKey("Trial.ID"), nullable=True)
    trial: Mapped["Trial | None"] = relationship(
        "Trial",
        back_populates="dose",
        lazy="joined",  # Optimize loading as trial info is frequently needed
    )

    # Child relationship
    max_dose_point: Mapped["MaxDosePoint | None"] = relationship(
        "MaxDosePoint",
        back_populates="dose",
        uselist=False,
        cascade="all, delete-orphan",
        lazy="joined",  # Optimize loading as max dose point is small and frequently accessed
    )

    # For storing serialized pixel data (optional, could use external storage)
    # We're not storing pixel data in the database to avoid making it unnecessarily large
    # _pixel_data_blob: Mapped[bytes] | None = Column("PixelDataBlob", LargeBinary, nullable=True)

    # Transient attributes (not stored in database)
    _pixel_data: np.ndarray[np.float32, np.dtype[np.float32]] | None = None

    def __init__(self, **kwargs: Any) -> None:
        """Initialize a Dose instance with optional attributes and relationships.

        Args:
            **kwargs: Keyword arguments to initialize Dose attributes.
                Common attributes include:
                - dose_id (str): Unique identifier for the dose
                - dose_type (str): Type of dose (e.g., 'PHYSICAL', 'EFFECTIVE')
                - dose_unit (str): Unit of dose (e.g., 'GY', 'CGY')
                - dose_summation_type (str): Type of dose summation (e.g., 'PLAN', 'BEAM')
                - pixel_data (np.ndarray): Optional numpy array containing dose grid data
                - referenced_beam_numbers (list[int]): List of beam numbers this dose references

        Relationships:
            dose_grid (DoseGrid): Parent DoseGrid containing spatial information (many-to-one).
            beam (Beam): Associated Beam for beam-specific doses (many-to-one, optional).
            trial (Trial): Associated Trial for trial-level doses (many-to-one, optional).
            max_dose_point (MaxDosePoint): Point of maximum dose (one-to-one).

        Example:
            >>> dose = Dose(
            ...     dose_id='D1',
            ...     dose_type='PHYSICAL',
            ...     dose_unit='GY',
            ...     dose_summation_type='PLAN',
            ...     pixel_data=np.zeros((100, 100, 50)),
            ...     referenced_beam_numbers=[1, 2, 3]
            ... )
        """
        # Handle referenced_beam_numbers if provided. These are stored as a comma-separated list of integers in the database
        beam_numbers = kwargs.pop("referenced_beam_numbers", None)
        if beam_numbers is not None:
            self.referenced_beam_numbers = beam_numbers

        # Handle pixel_data if provided
        pixel_data = kwargs.pop("pixel_data", None)
        if pixel_data is not None:
            self.pixel_data = pixel_data

        super().__init__(**kwargs)

    @classmethod
    def create_dose_object(
        cls,
        dose_type: str,
        dose_unit: str,
        dose_summation_type: str,
        dose_grid: "DoseGrid",
        dose_comment: str,
        pixel_data: npt.NDArray[np.float32],
        trial: "Trial | None" = None,
        beam: "Beam | None" = None,
    ) -> "Dose":
        """
        Class method to create a Dose object with consolidated None value checking.
        
        Args:
            dose_type: Type of dose (e.g., "PHYSICAL")
            dose_unit: Unit of dose (e.g., "CGY")
            dose_summation_type: Type of summation (e.g., "PLAN", "BEAM")
            dose_grid: DoseGrid object containing spatial information
            dose_comment: Comment for the dose
            pixel_data: Numpy array containing dose data
            trial: Optional Trial object to associate with dose
            beam: Optional Beam object to associate with dose
            
        Returns:
            Dose object with all spatial parameters properly set
        """
        return cls(
            dose_type=dose_type,
            dose_unit=dose_unit,
            dose_summation_type=dose_summation_type,
            dose_grid=dose_grid,
            dose_grid_id=dose_grid.id if dose_grid else None,
            dose_comment=dose_comment,
            pixel_data=pixel_data,
            trial=trial,
            beam=beam,
        )

    @property
    def referenced_beam_numbers(self) -> list[int]:
        """Get the referenced beam numbers."""
        if not self._referenced_beam_numbers:
            return []
        return [int(num) for num in self._referenced_beam_numbers.split(",") if num]

    @referenced_beam_numbers.setter
    def referenced_beam_numbers(self, value: list[int]) -> None:
        """Set the referenced beam numbers."""
        if not value:
            self._referenced_beam_numbers = None
        else:
            self._referenced_beam_numbers = ",".join(str(num) for num in value)

    @property
    def pixel_data(self) -> np.ndarray[np.float32, np.dtype[np.float32]] | None:
        """Get the pixel data."""
        return self._pixel_data

    @pixel_data.setter
    def pixel_data(self, value: np.ndarray[np.float32, np.dtype[np.float32]] | None) -> None:
        """Set the pixel data."""
        self._pixel_data = value

    def get_dose_dimensions(self) -> Tuple[int, int, int]:
        """
        Get the dose grid dimensions.

        Returns:
            Tuple of (x_dim, y_dim, z_dim).
        """
        if self.dose_grid is None:
            raise ValueError("DoseGrid is not set for this Dose object.")

        dim = self.dose_grid.dimension
        if dim is None or dim.x is None or dim.y is None or dim.z is None:
            raise ValueError("DoseGrid dimension is not completely defined.")
        return dim.x, dim.y, dim.z

    def get_dose_grid_resolution(self) -> Tuple[float, float, float]:
        """
        Get the dose grid resolution.

        Returns:
            Tuple of (x_pixdim, y_pixdim, z_pixdim) in mm.
        """
        if self.dose_grid is None:
            raise ValueError("DoseGrid is not set for this Dose object.")

        voxel_size = self.dose_grid.voxel_size
        if voxel_size is None or voxel_size.x is None or voxel_size.y is None or voxel_size.z is None:
            raise ValueError("DoseGrid voxel size is not completely defined.")
        return voxel_size.x, voxel_size.y, voxel_size.z

    def get_dose_grid_origin(self) -> Tuple[float, float, float]:
        """
        Get the dose grid origin.

        Returns:
            Tuple of (x_start, y_start, z_start) in mm.
        """
        if self.dose_grid is None:
            raise ValueError("DoseGrid is not set for this Dose object.")

        origin = self.dose_grid.origin
        if origin is None or origin.x is None or origin.y is None or origin.z is None:
            raise ValueError("DoseGrid origin is not completely defined.")
        return origin.x, origin.y, origin.z

    def get_slice_data(self, slice_index: int) -> np.ndarray[np.float32, np.dtype[np.float32]] | None:
        """
        Get the dose data for a specific slice.

        Args:
            slice_index: Index of the slice to retrieve.

        Returns:
            2D numpy array of dose data for the specified slice, or None if dose data is not available.
        """
        if self.pixel_data is None:
            return None

        dimensions = self.get_dose_dimensions()
        if slice_index >= dimensions[2]:
            return None

        return self.pixel_data[slice_index, :, :]

    def set_slice_data(self, slice_index: int, data: np.ndarray[np.float32, np.dtype[np.float32]]) -> None:
        """
        Set the dose data for a specific slice.

        Args:
            slice_index: Index of the slice to set.
            data: 2D numpy array of dose data for the slice.
        """
        dimensions = self.get_dose_dimensions()

        if self.pixel_data is None:
            # Initialize dose data array if it doesn't exist (z, y, x order)
            self.pixel_data = np.zeros((dimensions[2], dimensions[1], dimensions[0]), dtype=np.float32)

        if slice_index < dimensions[2]:
            self.pixel_data[slice_index, :, :] = data

    def get_dose_value_at_index(self, ix: int | float, iy: int | float, iz: int | float) -> float | None:
        """
        Get the dose value at a specific grid point index. This can be a continuous index or an integer index.

        Args:
            ix: X index (in grid space).
            iy: Y index (in grid space).
            iz: Z index (in grid space).

        Returns:
            Dose value at the specified grid point, or None if coordinates are out of bounds or dose data is not available.
        """
        if self.pixel_data is None:
            return None

        dimensions = self.get_dose_dimensions()
        if ix < 0 or ix >= dimensions[0] or iy < 0 or iy >= dimensions[1] or iz < 0 or iz >= dimensions[2]:
            return None

        # If the index is an integer, return the dose value
        if ix == int(ix) and iy == int(iy) and iz == int(iz):
            return float(self.pixel_data[int(iz), int(iy), int(ix)]) * float(self.dose_grid_scaling or 1.0)

        # For a continuous index, interpolate the dose value
        # Clamp indices to valid range for interpolation
        x_dim, y_dim, z_dim = dimensions
        if not (0 <= ix < x_dim - 1 and 0 <= iy < y_dim - 1 and 0 <= iz < z_dim - 1):
            return None

        # Get integer parts and fractional parts
        x0, y0, z0 = int(np.floor(ix)), int(np.floor(iy)), int(np.floor(iz))
        x1, y1, z1 = x0 + 1, y0 + 1, z0 + 1
        dx, dy, dz = ix - x0, iy - y0, iz - z0

        # Fetch the 8 neighboring values
        # Note: pixel_data is indexed as (z, y, x)
        c000 = self.pixel_data[z0, y0, x0]
        c100 = self.pixel_data[z0, y0, x1]
        c010 = self.pixel_data[z0, y1, x0]
        c110 = self.pixel_data[z0, y1, x1]
        c001 = self.pixel_data[z1, y0, x0]
        c101 = self.pixel_data[z1, y0, x1]
        c011 = self.pixel_data[z1, y1, x0]
        c111 = self.pixel_data[z1, y1, x1]

        # Trilinear interpolation
        c00 = c000 * (1 - dx) + c100 * dx
        c01 = c001 * (1 - dx) + c101 * dx
        c10 = c010 * (1 - dx) + c110 * dx
        c11 = c011 * (1 - dx) + c111 * dx

        c0 = c00 * (1 - dy) + c10 * dy
        c1 = c01 * (1 - dy) + c11 * dy

        c = c0 * (1 - dz) + c1 * dz

        return float(c) * float(self.dose_grid_scaling or 1.0)

    def get_dose_value_at_point(self, x: float, y: float, z: float) -> float | None:
        """
        Get the dose value at a specific point.

        Args:
            x: X coordinate in mm.
            y: Y coordinate in mm.
            z: Z coordinate in mm.

        Returns:

        Returns:
            Dose value at the specified point, or None if coordinates are out of bounds or dose data is not available.
        """
        if self.pixel_data is None:
            return None

        origin = self.get_dose_grid_origin()
        resolution = self.get_dose_grid_resolution()

        iy = (y - origin[1]) / resolution[1]
        iz = (z - origin[2]) / resolution[2]
        ix = (x - origin[0]) / resolution[0]

        return self.get_dose_value_at_index(ix, iy, iz)

    def get_max_dose(self) -> float | None:
        """
        Get the maximum dose value in the dose grid.

        Returns:
            Maximum dose value, or None if dose data is not available.
        """
        if self.pixel_data is None:
            return None

        return float(np.max(self.pixel_data) * float(self.dose_grid_scaling or 1.0))

    def get_min_dose(self) -> float | None:
        """
        Get the minimum dose value in the dose grid.

        Returns:
            Minimum dose value, or None if dose data is not available.
        """
        if self.pixel_data is None:
            return None

        return float(np.min(self.pixel_data) * float(self.dose_grid_scaling or 1.0))

    def get_mean_dose(self) -> float | None:
        """
        Get the mean dose value in the dose grid.

        Returns:
            Mean dose value, or None if dose data is not available.
        """
        if self.pixel_data is None:
            return None

        return float(np.mean(self.pixel_data) * float(self.dose_grid_scaling or 1.0))

    def __repr__(self) -> str:
        """
        Return a string representation of this dose.
        """
        return f"<Dose(id='{self.dose_id}', type='{self.dose_type}', dimensions={self.get_dose_dimensions()})>"


class MaxDosePoint(PinnacleBase):
    """Model representing the maximum dose point in a dose distribution.

    This class stores the spatial location and properties of the point receiving
    the maximum dose in a treatment plan, beam, or trial. It includes display
    properties for visualization and references to the associated dose and beam.

    Attributes:
        color (str): RGB color string for display (e.g., '255,0,0' for red).
        display_2d (str): Display settings for 2D views.
        dose_value (float): The maximum dose value at this point.
        dose_units (str): Units of the dose value (e.g., 'GY').
        location_x (float): X-coordinate of the maximum dose point in mm.
        location_y (float): Y-coordinate of the maximum dose point in mm.
        location_z (float): Z-coordinate of the maximum dose point in mm.

    Relationships:
        beam (Beam): The Beam associated with this maximum dose point (many-to-one).
        dose (Dose): The parent Dose containing this point (many-to-one).
        trial (Trial): The parent Trial for this point (many-to-one).
    """

    __tablename__: str = "MaxDosePoint"

    # Primary key is inherited from PinnacleBase

    # Only Color and Display2d are specified in the plan.Trial file
    color: Mapped[str | None] = Column("Color", String, nullable=True)
    display_2d: Mapped[str | None] = Column("Display2D", String, nullable=True)

    # The following fields are added for convenience but are not part of the original Pinnacle plan.Trial file
    dose_value: Mapped[float | None] = Column("DoseValue", Float, nullable=True)
    dose_units: Mapped[str | None] = Column("DoseUnits", String, nullable=True)
    location_x: Mapped[float | None] = Column("LocationX", Float, nullable=True)
    location_y: Mapped[float | None] = Column("LocationY", Float, nullable=True)
    location_z: Mapped[float | None] = Column("LocationZ", Float, nullable=True)

    # Parent relationships
    beam_id: Mapped[int | None] = Column("BeamID", Integer, ForeignKey("Beam.ID"), nullable=True)
    beam = relationship(
        "Beam",
        back_populates="max_dose_point",
        lazy="joined",  # Optimize loading as beam info is frequently needed
    )
    dose_id: Mapped[int | None] = Column("DoseID", Integer, ForeignKey("Dose.ID"), nullable=True)
    dose = relationship(
        "Dose",
        back_populates="max_dose_point",
        lazy="joined",  # Optimize loading as dose info is frequently needed
    )
    trial_id: Mapped[int] = Column("TrialID", Integer, ForeignKey("Trial.ID"))
    trial = relationship(
        "Trial",
        back_populates="max_dose_point",
        lazy="joined",  # Optimize loading as trial info is frequently needed
    )

    def __repr__(self) -> str:
        color = self.color if self.color else "None"
        dose = self.dose_value if self.dose_value is not None else ""
        if dose and self.dose_units:
            dose = f"{dose} {self.dose_units}"
        if not dose:
            dose = "None"
        return f"<MaxDosePoint(id={self.id}, color={color}, dose={dose})>"

    def __init__(self, **kwargs: Any) -> None:
        """Initialize a MaxDosePoint instance with optional attributes.

        Args:
            **kwargs: Keyword arguments to initialize MaxDosePoint attributes.
                Common attributes include:
                - color (str): RGB color string for display
                - dose_value (float): Maximum dose value
                - location_x (float): X-coordinate in mm
                - location_y (float): Y-coordinate in mm
                - location_z (float): Z-coordinate in mm
                - beam_id (int): ID of associated Beam
                - dose_id (int): ID of parent Dose
                - trial_id (int): ID of parent Trial

        Relationships:
            beam (Beam): Associated Beam (many-to-one).
            dose (Dose): Parent Dose (many-to-one).
            trial (Trial): Parent Trial (many-to-one).

        Example:
            >>> max_point = MaxDosePoint(
            ...     color='255,0,0',
            ...     dose_value=72.5,
            ...     dose_units='GY',
            ...     location_x=10.5,
            ...     location_y=-5.2,
            ...     location_z=15.8
            ... )
        """
        super().__init__(**kwargs)
