"""
SQLAlchemy model for Pinnacle Point data.

This module provides the Point data model for representing points of interest (POIs)
in Pinnacle treatment plans. Points can represent various treatment-relevant locations
such as isocenter points, registration landmarks, dose reference points, or anatomical
markers. Each point maintains its 3D coordinates, display properties, and metadata
needed for treatment planning and DICOM export.
"""

from __future__ import annotations
from typing import Tuple, TYPE_CHECKING, Any

from sqlalchemy import Column, Integer, String, Float, ForeignKey
from sqlalchemy.orm import Mapped, relationship

from pinnacle_io.models.versioned_base import VersionedBase

if TYPE_CHECKING:
    from pinnacle_io.models.plan import Plan


class Point(VersionedBase):
    """
    Model representing a point of interest (POI) in a Pinnacle treatment plan.

    Points of Interest are key locations in the treatment plan that serve various
    purposes in radiation therapy planning and delivery. These can include:
    - Treatment isocenters
    - Dose reference points
    - Registration landmarks
    - Anatomical markers
    - Setup reference points
    - DRR reference points

    Technical Details:
        - Inherits version tracking from VersionedBase
        - Stores 3D coordinates in the treatment planning system
        - Supports rotation information for oriented points
        - Maintains display properties for visualization
        - Links to parent plan for context
        - Provides coordinate system transformation support

    Use Cases:
        - Define treatment delivery points
        - Mark anatomical landmarks
        - Specify dose calculation reference points
        - Support image registration
        - Guide patient setup
        - Define DRR visualization markers

    Attributes:
        id (int): Primary key (inherited from VersionedBase)
        name (str): Descriptive name of the point
        x_coord (float): X coordinate in the specified coordinate system
        y_coord (float): Y coordinate in the specified coordinate system
        z_coord (float): Z coordinate in the specified coordinate system
        x_rotation (float): Rotation around X axis in degrees
        y_rotation (float): Rotation around Y axis in degrees
        z_rotation (float): Rotation around Z axis in degrees
        radius (float): Display radius for visualization
        color (str): Display color for visualization
        coord_sys (str): Coordinate system (e.g., "CT", "DICOM")
        coordinate_format (str): Format string for coordinate display
        display_2d (str): 2D display mode
        display_3d (str): 3D display mode
        volume_name (str): Associated volume name
        poi_interpreted_type (str): Point type interpretation
        poi_display_on_other_volumes (int): Flag for multi-volume display
        is_locked (int): Lock state of the point

    Properties:
        coordinates (Tuple[float, float, float]): Point coordinates as (x, y, z)

    Relationships:
        plan (Plan): Parent plan containing this point (many-to-one)
    """

    __tablename__: str = "Point"

    # Point-specific information
    name: Mapped[str | None] = Column("Name", String, nullable=True)
    x_coord: Mapped[float | None] = Column("XCoord", Float, nullable=True)
    y_coord: Mapped[float | None] = Column("YCoord", Float, nullable=True)
    z_coord: Mapped[float | None] = Column("ZCoord", Float, nullable=True)
    x_rotation: Mapped[float | None] = Column("XRotation", Float, nullable=True)
    y_rotation: Mapped[float | None] = Column("YRotation", Float, nullable=True)
    z_rotation: Mapped[float | None] = Column("ZRotation", Float, nullable=True)
    radius: Mapped[float | None] = Column("Radius", Float, nullable=True)
    color: Mapped[str | None] = Column("Color", String, nullable=True)
    coord_sys: Mapped[str | None] = Column("CoordSys", String, nullable=True)
    coordinate_format: Mapped[str | None] = Column("CoordinateFormat", String, nullable=True)
    display_2d: Mapped[str | None] = Column("Display2d", String, nullable=True)
    display_3d: Mapped[str | None] = Column("Display3d", String, nullable=True)
    volume_name: Mapped[str | None] = Column("VolumeName", String, nullable=True)
    poi_interpreted_type: Mapped[str | None] = Column("PoiInterpretedType", String, nullable=True)
    poi_display_on_other_volumes: Mapped[int | None] = Column("PoiDisplayOnOtherVolumes", Integer, nullable=True)
    is_locked: Mapped[int | None] = Column("IsLocked", Integer, nullable=True)

    # Parent relationship with optimized loading
    plan_id: Mapped[int | None] = Column("PlanID", Integer, ForeignKey("Plan.ID", ondelete="CASCADE"), nullable=True)
    plan: Mapped["Plan | None"] = relationship(
        "Plan",
        back_populates="point_list",
        lazy="joined",  # Optimize loading as points are frequently accessed with plans
    )

    def __init__(self, **kwargs: Any) -> None:
        """
        Initialize a Point instance.

        Args:
            **kwargs: Keyword arguments for initializing Point attributes.
                     See class attributes documentation for available fields.

        Example:
            >>> point = Point(
            ...     name="Isocenter",
            ...     x_coord=10.0,
            ...     y_coord=20.0,
            ...     z_coord=30.0,
            ...     poi_interpreted_type="ISOCENTER"
            ... )
        """
        super().__init__(**kwargs)

    @property
    def coordinates(self) -> Tuple[float | None, float | None, float | None]:
        """
        Get the point coordinates as a tuple.

        Returns:
            A tuple of (x, y, z) coordinates. Each coordinate may be None if not set.

        Example:
            >>> point = Point(x_coord=1.0, y_coord=2.0, z_coord=3.0)
            >>> x, y, z = point.coordinates
            >>> print(f"Point at ({x}, {y}, {z})")
            'Point at (1.0, 2.0, 3.0)'
        """
        return (self.x_coord, self.y_coord, self.z_coord)

    def __repr__(self) -> str:
        """Generate a string representation of the Point."""
        return f"<Point(id={self.id}, name='{self.name}', coordinates={self.coordinates})>"
