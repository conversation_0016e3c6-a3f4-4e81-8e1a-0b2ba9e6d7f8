"""
SQLAlchemy model for Pinnacle PatientLite data.
"""

from datetime import datetime
from typing import TYPE_CHECKING, Any

from sqlalchemy import Column, String, Integer, Float, ForeignKey, DateTime
from sqlalchemy.orm import Mapped, relationship

from pinnacle_io.models.pinnacle_base import PinnacleBase

if TYPE_CHECKING:
    from pinnacle_io.models.institution import Institution


class PatientLite(PinnacleBase):
    """
    Model representing a lightweight Pinnacle Patient record.

    This class provides a streamlined representation of patient data for efficient
    loading and display in institutional patient lists. It contains essential patient
    identification and metadata without the full treatment planning data, making it
    ideal for patient browsing and selection interfaces.

    The PatientLite model serves as a bridge between the Institution and the full
    Patient model, allowing institutions to maintain lists of available patients
    without the overhead of loading complete patient records with all associated
    treatment plans, image sets, and clinical data.

    Attributes:
        id (int): Primary key inherited from PinnacleBase
        patient_id (int): Pinnacle-specific patient identifier
        patient_path (str): Filesystem path to the patient data directory
        mount_point (str): Storage mount point where patient data resides
        dir_size (float): Size of the patient directory in bytes
        last_name (str): <PERSON><PERSON>'s last name
        first_name (str): <PERSON><PERSON>'s first name
        middle_name (str): <PERSON><PERSON>'s middle name or initial
        medical_record_number (str): Hospital medical record number
        physician (str): Name of the attending physician
        last_modified (datetime): Timestamp of last modification to patient data

    Relationships:
        institution (Institution): Parent institution that owns this patient record (many-to-one)

    Database Mapping:
        This model maps to the "PatientLite" table in the Pinnacle database schema.
        Column names follow Pinnacle's PascalCase convention for database compatibility.

    Usage:
        PatientLite instances are typically created automatically when loading
        Institution data from Pinnacle files. They can also be created manually
        for testing or data migration purposes.

        Example:
            >>> patient = PatientLite(
            ...     patient_id=12345,
            ...     patient_path="Institution_1/Mount_0/Patient_12345",
            ...     last_name="Smith",
            ...     first_name="John",
            ...     medical_record_number="MRN001"
            ... )

    Special Initialization:
        The model supports initialization via a 'formatted_description' parameter
        that contains patient details in a delimited string format, allowing for
        efficient bulk loading from Pinnacle data files.
    """

    __tablename__: str = "PatientLite"

    # Primary key is inherited from PinnacleBase
    patient_id: Mapped[int | None] = Column("PatientID", Integer, nullable=True)
    patient_path: Mapped[str] = Column("PatientPath", String, nullable=False)
    mount_point: Mapped[str | None] = Column("MountPoint", String, nullable=True)
    dir_size: Mapped[float | None] = Column("DirSize", Float, nullable=True)
    last_name: Mapped[str | None] = Column("LastName", String, nullable=True)
    first_name: Mapped[str | None] = Column("FirstName", String, nullable=True)
    middle_name: Mapped[str | None] = Column("MiddleName", String, nullable=True)
    medical_record_number: Mapped[str | None] = Column("MedicalRecordNumber", String, nullable=True)
    physician: Mapped[str | None] = Column("Physician", String, nullable=True)
    last_modified: Mapped[datetime | None] = Column("LastModified", DateTime, nullable=True)

    # Parent relationship
    institution_id: Mapped[int | None] = Column(Integer, ForeignKey("Institution.ID"), nullable=True)
    institution: Mapped["Institution | None"] = relationship(
        "Institution",
        back_populates="patient_lite_list",
        lazy="joined",  # Optimize loading as institution info is frequently needed
    )

    def __repr__(self) -> str:
        """
        Return a string representation of this patient lite record.

        Returns:
            str: A string containing the patient's ID, patient_id, and name.
        """
        return f"<PatientLite(id={self.id}, patient_id={self.patient_id}, name='{self.last_name}, {self.first_name}')>"

    def __init__(self, **kwargs: Any):
        """
        Initialize a PatientLite instance.

        Args:
            **kwargs: Keyword arguments used to initialize PatientLite attributes.

        Relationships:
            institution (Institution): The parent Institution to which this PatientLite belongs (many-to-one).

        Notes:
            If the keyword argument 'formatted_description' ('FormattedDescription') is provided,
            it should be a string containing patient details separated by '&&' in the following order:
                'LastName&&FirstName&&MiddleName&&MedicalRecordNumber&&Physician&&LastModified'

            The string is split on '&&' and mapped to the corresponding attributes:
                - last_name
                - first_name
                - middle_name
                - medical_record_number
                - physician
                - last_modified

            If fewer than 6 fields are present, missing fields are set to empty strings.
            The 'last_modified' field is converted to a datetime object if it is provided in the formatted description.
        """
        formatted_description = kwargs.pop("formatted_description", kwargs.pop("FormattedDescription", None))

        super().__init__(**kwargs)

        # Process formatted_description if provided
        if formatted_description:
            parts = formatted_description.split("&&")
            parts += [""] * (6 - len(parts))
            self.last_name = parts[0] if parts[0] else None
            self.first_name = parts[1] if parts[1] else None
            self.middle_name = parts[2] if parts[2] else None
            self.medical_record_number = parts[3] if parts[3] else None
            self.physician = parts[4] if parts[4] else None

            # Convert last_modified string to datetime if provided
            if parts[5]:
                try:
                    # Try to parse the datetime string
                    self.last_modified = datetime.fromisoformat(parts[5].replace("Z", "+00:00"))
                except (ValueError, AttributeError):
                    # If parsing fails, try other common formats
                    try:
                        self.last_modified = datetime.strptime(parts[5], "%Y-%m-%d")
                    except ValueError:
                        # If all parsing attempts fail, set to None
                        self.last_modified = None
            else:
                self.last_modified = None
