"""
SQLAlchemy model for Pinnacle CPManager data.

This module provides the CPManager data model for managing control points in Pinnacle treatment
beams. The CPManager handles the configuration and management of control points for a beam,
including gantry, couch, and collimator movement settings.
"""

from __future__ import annotations
from typing import TYPE_CHECKING, Any, cast, override

from sqlalchemy import Column, String, Integer, ForeignKey
from sqlalchemy.orm import Mapped, relationship

from pinnacle_io.models.pinnacle_base import PinnacleBase
from pinnacle_io.models.control_point import ControlPoint

if TYPE_CHECKING:
    from pinnacle_io.models.beam import Beam


class CPManager(PinnacleBase):
    """
    Model representing a control point manager for a treatment beam.

    This class manages the configuration and collection of control points for a beam,
    including settings for gantry, couch, and collimator movement, as well as MLC and
    jaw configurations.

    Attributes:
        id (int): Primary key
        is_gantry_start_stop_locked (int): Whether gantry start/stop positions are locked
        is_couch_start_stop_locked (int): Whether couch start/stop positions are locked
        is_collimator_start_stop_locked (int): Whether collimator start/stop positions are locked
        is_left_right_independent (int): Whether left/right jaws move independently
        is_top_bottom_independent (int): Whether top/bottom jaws move independently
        _number_of_control_points (int): Number of control points (cached value)
        gantry_is_ccw (int): Gantry rotation direction (1 for counter-clockwise)
        mlc_push_method (str): MLC push method configuration
        jaws_conformance (str): Jaws conformance configuration

    Relationships:
        beam (Beam): The parent beam that this manager belongs to
        control_point_list (list[ControlPoint]): List of control points managed by this manager
    """

    __tablename__: str = "CPManager"

    # Primary key is inherited from PinnacleBase
    is_gantry_start_stop_locked: Mapped[int | None] = Column("IsGantryStartStopLocked", Integer, nullable=True)
    is_couch_start_stop_locked: Mapped[int | None] = Column("IsCouchStartStopLocked", Integer, nullable=True)
    is_collimator_start_stop_locked: Mapped[int | None] = Column("IsCollimatorStartStopLocked", Integer, nullable=True)
    is_left_right_independent: Mapped[int | None] = Column("IsLeftRightIndependent", Integer, nullable=True)
    is_top_bottom_independent: Mapped[int | None] = Column("IsTopBottomIndependent", Integer, nullable=True)
    _number_of_control_points: Mapped[int | None] = Column("NumberOfControlPoints", Integer, nullable=True)
    gantry_is_ccw: Mapped[int | None] = Column("GantryIsCcw", Integer, nullable=True)
    mlc_push_method: Mapped[str | None] = Column("MlcPushMethod", String, nullable=True)
    jaws_conformance: Mapped[str | None] = Column("JawsConformance", String, nullable=True)

    # Parent relationship
    beam_id: Mapped[int] = Column("BeamID", Integer, ForeignKey("Beam.ID"))
    beam: Mapped["Beam"] = relationship(
        "Beam",
        back_populates="cp_manager",
        lazy="joined",  # Optimize loading as beam info is frequently needed
    )

    # Child relationship
    control_point_list: Mapped[list["ControlPoint"]] = relationship(
        "ControlPoint",
        back_populates="cp_manager",
        cascade="all, delete-orphan",
        lazy="selectin",  # Optimize loading for collections that are frequently accessed
    )

    def __init__(self, **kwargs: Any) -> None:
        """
        Initialize a CPManager instance.

        Args:
            **kwargs: Keyword arguments used to initialize CPManager attributes.
                Supported keyword arguments include all column names as attributes and:
                - beam: Beam | None - The parent beam
                - control_point_list: list[ControlPoint] | None - List of control points
        """
        # Initialize control point list if provided
        self.control_point_list = cast(list["ControlPoint"], kwargs.pop("control_point_list", []))
        super().__init__(**kwargs)

    @override
    def __repr__(self) -> str:
        """
        Return a string representation of this control point manager.

        Returns:
            str: A string representation in the format:
                <CPManager(id=X, beam='beam_name', number_of_control_points=Y)>
        """
        beam_name = getattr(getattr(self, "beam", ""), "name", "") or ""
        return f"<CPManager(id={self.id}, beam='{beam_name}', number_of_control_points={self.number_of_control_points})>"

    @property
    def number_of_control_points(self) -> int:
        """
        Get the number of control points.

        Returns:
            int: The number of control points in the control_point_list.
                Returns 0 if control_point_list is None.
        """
        return len(self.control_point_list) if self.control_point_list else 0

    @number_of_control_points.setter
    def number_of_control_points(self, value: int) -> None:
        """
        Set the number of control points.

        Args:
            value (int): The number of control points to set.
                This updates the cached value but doesn't modify the actual control point list.

        Note:
            This method only updates the cached value and doesn't modify the actual
            control points list. Use with caution as it may cause inconsistency.
        """
        try:
            int_value = int(value)
            if int_value < 0 or int_value != value:
                raise ValueError("Number of control points must be a non-negative integer")
        except ValueError:
            raise ValueError("Number of control points must be a non-negative integer")

        self._number_of_control_points = int_value

    def add_control_point(self, control_point: "ControlPoint") -> None:
        """
        Add a control point to this manager.

        Args:
            control_point: The ControlPoint instance to add.
                The control point will be associated with this manager.

        Raises:
            TypeError: If control_point is not a ControlPoint instance
            ValueError: If the control point is already associated with another manager
        """
        if control_point in self.control_point_list:
            return  # Already in the list

        if not isinstance(control_point, ControlPoint):  # type: ignore
            raise TypeError("control_point must be a ControlPoint instance")

        if control_point.cp_manager is not None and control_point.cp_manager is not self:
            raise ValueError("Control point is already associated with another manager")

        self.control_point_list.append(control_point)
        control_point.cp_manager = self

    def remove_control_point(self, control_point: "ControlPoint") -> bool:
        """
        Remove a control point from this manager.

        Args:
            control_point: The ControlPoint instance to remove.

        Returns:
            bool: True if the control point was removed, False if it wasn't found
        """
        if control_point in self.control_point_list:
            self.control_point_list.remove(control_point)
            control_point.cp_manager = None
            return True
        return False
