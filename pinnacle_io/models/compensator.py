"""
SQLAlchemy model for Pinnacle Compensator data.

This module provides the Compensator data models for representing beam compensator configuration.
"""

from typing import TYPE_CHECKING, Any, override

from sqlalchemy import Column, String, Integer, ForeignKey, Float, LargeBinary
from sqlalchemy.orm import Mapped, relationship

from pinnacle_io.models.pinnacle_base import PinnacleBase

if TYPE_CHECKING:
    from pinnacle_io.models.beam import Beam


class Compensator(PinnacleBase):
    """
    Model representing a beam compensator in Pinnacle treatment planning.

    This class stores comprehensive compensator information for radiation therapy beam
    modification and dose compensation. Compensators are physical devices used to
    modulate beam intensity and improve dose uniformity across the target volume.
    They are particularly important in proton therapy and specialized photon treatments
    where precise dose shaping is required.

    The compensator model handles both the physical properties (dimensions, material
    density, thickness maps) and the computational aspects (optimization parameters,
    dose statistics, milling specifications) needed for treatment planning and
    manufacturing.

    Technical Details:
        - Supports both photon and proton beam compensators
        - Handles binary thickness map data for precise dose calculations
        - Includes optimization parameters for automated compensator design
        - Provides milling specifications for physical manufacturing
        - Tracks dose statistics and homogeneity metrics
        - Supports various export formats for treatment delivery systems

    Use Cases:
        - Proton therapy range compensation
        - Photon beam intensity modulation
        - Dose uniformity optimization
        - Treatment plan quality assurance
        - Compensator manufacturing and export

    Attributes:
        id (int): Primary key identifier
        name (str): Human-readable name of the compensator
        export_name (str): Name used when exporting to treatment delivery systems
        tray_number (str): Physical tray identifier for compensator mounting
        export_format (str): Format specification for export (e.g., "DICOM", "ASCII")
        is_valid (int): Flag indicating if compensator data is valid (0/1)
        generated_automatically (int): Flag indicating automatic generation (0/1)

        # Physical Dimensions and Properties
        width (float): Physical width of the compensator in mm
        height (float): Physical height of the compensator in mm
        resolution_x (float): Spatial resolution in X direction (mm/pixel)
        resolution_y (float): Spatial resolution in Y direction (mm/pixel)
        display_resolution (float): Display resolution for visualization (mm)
        density (float): Material density in g/cm³ (typically 1.0-2.0 for plastics)

        # Proton-Specific Parameters
        proton_source_to_compensator_distance (float): Distance from source to compensator (mm)
        compensator_hangs_down (int): Flag for compensator orientation (0/1)
        plane_depth (float): Depth of compensation plane in patient (mm)

        # Thickness and Optimization
        thickness (bytes): Binary array data representing thickness map
        min_allowable_thickness (float): Minimum thickness constraint (mm)
        max_allowable_thickness (float): Maximum thickness constraint (mm)
        initial_thickness (float): Starting thickness for optimization (mm)

        # Optimization Parameters
        max_iterations (int): Maximum optimization iterations
        cutoff_homogeneity (float): Target homogeneity threshold (%)
        actual_homogeneity (float): Achieved homogeneity after optimization (%)
        start_optimization_with_current (int): Use current thickness as starting point (0/1)
        optimize_to_min_dose_first_iteration (int): Minimize dose in first iteration (0/1)

        # Manufacturing and Milling
        milling_x_scaler (float): X-axis scaling factor for milling machine
        milling_y_scaler (float): Y-axis scaling factor for milling machine
        milling_z_scaler (float): Z-axis scaling factor for milling machine
        milling_thickness (float): Base thickness for milling operations (mm)
        positive_milled (float): Amount of positive milling applied (mm)

        # Data Processing
        round_data (int): Flag to enable thickness data rounding (0/1)
        rounding_value (float): Rounding increment for thickness values (mm)
        scale_width_height (int): Flag to scale dimensions (0/1)
        include_poly_base (int): Include polynomial base in calculations (0/1)
        resample_using_linear_interpolation (int): Use linear interpolation for resampling (0/1)
        center_at_zero (int): Center compensator at coordinate origin (0/1)

        # Edge and Fill Parameters
        edge_of_field_border (float): Border width at field edge (mm)
        fill_outside_mode (str): Method for filling outside field ("ZERO", "EDGE", "EXTRAPOLATE")
        fill_outside_thickness (float): Thickness value for outside-field regions (mm)

        # Dose Statistics and Quality Metrics
        dose_comp_mode (str): Dose compensation mode ("UNIFORM", "OPTIMIZE", "MANUAL")
        output_factor (float): Beam output factor with compensator
        dose_min (float): Minimum dose in compensated field (cGy)
        dose_max (float): Maximum dose in compensated field (cGy)
        dose_mean (float): Mean dose in compensated field (cGy)
        dose_std_dev (float): Standard deviation of dose distribution (cGy)

        # Array Dimensions
        wet_x_dim (int): X dimension of thickness array (pixels)
        wet_y_dim (int): Y dimension of thickness array (pixels)
        array_object_name (str): Name of associated array object
        type (str): Compensator type classification

    Relationships:
        beam (Beam): Parent beam that this compensator modifies (many-to-one)

    Example:
        >>> compensator = Compensator(
        ...     name="Proton_Range_Comp_001",
        ...     width=200.0,
        ...     height=200.0,
        ...     density=1.05,
        ...     resolution_x=1.0,
        ...     resolution_y=1.0
        ... )
        >>> print(f"Compensator: {compensator.name}, Size: {compensator.width}x{compensator.height}mm")
    """

    __tablename__: str = "Compensator"

    # Primary key is inherited from PinnacleBase
    name: Mapped[str | None] = Column("Name", String, nullable=True)
    export_name: Mapped[str | None] = Column("ExportName", String, nullable=True)
    tray_number: Mapped[str | None] = Column("TrayNumber", String, nullable=True)
    export_format: Mapped[str | None] = Column("ExportFormat", String, nullable=True)
    is_valid: Mapped[int | None] = Column("IsValid", Integer, nullable=True)
    generated_automatically: Mapped[int | None] = Column("GeneratedAutomatically", Integer, nullable=True)
    proton_source_to_compensator_distance: Mapped[float | None] = Column("ProtonSourceToCompensatorDistance", Float, nullable=True)
    scale_width_height: Mapped[int | None] = Column("ScaleWidthHeight", Integer, nullable=True)
    width: Mapped[float | None] = Column("Width", Float, nullable=True)
    height: Mapped[float | None] = Column("Height", Float, nullable=True)
    resolution_x: Mapped[float | None] = Column("ResolutionX", Float, nullable=True)
    resolution_y: Mapped[float | None] = Column("ResolutionY", Float, nullable=True)
    display_resolution: Mapped[float | None] = Column("DisplayResolution", Float, nullable=True)
    compensator_hangs_down: Mapped[int | None] = Column("CompensatorHangsDown", Integer, nullable=True)
    include_poly_base: Mapped[int | None] = Column("IncludePolyBase", Integer, nullable=True)
    max_iterations: Mapped[int | None] = Column("MaxIterations", Integer, nullable=True)
    min_allowable_thickness: Mapped[float | None] = Column("MinAllowableThickness", Float, nullable=True)
    max_allowable_thickness: Mapped[float | None] = Column("MaxAllowableThickness", Float, nullable=True)
    round_data: Mapped[int | None] = Column("RoundData", Integer, nullable=True)
    rounding_value: Mapped[float | None] = Column("RoundingValue", Float, nullable=True)
    cutoff_homogeneity: Mapped[float | None] = Column("CutoffHomogeneity", Float, nullable=True)
    start_optimization_with_current: Mapped[int | None] = Column("StartOptimizationWithCurrent", Integer, nullable=True)
    actual_homogeneity: Mapped[float | None] = Column("ActualHomogeneity", Float, nullable=True)
    output_factor: Mapped[float | None] = Column("OutputFactor", Float, nullable=True)
    density: Mapped[float | None] = Column("Density", Float, nullable=True)
    edge_of_field_border: Mapped[float | None] = Column("EdgeOfFieldBorder", Float, nullable=True)
    initial_thickness: Mapped[float | None] = Column("InitialThickness", Float, nullable=True)
    milling_x_scaler: Mapped[float | None] = Column("MillingXScaler", Float, nullable=True)
    milling_y_scaler: Mapped[float | None] = Column("MillingYScaler", Float, nullable=True)
    milling_z_scaler: Mapped[float | None] = Column("MillingZScaler", Float, nullable=True)
    milling_thickness: Mapped[float | None] = Column("MillingThickness", Float, nullable=True)
    positive_milled: Mapped[float | None] = Column("PositiveMilled", Float, nullable=True)
    optimize_to_min_dose_first_iteration: Mapped[int | None] = Column("OptimizeToMinDoseFirstIteration", Integer, nullable=True)
    resample_using_linear_interpolation: Mapped[int | None] = Column("ResampleUsingLinearInterpolation", Integer, nullable=True)
    fill_outside_mode: Mapped[str | None] = Column("FillOutsideMode", String, nullable=True)
    fill_outside_thickness: Mapped[float | None] = Column("FillOutsideThickness", Float, nullable=True)
    dose_comp_mode: Mapped[str | None] = Column("DoseCompMode", String, nullable=True)
    type: Mapped[str | None] = Column("Type", String, nullable=True)
    plane_depth: Mapped[float | None] = Column("PlaneDepth", Float, nullable=True)
    array_object_name: Mapped[str | None] = Column("ArrayObjectName", String, nullable=True)
    center_at_zero: Mapped[int | None] = Column("CenterAtZero", Integer, nullable=True)
    thickness: Mapped[bytes | None] = Column("Thickness", LargeBinary, nullable=True)  # Store array as binary
    dose_min: Mapped[float | None] = Column("DoseMin", Float, nullable=True)
    dose_max: Mapped[float | None] = Column("DoseMax", Float, nullable=True)
    dose_mean: Mapped[float | None] = Column("DoseMean", Float, nullable=True)
    dose_std_dev: Mapped[float | None] = Column("DoseStdDev", Float, nullable=True)
    wet_x_dim: Mapped[int | None] = Column("WetXDim", Integer, nullable=True)
    wet_y_dim: Mapped[int | None] = Column("WetYDim", Integer, nullable=True)

    # Parent relationship
    beam_id: Mapped[int] = Column("BeamID", Integer, ForeignKey("Beam.ID"))
    beam: Mapped["Beam"] = relationship(
        "Beam",
        back_populates="compensator",
        lazy="joined",  # Optimize loading as beam info is frequently needed
    )

    def __init__(self, **kwargs: Any):
        """
        Initialize a Compensator instance with optional keyword arguments.

        Args:
            **kwargs: Keyword arguments used to initialize Compensator attributes.

        Relationships:
            beam (Beam): The parent Beam to which this compensator belongs (many-to-one).
        """
        super().__init__(**kwargs)

    @override
    def __repr__(self) -> str:
        """Return string representation of the compensator.

        Returns:
            str: A string containing the compensator's ID and name.
        """
        return f"<Compensator(id={self.id}, name='{self.name}')>"
