"""
SQLAlchemy model for Pinnacle Dose Engine data.

This module provides the Dose Engine data models for representing dose engine configuration.
"""

from __future__ import annotations

from typing import TYPE_CHECKING, Any, override

from sqlalchemy import <PERSON>umn, <PERSON>, Integer, ForeignKey, Float
from sqlalchemy.orm import Mapped, relationship

from pinnacle_io.models.pinnacle_base import PinnacleBase

if TYPE_CHECKING:
    from pinnacle_io.models.beam import Beam


class DoseEngine(PinnacleBase):
    """
    Model representing a dose engine configuration in the Pinnacle treatment planning system.

    This class stores all dose engine-specific information needed for DICOM conversion,
    including type, convolution settings, and statistics tracking. Each DoseEngine instance
    is associated with exactly one Beam.

    Attributes:
        id (int): Primary key (inherited from PinnacleBase)
        type_name (str | None): Type of the dose engine
        convolve_homogeneous (int | None]): Flag for homogeneous convolution
        fluence_homogeneous (int | None]): Flag for homogeneous fluence
        flat_water_phantom (int | None]): Flag for flat water phantom
        flat_homogeneous (int | None]): Flag for flat homogeneous
        electron_homogeneous (int | None]): Flag for electron homogeneous
        fluence_type (str | None): Type of fluence calculation
        long_step_tuning_factor (float | None): Tuning factor for long steps
        short_step_length (float | None): Length of short steps
        number_of_short_steps (int | None]): Count of short steps
        split_fluence_field_size_cutoff (int | None]): Field size cutoff for split fluence
        azimuthal_bin_count (int | None]): Number of azimuthal bins
        zenith_bin_count (int | None]): Number of zenith bins
        cum_kernel_radial_bin_width (float | None): Radial bin width for cumulative kernel
        siddon_corner_cutoff (float | None): Cutoff for Siddon corner detection
        nrd_bin_width (float | None): NRD bin width
        allowable_dose_diff (float | None): Allowed dose difference
        high_fluence_cutoff (float | None): High fluence cutoff value
        low_first_deriv_cutoff (float | None): Low first derivative cutoff
        low_second_deriv_cutoff (float | None): Low second derivative cutoff
        high_first_deriv_cutoff (float | None): High first derivative cutoff
        high_second_deriv_cutoff (float | None): High second derivative cutoff
        adaptive_levels (int | None]): Number of adaptive levels
        energy_flatness_cutoff (float | None): Energy flatness cutoff
        energy_flatness_minimum_distance (float | None): Minimum distance for energy flatness
        energy_flatness_scaling_distance (float | None): Scaling distance for energy flatness
        energy_flatness_power (float | None): Power for energy flatness calculation
        restart_index (int | None]): Index for restarting calculations
        samples_per_batch (int | None]): Number of samples per batch
        number_of_histories_goal (float | None): Target number of histories
        uncertainty_goal (float | None): Target uncertainty level
        max_seconds (float | None): Maximum calculation time in seconds
        completed_histories (int | None]): Number of completed histories
        dose_uncertainty (float | None): Current dose uncertainty
        percent_done (float | None): Completion percentage
        elapsed_seconds (float | None): Elapsed time in seconds
        elapsed_cpu_seconds (float | None): CPU time used in seconds
        cpu_percent_utilization (float | None): CPU utilization percentage
        print_batch_files (int | None]): Flag for printing batch files
        print_data_file (int | None]): Flag for printing data files
        print_event_file (int | None]): Flag for printing event files
        print_track_file (int | None]): Flag for printing track files
        statistics_outside_roi (int | None]): Flag for including statistics outside ROI

    Relationships:
        beam (Beam): The parent Beam to which this DoseEngine belongs (many-to-one).
                     Back-populates to Beam.dose_engine.
    """

    __tablename__: str = "DoseEngine"

    # Primary key is inherited from PinnacleBase
    type_name: Mapped[str | None] = Column("TypeName", String, nullable=True)
    convolve_homogeneous: Mapped[int | None] = Column("ConvolveHomogeneous", Integer, nullable=True)
    fluence_homogeneous: Mapped[int | None] = Column("FluenceHomogeneous", Integer, nullable=True)
    flat_water_phantom: Mapped[int | None] = Column("FlatWaterPhantom", Integer, nullable=True)
    flat_homogeneous: Mapped[int | None] = Column("FlatHomogeneous", Integer, nullable=True)
    electron_homogeneous: Mapped[int | None] = Column("ElectronHomogeneous", Integer, nullable=True)
    fluence_type: Mapped[str | None] = Column("FluenceType", String, nullable=True)
    long_step_tuning_factor: Mapped[float | None] = Column("LongStepTuningFactor", Float, nullable=True)
    short_step_length: Mapped[float | None] = Column("ShortStepLength", Float, nullable=True)
    number_of_short_steps: Mapped[int | None] = Column("NumberOfShortSteps", Integer, nullable=True)
    split_fluence_field_size_cutoff: Mapped[int | None] = Column("SplitFluenceFieldSizeCutoff", Integer, nullable=True)
    azimuthal_bin_count: Mapped[int | None] = Column("AzimuthalBinCount", Integer, nullable=True)
    zenith_bin_count: Mapped[int | None] = Column("ZenithBinCount", Integer, nullable=True)
    cum_kernel_radial_bin_width: Mapped[float | None] = Column("CumKernelRadialBinWidth", Float, nullable=True)
    siddon_corner_cutoff: Mapped[float | None] = Column("SiddonCornerCutoff", Float, nullable=True)
    nrd_bin_width: Mapped[float | None] = Column("NrdBinWidth", Float, nullable=True)
    allowable_dose_diff: Mapped[float | None] = Column("AllowableDoseDiff", Float, nullable=True)
    high_fluence_cutoff: Mapped[float | None] = Column("HighFluenceCutoff", Float, nullable=True)
    low_first_deriv_cutoff: Mapped[float | None] = Column("LowFirstDerivCutoff", Float, nullable=True)
    low_second_deriv_cutoff: Mapped[float | None] = Column("LowSecondDerivCutoff", Float, nullable=True)
    high_first_deriv_cutoff: Mapped[float | None] = Column("HighFirstDerivCutoff", Float, nullable=True)
    high_second_deriv_cutoff: Mapped[float | None] = Column("HighSecondDerivCutoff", Float, nullable=True)
    adaptive_levels: Mapped[int | None] = Column("AdaptiveLevels", Integer, nullable=True)
    energy_flatness_cutoff: Mapped[float | None] = Column("EnergyFlatnessCutoff", Float, nullable=True)
    energy_flatness_minimum_distance: Mapped[float | None] = Column("EnergyFlatnessMinimumDistance", Float, nullable=True)
    energy_flatness_scaling_distance: Mapped[float | None] = Column("EnergyFlatnessScalingDistance", Float, nullable=True)
    energy_flatness_power: Mapped[float | None] = Column("EnergyFlatnessPower", Float, nullable=True)
    restart_index: Mapped[int | None] = Column("RestartIndex", Integer, nullable=True)
    samples_per_batch: Mapped[int | None] = Column("SamplesPerBatch", Integer, nullable=True)
    number_of_histories_goal: Mapped[float | None] = Column("NumberOfHistoriesGoal", Float, nullable=True)
    uncertainty_goal: Mapped[float | None] = Column("UncertaintyGoal", Float, nullable=True)
    max_seconds: Mapped[float | None] = Column("MaxSeconds", Float, nullable=True)
    completed_histories: Mapped[int | None] = Column("CompletedHistories", Integer, nullable=True)
    dose_uncertainty: Mapped[float | None] = Column("DoseUncertainty", Float, nullable=True)
    percent_done: Mapped[float | None] = Column("PercentDone", Float, nullable=True)
    elapsed_seconds: Mapped[float | None] = Column("ElapsedSeconds", Float, nullable=True)
    elapsed_cpu_seconds: Mapped[float | None] = Column("ElapsedCpuSeconds", Float, nullable=True)
    cpu_percent_utilization: Mapped[float | None] = Column("CpuPercentUtilization", Float, nullable=True)
    print_batch_files: Mapped[int | None] = Column("PrintBatchFiles", Integer, nullable=True)
    print_data_file: Mapped[int | None] = Column("PrintDataFile", Integer, nullable=True)
    print_event_file: Mapped[int | None] = Column("PrintEventFile", Integer, nullable=True)
    print_track_file: Mapped[int | None] = Column("PrintTrackFile", Integer, nullable=True)
    statistics_outside_roi: Mapped[int | None] = Column("StatisticsOutsideRoi", Integer, nullable=True)

    # Parent relationship with back-population to Beam.dose_engine
    beam_id: Mapped[int] = Column("BeamID", Integer, ForeignKey("Beam.ID"), nullable=False)
    beam: Mapped["Beam"] = relationship(
        "Beam",
        back_populates="dose_engine",
        lazy="joined",  # Optimize loading as beam info is frequently needed
    )

    def __init__(self, **kwargs: Any) -> None:
        """
        Initialize a DoseEngine instance with the given parameters.

        Args:
            **kwargs: Keyword arguments corresponding to model attributes.
                Supported keyword arguments include all column names as attributes and:
                - beam (Beam): The parent Beam to which this DoseEngine belongs.
        """
        # Initialize parent class with all keyword arguments
        super().__init__(**kwargs)

    @override
    def __repr__(self) -> str:
        """
        Return a string representation of this dose engine.

        Returns:
            str: A string representation in the format:
                <DoseEngine(id=X, beam='beam_name', type_name='type')>
        """
        beam_name = getattr(getattr(self, "beam", ""), "name", "")
        return f"<DoseEngine(id={self.id}, beam='{beam_name}', type_name='{self.type_name}')>"
