"""
SQLAlchemy model for Pinnacle Control Point data.

This module provides the ControlPoint data model for representing control points in
Pinnacle treatment beams, including all control point-specific parameters and relationships
to other beam components like MLCs and wedges.
"""

from __future__ import annotations
from typing import TYPE_CHECKING, Any, override, cast

import numpy as np
from sqlalchemy import <PERSON>umn, Integer, Float, String, ForeignKey
from sqlalchemy.orm import Mapped, relationship

from pinnacle_io.models.pinnacle_base import PinnacleBase

if TYPE_CHECKING:
    from pinnacle_io.models.beam import Beam
    from pinnacle_io.models.cp_manager import CPManager
    from pinnacle_io.models.mlc import MLCLeafPositions
    from pinnacle_io.models.wedge_context import WedgeContext


# Type alias for MLC leaf positions input
try:
    MLCInputType = "np.ndarray | dict[str, Any] | MLCLeafPositions"
except NameError:
    MLCInputType = Any  # Fallback for runtime


class ControlPoint(PinnacleBase):
    """
    Model representing a control point in a treatment beam.

    This class stores all control point-specific information including machine parameters,
    jaw positions, and MLC leaf positions needed for treatment delivery. Each control
    point represents a snapshot of the beam's state during delivery.

    Attributes:
        id (int): Primary key
        index (int): Index of this control point in the beam's control point sequence
        gantry (float): Gantry angle in degrees
        collimator (float): Collimator angle in degrees
        couch (float): Couch angle in degrees
        left_jaw_position (float): Left jaw position in mm
        right_jaw_position (float): Right jaw position in mm
        top_jaw_position (float): Top jaw position in mm
        bottom_jaw_position (float): Bottom jaw position in mm
        weight (float): Weight of this control point
        dose_rate (float): Dose rate in MU/min
        delivery_time (float): Delivery time in seconds

    Relationships:
        beam (Beam): Parent beam that this control point belongs to
        cp_manager (CPManager): Manager for control point data
        _mlc_leaf_positions (MLCLeafPositions): MLC leaf positions for this control point
        wedge_context (WedgeContext): Wedge parameters for this control point
    """

    __tablename__: str = "ControlPoint"

    index: Mapped[int | None] = Column("Index", Integer, nullable=True)  # Not part of the Pinnacle plan.Trial file
    gantry: Mapped[float | None] = Column("Gantry", Float, nullable=True)
    couch: Mapped[float | None] = Column("Couch", Float, nullable=True)
    collimator: Mapped[float | None] = Column("Collimator", Float, nullable=True)
    left_jaw_position: Mapped[float | None] = Column("LeftJawPosition", Float, nullable=True)
    right_jaw_position: Mapped[float | None] = Column("RightJawPosition", Float, nullable=True)
    top_jaw_position: Mapped[float | None] = Column("TopJawPosition", Float, nullable=True)
    bottom_jaw_position: Mapped[float | None] = Column("BottomJawPosition", Float, nullable=True)
    weight: Mapped[float | None] = Column("Weight", Float, nullable=True)
    weight_locked: Mapped[int | None] = Column("WeightLocked", Integer, nullable=True)
    percent_of_arc: Mapped[float | None] = Column("PercentOfArc", Float, nullable=True)
    has_shared_modifier_list: Mapped[int | None] = Column("HasSharedModifierList", Integer, nullable=True)
    mlc_trans_for_display: Mapped[float | None] = Column("MLCTransForDisplay", Float, nullable=True)
    c_arm_angle: Mapped[float | None] = Column("CArmAngle", Float, nullable=True)
    target_projection_valid: Mapped[int | None] = Column("TargetProjectionValid", Integer, nullable=True)
    dose_rate: Mapped[float | None] = Column("DoseRate", Float, nullable=True)
    delivery_time: Mapped[float | None] = Column("DeliveryTime", Float, nullable=True)
    odm: Mapped[str | None] = Column("ODM", String, nullable=True)
    dose_vector: Mapped[str | None] = Column("DoseVector", String, nullable=True)
    cumulative_meterset_weight: Mapped[float | None] = Column("CumulativeMeterset", Float, nullable=True)

    # Parent relationships. Control points are saved in Pinnacle under the Beam -> CPManager.
    # For convenience, control points are also associated with the Beam model directly
    beam_id: Mapped[int | None] = Column("BeamID", Integer, ForeignKey("Beam.ID"), nullable=True)
    beam: Mapped["Beam | None"] = relationship(
        "Beam",
        back_populates="control_point_list",
        lazy="joined",  # Optimize loading as beam info is frequently needed
    )

    cp_manager_id: Mapped[int | None] = Column("CPManagerID", Integer, ForeignKey("CPManager.ID"), nullable=True)
    cp_manager: Mapped["CPManager | None"] = relationship(
        "CPManager",
        back_populates="control_point_list",
        lazy="joined",  # Optimize loading as CP manager is frequently accessed
    )

    # Child relationships
    _mlc_leaf_positions: Mapped["MLCLeafPositions | None"] = relationship(
        "MLCLeafPositions",
        back_populates="control_point",
        uselist=False,
        cascade="all, delete-orphan",
        lazy="select",  # Load on demand as MLC data can be large
    )

    wedge_context: Mapped["WedgeContext | None"] = relationship(
        "WedgeContext",
        back_populates="control_point",
        uselist=False,
        cascade="all, delete-orphan",
        lazy="joined",  # Optimize loading as wedge data is small and frequently needed
    )

    def __init__(self, **kwargs: Any) -> None:
        """
        Initialize a ControlPoint instance.

        Args:
            **kwargs: Keyword arguments used to initialize ControlPoint attributes.
                Supported keyword arguments include all column names as attributes and:
                - mlc_leaf_positions: MLCInputType | None
                - cp_manager: CPManager | None
                - beam: Beam | None
        """
        # Import here for type casting to avoid circular import
        from pinnacle_io.models.cp_manager import CPManager  # type: ignore

        # Handle mlc_leaf_positions if provided
        mlc_leaf_positions = kwargs.pop("mlc_leaf_positions", kwargs.pop("MLCLeafPositions", None))

        # Get cp_manager before super init to handle relationship
        cp_manager = cast(CPManager | None, kwargs.get("cp_manager"))
        if cp_manager is not None and not kwargs.get("beam"):
            # If cp_manager is provided but beam isn't, use the beam from cp_manager
            kwargs["beam"] = cp_manager.beam

        super().__init__(**kwargs)

        # Create related objects after initialization if provided
        if mlc_leaf_positions is not None:
            self._set_mlc_leaf_positions(mlc_leaf_positions)

    @override
    def __repr__(self) -> str:
        """
        Return a string representation of this control point.
        """
        return f"<ControlPoint(id={self.id}, index={self.index}, gantry={self.gantry}, collimator={self.collimator}, couch={self.couch})>"

    @property
    def mlc_leaf_positions(self) -> np.ndarray[np.float32, np.dtype[np.float32]] | None:
        """
        Get the MLC leaf positions as a numpy array.

        Returns:
            np.ndarray | None: A numpy array containing the MLC leaf positions in mm,
                or None if no MLC positions are set.
                Shape is (n_leaves, 2) where the second dimension contains [leaf_left, leaf_right]
                positions relative to the central axis.
        """
        if self._mlc_leaf_positions is not None:
            return self._mlc_leaf_positions.points
        return None

    @property
    def has_mlc(self) -> bool:
        """
        Check if this control point has MLC positions defined.

        Returns:
            bool: True if MLC positions are set, False otherwise.
        """
        return self._mlc_leaf_positions is not None and self._mlc_leaf_positions.points is not None

    def get_jaw_positions(self) -> tuple[float, float, float, float]:
        """
        Get the jaw positions in the standard DICOM coordinate system.

        Returns:
            Tuple[float, float, float, float]: A tuple containing (X1, X2, Y1, Y2) jaw
                positions in mm, where:
                - X1: Left jaw position (negative from isocenter)
                - X2: Right jaw position (positive from isocenter)
                - Y1: Bottom jaw position (negative from isocenter)
                - Y2: Top jaw position (positive from isocenter)

        Note:
            All positions are in the DICOM coordinate system with the beam's eye view.
        """
        return (
            self.left_jaw_position or 0.0,
            self.right_jaw_position or 0.0,
            self.bottom_jaw_position or 0.0,
            self.top_jaw_position or 0.0,
        )

    def get_field_size(self) -> tuple[float, float]:
        """
        Calculate the field size defined by the jaw positions.

        Returns:
            Tuple[float, float]: A tuple containing (width, height) in mm.

        Raises:
            ValueError: If jaw positions are invalid (left > right or bottom > top)
            RuntimeError: If any jaw position is None

        Example:
            >>> cp = ControlPoint(
            ...     left_jaw_position=-50,
            ...     right_jaw_position=50,
            ...     bottom_jaw_position=-40,
            ...     top_jaw_position=40
            ... )
            >>> cp.get_field_size()
            (100.0, 80.0)
        """
        if self.left_jaw_position is None or self.right_jaw_position is None or self.bottom_jaw_position is None or self.top_jaw_position is None:
            raise RuntimeError("Cannot calculate field size: one or more jaw positions are None")

        if self.left_jaw_position > self.right_jaw_position:
            raise ValueError(f"Invalid jaw positions: left ({self.left_jaw_position}) > right ({self.right_jaw_position})")

        if self.bottom_jaw_position > self.top_jaw_position:
            raise ValueError(f"Invalid jaw positions: bottom ({self.bottom_jaw_position}) > top ({self.top_jaw_position})")

        return (
            self.right_jaw_position - self.left_jaw_position,
            self.top_jaw_position - self.bottom_jaw_position,
        )

    def _set_mlc_leaf_positions(self, value: "np.ndarray[np.float32, np.dtype[np.float32]] | dict[str, Any] | MLCLeafPositions | None") -> None:
        """
        Internal method to set MLC leaf positions with proper type handling.

        Args:
            value: The MLC leaf positions. Can be:
                - MLCLeafPositions instance
                - numpy array of shape (n_leaves, 2)
                - dict with MLC parameters

        Raises:
            ValueError: If the MLC positions array has invalid shape
            TypeError: If the input data is not a supported type
        """
        from pinnacle_io.models.mlc import MLCLeafPositions

        if value is None:
            self._mlc_leaf_positions = None
        elif isinstance(value, MLCLeafPositions):
            self._mlc_leaf_positions = value
        elif isinstance(value, dict):
            self._mlc_leaf_positions = MLCLeafPositions(**dict[str, Any](value))
        else:
            # Assume it's a numpy array or compatible sequence
            value_dict: dict[str, Any] = dict(points=value)
            self._mlc_leaf_positions = MLCLeafPositions(**value_dict)

    @mlc_leaf_positions.setter
    def mlc_leaf_positions(self, value: np.ndarray[np.float32, np.dtype[np.float32]] | None) -> None:
        """
        Set the MLC leaf positions from a numpy array.

        Args:
            value: The MLC leaf positions as a numpy array of shape (n_leaves, 2).
                Each row should contain [left_leaf, right_leaf] positions in mm.

        Raises:
            ValueError: If the MLC positions array has invalid shape
            TypeError: If the input is not a numpy array or None
        """
        self._set_mlc_leaf_positions(value)
