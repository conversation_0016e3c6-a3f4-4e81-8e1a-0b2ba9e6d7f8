from .base_reader_service import BaseReaderService
from typing import Any, IO
import tarfile
import io


class TarFileReader(BaseReaderService):
    """
    TarFileReader service for reading Pinnacle data from a tar archive.
    
    This service provides access to Pinnacle data stored within tar archives,
    including compressed formats (.tar.gz, .tgz). It uses Python's tarfile
    module for reading archived data files.
    
    The service supports pattern matching for file discovery within the archive,
    allowing flexible access to nested directory structures that may vary
    between different Pinnacle installations.
    
    Args:
        tar_path (str): Path to the tar archive file
        
    Example:
        reader = TarFileReader("/path/to/pinnacle/data.tar.gz")
        institution = reader.get_institution()
        
        # Works with different compression formats
        reader = TarFileReader("/path/to/data.tar")
        reader = TarFileReader("/path/to/data.tgz")
        
    Note:
        The tar file is opened in read-only mode and kept open for the lifetime
        of the reader service for efficient access to multiple files.
        Text files are read using latin1 encoding for compatibility.
    """

    def __init__(self, tar_path: str):
        self.tar_path = tar_path
        try:
            self._tarfile = tarfile.open(tar_path, "r")
        except (tarfile.ReadError, EOFError, OSError) as e:
            raise tarfile.ReadError(f"Cannot open tar archive {tar_path}: {e}") from e

    def _find_file(self, filepath: str, filename: str | None = None) -> tarfile.TarInfo:
        """
        Find a file in the tar archive based on filepath and filename.
        Args:
            filepath: Directory path or full file path within the tar archive.
            filename: Optional filename to append to filepath. If None, filepath is treated as full path.
        Returns:
            TarInfo object for the found file.
        Raises:
            FileNotFoundError: If the specified file does not exist in the tar archive.
        """
        # Build the full path to search for
        if filename is None:
            target_path = filepath
        else:
            target_path = f"{filepath}/{filename}" if filepath else filename
        
        # First try exact match
        try:
            return self._tarfile.getmember(target_path)
        except KeyError:
            # If exact match fails, search for members that match the pattern
            matching_members: list[tarfile.TarInfo] = []
            for member in self._tarfile.getmembers():
                if filename is None:
                    # Looking for exact filepath match
                    if member.name == filepath:
                        matching_members.append(member)
                else:
                    # Looking for members that start with filepath and end with filename
                    if filepath:
                        if member.name.startswith(f"{filepath}/") and member.name.endswith(filename):
                            matching_members.append(member)
                    else:
                        if member.name.endswith(filename):
                            matching_members.append(member)
            
            if not matching_members:
                display_path = f"{filepath}/{filename}" if filename else filepath
                raise FileNotFoundError(f"File {display_path} not found in tar archive {self.tar_path}")

            if len(matching_members) > 1:
                display_path = f"{filepath}/{filename}" if filename else filepath
                raise FileNotFoundError(f"Multiple files match {display_path} in tar archive {self.tar_path}")
            
            # Use the first matching member
            return matching_members[0]

    def open_file(self, filepath: str, filename: str | None = None, mode: str = "r") -> IO[Any]:
        """
        Open a file from the tar archive.
        Args:
            filepath: Directory path or full file path within the tar archive.
            filename: Optional filename to append to filepath. If None, filepath is treated as full path.
            mode: File mode ('r' for text, 'rb' for binary).
        Returns:
            A file-like object.
        Raises:
            FileNotFoundError: If the specified file does not exist in the tar archive.
            ValueError: If the file mode is invalid for tar file reading.
        """
        # Validate file mode - tar files only support read modes
        valid_modes = {'r', 'rb'}
        if mode not in valid_modes:
            raise ValueError(f"Invalid mode '{mode}' for tar file reading. Supported modes: {', '.join(sorted(valid_modes))}")
        
        tarinfo = self._find_file(filepath, filename)
        
        fileobj = self._tarfile.extractfile(tarinfo)
        if fileobj is None:
            display_path = f"{filepath}/{filename}" if filename else filepath
            raise FileNotFoundError(f"File {display_path} not found in tar archive {self.tar_path}")
        if "b" in mode:
            return fileobj
        else:
            return io.TextIOWrapper(fileobj, encoding="latin1")

    def exists(self, filepath: str, filename: str | None = None) -> bool:
        """
        Check if a file exists in the tar archive.
        Args:
            filepath: Directory path or full file path within the tar archive.
            filename: Optional filename to append to filepath. If None, filepath is treated as full path.
        Returns:
            True if the file exists, False otherwise.
        """
        try:
            self._find_file(filepath, filename)
            return True
        except FileNotFoundError:
            return False
