from .base_reader_service import BaseReaderService
from typing import Any, IO


class FileReader(BaseReaderService):
    """
    FileReader service for reading Pinnacle data from a directory on the filesystem.

    This service provides access to Pinnacle data stored in a traditional directory
    structure on the local filesystem. It uses Python's built-in file operations
    for reading data files.

    The service operates relative to a root directory path and can access files
    using either separate filepath/filename parameters or full relative paths.

    Args:
        root_path (str): Path to the root directory containing Pinnacle data

    Example:
        reader = FileReader("/path/to/pinnacle/data")
        institution = reader.get_institution()

        # Access nested files
        patient = reader.get_patient()

    Note:
        All file paths are resolved relative to the root_path for security.
        The service inherits all get_* methods from BaseReaderService.
    """

    def __init__(self, root_path: str):
        self.root_path = root_path

    def _build_path(self, filepath: str, filename: str | None = None) -> str:
        """
        Build the full path to a file based on filepath and filename.
        Args:
            filepath: Directory path or full file path within the root directory.
            filename: Optional filename to append to filepath. If None, filepath is treated as full path.
        Returns:
            The full filesystem path to the file.
        Raises:
            ValueError: If the filepath or filename contains invalid characters.
        """
        import os

        # Check for invalid characters in filepath and filename
        if '\x00' in filepath:
            raise ValueError("Filepath contains null bytes")
        if filename is not None and '\x00' in filename:
            raise ValueError("Filename contains null bytes")

        if filename is None:
            return (
                os.path.join(self.root_path, filepath) if filepath else self.root_path
            )
        else:
            return os.path.join(self.root_path, filepath, filename)

    def open_file(
        self, filepath: str, filename: str | None = None, mode: str = "r", **kwargs: Any
    ) -> IO[Any]:
        """
        Open a file from the filesystem relative to the root_path.
        Args:
            filepath: Directory path or full file path within the root directory.
            filename: Optional filename to append to filepath. If None, filepath is treated as full path.
            mode: File mode (e.g., 'r', 'rb').
        Returns:
            A file-like object.
        Raises:
            FileNotFoundError: If the specified file does not exist in the filesystem.
        """
        import os

        full_path = self._build_path(filepath, filename)

        if not os.path.exists(full_path):
            display_path = os.path.join(filepath, filename) if filename else filepath
            raise FileNotFoundError(
                f"File {display_path} not found in directory {self.root_path}"
            )
        return open(full_path, mode, **kwargs)

    def exists(self, filepath: str, filename: str | None = None) -> bool:
        """
        Check if a file exists in the filesystem relative to the root_path.
        Args:
            filepath: Directory path or full file path within the root directory.
            filename: Optional filename to append to filepath. If None, filepath is treated as full path.
        Returns:
            True if the file exists, False otherwise.
        """
        import os

        full_path = self._build_path(filepath, filename)
        return os.path.exists(full_path)
