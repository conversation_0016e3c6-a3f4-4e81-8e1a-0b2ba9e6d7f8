"""
Reader for Pinnacle plan.Trail.binary.### files.
"""

from pinnacle_io.models import Dose, DoseGrid, Trial, Beam, Point
import numpy as np
import numpy.typing as npt
from typing import Any, IO
import os
import re
from pinnacle_io.readers.base_pinnacle_reader import BasePinnacleReader


class DoseReader(BasePinnacleReader):
    """
    Reader for Pinnacle plan.Trail.binary.### files.
    Now supports any file service with an open_file(filename, mode) method.
    """

    @staticmethod
    def read_from_ids(
        institution_id: int,
        patient_id: int,
        plan_id: int,
        trial: Trial,
        mount_id: int = 0,
        beam: Beam | None = None,
        points: list[Point] | None = None,
        file_service: Any = None,
    ) -> Dose:
        """
        Read Pinnacle plan.Trail.binary.### file(s) using ID-based loading and create a Dose model for the given trial.

        Args:
            institution_id: Institution ID number
            patient_id: Patient ID number
            plan_id: Plan ID number (e.g., 0 for Plan_0)
            trial: Trial model to use for the dose
            mount_id: Mount ID number (default: 0)
            beam: Optional specific beam (if None, reads all beams for trial dose)
            points: Optional list of Point objects (if not provided, will be read)
            file_service: File service object with open_file method

        Returns:
            Dose model populated with data from the files. Pixel data is the total trial dose or beam dose.

        Usage:
            dose = DoseReader.read_from_ids(1, 1, 0, trial, file_service=file_service)
            dose = DoseReader.read_from_ids(1, 1, 0, trial, 0, beam, points, file_service=file_service)
        """
        # Construct standard Pinnacle path format
        plan_path = f"Institution_{institution_id}/Mount_{mount_id}/Patient_{patient_id}/Plan_{plan_id}"
        # Delegate to path-based method
        return DoseReader.read_from_path(plan_path, trial, beam, points, file_service)

    @staticmethod
    def read_from_path(
        plan_path: str,
        trial: Trial,
        beam: Beam | None = None,
        points: list[Point] | None = None,
        file_service: Any = None,
    ) -> Dose:
        """
        Read Pinnacle plan.Trail.binary.### file(s) using path-based loading and create a Dose model for the given trial.

        Args:
            plan_path: Path to the plan directory or plan.Trial file
            trial: Trial model to use for the dose
            beam: Optional specific beam (if None, reads all beams for trial dose)
            points: Optional list of Point objects (if not provided, will be read)
            file_service: File service object with open_file method

        Returns:
            Dose model populated with data from the files. Pixel data is the total trial dose or beam dose.

        Usage:
            dose = DoseReader.read_from_path("/path/to/Patient_0/Plan_0", trial)
            dose = DoseReader.read_from_path("/path/to/Patient_0/Plan_0", trial, beam, points, file_service=file_service)
        """
        # The trial model is required and needs to have beams and a dose grid included
        if not trial.beam_list:
            raise ValueError("Trial has no beams")

        dose_grid = trial.dose_grid
        if dose_grid is None:
            raise ValueError("Trial has no dose grid")

        # Read and return a single beam dose object if the plan_path specifies a dose file or a beam is provided
        if re.match(r"plan\.Trial\.binary\.\d+$", plan_path) or beam is not None:
            if beam is None:
                for beam in trial.beam_list:
                    if (
                        beam.dose_volume_file == plan_path
                        or beam.dose_var_volume_file == plan_path
                    ):
                        break

            if beam is None:
                raise ValueError(
                    f"Beam not found in trial '{trial.name}' for dose file '{plan_path}'"
                )

            return DoseReader.read_beam_dose(
                plan_path, beam, trial.dose_grid, points, file_service
            )

        # Now assume were reading all beam doses and computing/returning a trial dose object
        return DoseReader.read_trial_dose(plan_path, trial, points, file_service)

    @staticmethod
    def read(
        plan_path: str,
        trial: Trial,
        beam: Beam | None = None,
        points: list[Point] | None = None,
        file_service: Any = None,
    ) -> Dose:
        """
        Legacy method for backward compatibility.
        
        Deprecated: Use read_from_path() or read_from_ids() instead.
        """
        return DoseReader.read_from_path(plan_path, trial, beam, points, file_service)

    @staticmethod
    def read_trial_dose(
        plan_path: str,
        trial: Trial,
        points: "list[Point] | None" = None,
        file_service: Any = None,
    ) -> Dose:
        """
        Read Pinnacle plan.Trail.binary.### files and create a Dose model for the given trial.
        This method saves the dose for each beam as well.

        Args:
            plan_path: Path to the plan directory or plan.Trial file
            trial: Trial model to use for the dose
            points: Optional list of Point objects (if not provided, will be read)
            file_service: File service object with open_file method

        Returns:
            Trial dose model populated with data from the file. Pixel data is the total trial dose.

        Usage:
            dose = DoseReader.read("/path/to/Patient_0/Plan_0/plan.Trial", trial)
            dose = DoseReader.read("/path/to/Patient_0/Plan_0", trial)
            dose = DoseReader.read("/path/to/Patient_0/Plan_0", trial, points)
            dose = DoseReader.read("/path/to/Patient_0/Plan_0", trial, points, file_service=file_service)
        """
        # Extract the file path
        if plan_path.endswith("plan.Trial"):
            file_path = os.path.dirname(plan_path)
        else:
            file_path = plan_path

        if not trial.beam_list:
            raise ValueError("Trial has no beams")

        dose_grid = trial.dose_grid
        if dose_grid is None:
            raise ValueError("Trial has no dose grid")

        # Read points if not provided
        if points is None:
            from pinnacle_io.readers.point_reader import PointReader

            points = PointReader.read_from_path(file_path, file_service)

        dose_dimension = dose_grid.dimension
        if (
            dose_dimension is None
            or dose_dimension.z is None
            or dose_dimension.y is None
            or dose_dimension.x is None
        ):
            raise ValueError("Dose grid has no dimension")

        trial_dose = Dose.create_dose_object(
            dose_type="PHYSICAL",
            dose_unit="CGY",
            dose_summation_type="PLAN",
            dose_grid=dose_grid,
            dose_comment=trial.name or "Unknown Trial",
            pixel_data=np.zeros(
                (dose_dimension.z, dose_dimension.y, dose_dimension.x), dtype=np.float32
            ),
            trial=trial,
        )
        for beam in trial.beam_list:
            prescription = next(
                (
                    p
                    for p in trial.prescription_list
                    if p.name == beam.prescription_name
                ),
                None,
            )
            if prescription is None:
                raise ValueError(f"Prescription not found for beam '{beam.name}'")
            beam_dose = DoseReader.read_beam_dose(
                file_path, beam, trial.dose_grid, points, file_service
            )
            if beam_dose.pixel_data is None:
                raise ValueError(f"Beam '{beam.name}' dose has no pixel data")
            beam_dose.pixel_data = beam_dose.pixel_data * prescription.number_of_fractions  # type: ignore
            beam.dose = beam_dose
            trial_dose.pixel_data += beam_dose.pixel_data  # type: ignore

        # Link the dose to the trial. Then return the trial dose.
        trial.dose = trial_dose
        return trial_dose

    @staticmethod
    def read_beam_dose(
        plan_path: str,
        beam: Beam,
        dose_grid: DoseGrid,
        points: "list[Point] | None" = None,
        file_service: Any = None,
    ) -> Dose:
        """
        Read a beam dose from a Pinnacle binary dose file.

        Args:
            plan_path: Path to the plan directory or plan.Trial file
            beam: Beam model to use for the dose
            dose_grid: DoseGrid model to use for the dose
            points: Optional list of Point objects (if not provided, will be read)
            file_service: File service object with open_file method

        Returns:
            Dose model populated with data from the file. Pixel data is the dose per fraction.
        """
        # Check for monitor unit info
        mu_info = beam.monitor_unit_info
        if mu_info is None:
            raise ValueError(f"Beam '{beam.name}' has no monitor unit info")

        # Extract the file path
        if plan_path.endswith("plan.Trial"):
            file_path = os.path.dirname(plan_path)
        else:
            file_path = plan_path

        # Read points if not provided
        if points is None:
            from pinnacle_io.readers.point_reader import PointReader

            points = PointReader.read_from_path(file_path, file_service)

        # Use base class file service handling for binary dose reading
        service = DoseReader._get_file_service(file_service, file_path)
        
        if not service.exists(file_path, beam.dose_volume_file):
            raise FileNotFoundError(f"Dose volume file not found: {file_path}/{beam.dose_volume_file}")

        with service.open_file(file_path, beam.dose_volume_file, "rb") as f:
            dose_per_fx_per_mu = DoseReader.read_binary_dose(f, dose_grid)

        beam_dose = Dose.create_dose_object(
            dose_type="PHYSICAL",
            dose_unit="CGY",
            dose_summation_type="BEAM",
            dose_grid=dose_grid,
            dose_comment=beam.name or "Unknown Beam",
            pixel_data=dose_per_fx_per_mu,
            beam=beam,
        )

        # Get the reference point and corresponding dose to compute beam monitor units
        reference_point = next(
            (
                p
                for p in points
                if getattr(p, "name", None) == beam.prescription_point_name
            ),
            None,
        )
        if reference_point is None:
            raise ValueError(f"Reference point not found for beam '{beam.name}'")

        reference_dose = beam_dose.get_dose_value_at_point(*reference_point.coordinates)  # type: ignore
        if reference_dose is None:
            raise ValueError(f"Reference dose not found for beam '{beam.name}'")

        monitor_units = mu_info.prescription_dose / reference_dose  # type: ignore

        # Scale the dose by the monitor units
        dose_per_fx = dose_per_fx_per_mu * monitor_units  # type: ignore
        beam_dose.pixel_data = dose_per_fx  # type: ignore

        return beam_dose

    @staticmethod
    def read_binary_dose(
        file_obj: IO[bytes],
        dose_grid: DoseGrid | None = None,
    ) -> npt.NDArray[np.float32]:
        """
        Read and parse a Pinnacle binary dose file from a file-like object.

        Args:
            file_obj: File-like object opened in binary mode
            dose_grid: A DoseGrid model object containing the dimensions of the dose volume.

        Returns:
            Numpy array of raw, unscaled dose data (i.e., dose per monitor unit per fraction).
            If the dose grid is provided, the dose data is reshaped to the correct dimensions and flipped along the Y axis.
        """
        binary_data = file_obj.read()
        data_type = ">f4"
        dose_volume = np.frombuffer(binary_data, dtype=data_type)
        if (
            dose_grid
            and dose_grid.dimension
            and dose_grid.dimension.z is not None
            and dose_grid.dimension.y is not None
            and dose_grid.dimension.x is not None
        ):
            z_dim = int(dose_grid.dimension.z)
            y_dim = int(dose_grid.dimension.y)
            x_dim = int(dose_grid.dimension.x)
            dose_volume = dose_volume.reshape((z_dim, y_dim, x_dim))

            # Always flip along the Y axis
            dose_volume = np.flip(dose_volume, axis=1)  # flip y
        return dose_volume
