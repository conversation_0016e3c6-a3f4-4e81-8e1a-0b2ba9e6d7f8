"""
Reader for Pinnacle plan.roi files.
"""

import numpy as np
from typing import List, Any
from pinnacle_io.models import RO<PERSON>, Curve
from pinnacle_io.readers.pinnacle_file_reader import PinnacleFileReader
from pinnacle_io.readers.base_pinnacle_reader import BasePinnacleReader


class ROIReader(BasePinnacleReader):
    """
    Reader for Pinnacle plan.roi files.
    """

    @staticmethod
    def read_from_ids(
        institution_id: int, 
        patient_id: int, 
        plan_id: int, 
        mount_id: int = 0, 
        file_service: Any = None
    ) -> List[ROI]:
        """
        Read a Pinnacle plan.roi file using institution, patient, and plan IDs.

        Args:
            institution_id: Institution ID number
            patient_id: Patient ID number
            plan_id: Plan ID number (e.g., 0 for Plan_0)
            mount_id: Mount ID number (default: 0)
            file_service: File service object with open_file method

        Returns:
            List of ROI models populated with data from the file

        Usage:
            rois = ROIReader.read_from_ids(1, 5, 0)
            rois = ROIReader.read_from_ids(1, 5, 0, mount_id=2)
            rois = ROIReader.read_from_ids(1, 5, 0, file_service=file_service)
        """
        # Construct standard Pinnacle path format
        plan_path = f"Institution_{institution_id}/Mount_{mount_id}/Patient_{patient_id}/Plan_{plan_id}"
        
        # Delegate to path-based method
        return ROIReader.read_from_path(plan_path, file_service)


    @staticmethod
    def read_from_path(plan_path: str, file_service: Any = None) -> List[ROI]:
        """
        Read a Pinnacle plan.roi file and create a list of ROI models.

        Args:
            plan_path: Path to the patient's plan directory or plan.roi file
            file_service: File service object with open_file method

        Returns:
            List of ROI models populated with data from the file
        
        Usage:
            rois = ROIReader.read_from_path("/path/to/Patient_0/Plan_0/plan.roi")
            rois = ROIReader.read_from_path("/path/to/Patient_0/Plan_0")
            rois = ROIReader.read_from_path("/path/to/Patient_0/Plan_0", file_service=file_service)
        """
        # Resolve file path and name
        file_path, file_name = ROIReader._resolve_file_path(plan_path, "plan.roi")
        
        # Read file content using base class utility
        content_lines = ROIReader._read_file_lines(file_path, file_name, file_service)
        
        return ROIReader.parse(content_lines)

    @staticmethod
    def parse(content_lines: list[str]) -> List[ROI]:
        """
        Parse lines from a Pinnacle plan.roi file into a list of ROI models.

        Args:
            content_lines: List of lines from the ROI file.

        Returns:
            List of ROI models populated with data from the content.
        """
        # Instead of evaluating the entire file line by line,
        #
        lines = [line.strip() for line in content_lines]
        beginning_of_rois = [i for i in range(len(lines)) if lines[i] == "roi={"]
        beginning_of_curves = [i for i in range(len(lines)) if lines[i] == "curve={"]

        rois: list[ROI] = []
        i_roi = 0
        i_curve = 0
        while i_roi < len(beginning_of_rois) and i_curve < len(beginning_of_curves):
            beginning_of_roi = beginning_of_rois[i_roi]
            beginning_of_curve = beginning_of_curves[i_curve]
            roi_lines = lines[beginning_of_roi + 1 : beginning_of_curve]
            roi_data = PinnacleFileReader.parse(roi_lines)
            roi_data["roi_number"] = i_roi + 1
            roi = ROI(**roi_data)

            curve_number = 0
            while curve_number < roi_data["num_curve"]:
                beginning_of_curve = beginning_of_curves[i_curve]
                curve_lines = lines[beginning_of_curve + 1 : beginning_of_curve + 4]
                curve_data = PinnacleFileReader.parse(curve_lines)
                curve_data["curve_number"] = curve_number

                beginning_of_points = beginning_of_curve + 5
                point_lines = lines[beginning_of_points : beginning_of_points + curve_data["num_points"]]
                curve_data["points"] = np.array([list(map(float, line.split())) for line in point_lines])
                roi.curve_list.append(Curve(**curve_data))

                curve_number += 1
                i_curve += 1

            rois.append(roi)
            i_roi += 1

        return rois
