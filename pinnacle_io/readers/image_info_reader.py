"""
Reader for Pinnacle ImageInfo files (extracted from ImageSetReader).
"""

import os
import re
from typing import Any
from pinnacle_io.models import ImageInfo
from pinnacle_io.readers.pinnacle_file_reader import PinnacleFileReader
from pinnacle_io.readers.base_pinnacle_reader import BasePinnacleReader


class ImageInfoReader(BasePinnacleReader):
    """
    Reader for Pinnacle ImageSet_#.ImageInfo files.
    """

    @staticmethod
    def read_from_ids(
        institution_id: int, 
        patient_id: int, 
        image_set_id: int, 
        mount_id: int = 0, 
        file_service: Any = None
    ) -> list[ImageInfo]:
        """
        Read a Pinnacle ImageInfo file using institution, patient, and image set IDs.

        Args:
            institution_id: Institution ID number
            patient_id: Patient ID number
            image_set_id: ImageSet ID number (e.g., 0 for ImageSet_0)
            mount_id: Mount ID number (default: 0)
            file_service: File service object with open_file method

        Returns:
            List of ImageInfo models populated with data from the file

        Usage:
            image_info_list = ImageInfoReader.read_from_ids(1, 5, 0)
            image_info_list = ImageInfoReader.read_from_ids(1, 5, 0, mount_id=2)
            image_info_list = ImageInfoReader.read_from_ids(1, 5, 0, file_service=file_service)
        """
        # Construct standard Pinnacle path format
        image_info_path = f"Institution_{institution_id}/Mount_{mount_id}/Patient_{patient_id}"
        
        # Delegate to path-based method with image_index
        return ImageInfoReader.read_from_path(image_info_path, image_index=image_set_id, file_service=file_service)

    @staticmethod
    def read_from_path(image_info_path: str, image_index: int | None = None, file_service: Any = None) -> list[ImageInfo]:
        """
        Read a Pinnacle ImageInfo file from a specified path.

        Args:
            image_info_path: Path to ImageSet_# file or directory (the .ImageInfo extension is optional)
            image_index: Optional index of the image to read, e.g., 0 for ImageSet_0.ImageInfo
            file_service: File service object with open_file method

        Returns:
            List of ImageInfo models populated with data from the file

        Usage:
            image_info_list = ImageInfoReader.read_from_path("/path/to/Patient_0/ImageSet_0.ImageInfo")
            image_info_list = ImageInfoReader.read_from_path("/path/to/Patient_0/ImageSet_0")
            image_info_list = ImageInfoReader.read_from_path("/path/to/Patient_0", image_index=0)
        """
        # Handle special ImageInfo path resolution logic
        if image_info_path.endswith(".ImageInfo"):
            file_path, file_name = os.path.split(image_info_path)
        elif re.match(r".*ImageSet_\d+$", os.path.basename(image_info_path)):
            file_path, file_name = os.path.split(image_info_path)
            file_name += ".ImageInfo"
        else:
            if image_index is None:
                raise ValueError("The image index must either be specified in the image_header_path or in the image_index argument")
            file_path, file_name = image_info_path, f"ImageSet_{image_index}.ImageInfo"

        # Read file content using base class utility
        content_lines = ImageInfoReader._read_file_lines(file_path, file_name, file_service)
        
        return ImageInfoReader.parse(content_lines)

    @staticmethod
    def parse(content_lines: list[str]) -> list[ImageInfo]:
        """
        Parse a Pinnacle ImageSet info content string and create a list of ImageInfo models.

        Args:
            content_lines: Pinnacle ImageSet info content lines

        Returns:
            List of ImageInfo models populated with data from the content
        """
        data = PinnacleFileReader.parse(content_lines)
        image_info_list = [ImageInfo(**image_info) for image_info in data.get("ImageInfoList", {})]
        return image_info_list
