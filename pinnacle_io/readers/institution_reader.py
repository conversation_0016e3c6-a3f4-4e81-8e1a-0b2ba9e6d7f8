"""
Reader for Pinnacle Institution files.
"""

from typing import Any
from pinnacle_io.models import Institution
from pinnacle_io.readers.pinnacle_file_reader import PinnacleFileReader
from pinnacle_io.readers.base_pinnacle_reader import BasePinnacleReader


class InstitutionReader(BasePinnacleReader):
    """
    Reader for Pinnacle Institution files.
    """

    @staticmethod
    def read_from_ids(institution_id: int, file_service: Any = None) -> Institution:
        """
        Read a Pinnacle Institution file using ID-based loading and create an Institution model.

        Note: Institution files are located at the root level, so this method is primarily
        for API consistency. The institution_id parameter is not used for path construction
        since Institution files are always at the root.

        Args:
            institution_id: Institution ID number (not used for path construction)
            file_service: File service object with open_file method

        Returns:
            Institution model populated with data from the files

        Usage:
            institution = InstitutionReader.read_from_ids(1, file_service=file_service)
        """
        # Institution files are at the root level, not in Institution_# directories
        # The institution_id is not used for path construction
        return InstitutionReader.read_from_path("", file_service)

    @staticmethod
    def read_from_path(institution_path: str | None = None, file_service: Any = None) -> Institution:
        """
        Read a Pinnacle Institution file and create an Institution model.

        Args:
            institution_path: Path to the Institution file or directory
            file_service: File service object with open_file method

        Returns:
            Institution model populated with data from the files

        Usage:
            institution = InstitutionReader.read_from_path("/path/to/Institution_0/Institution")
            institution = InstitutionReader.read_from_path("/path/to/Institution_0")
            institution = InstitutionReader.read_from_path("/path/to/Institution_0", file_service=file_service)
        """
        # Handle special case for empty or "Institution" paths
        institution_path = institution_path or ""
        if institution_path == "Institution":
            file_path, file_name = ".", "Institution"
        else:
            file_path, file_name = InstitutionReader._resolve_file_path(institution_path, "Institution")

        # Read file content using base class utility
        content_lines = InstitutionReader._read_file_lines(file_path, file_name, file_service)

        return InstitutionReader.parse(content_lines)

    @staticmethod
    def read(institution_path: str | None = None, file_service: Any = None) -> Institution:
        """
        Read a Pinnacle Institution file and create an Institution model.

        DEPRECATED: Use read_from_path() instead. This method is kept for backward compatibility.

        Args:
            institution_path: Path to the Institution file or directory
            file_service: File service object with open_file method

        Returns:
            Institution model populated with data from the files
        """
        return InstitutionReader.read_from_path(institution_path, file_service)

    @staticmethod
    def parse(content_lines: list[str]) -> Institution:
        """
        Parse a Pinnacle Institution content string and create an Institution model.

        Args:
            content_lines: Pinnacle Institution content lines

        Returns:
            Institution model populated with data from the content
        """
        data = PinnacleFileReader.parse(content_lines)
        institution = Institution(**data)
        return institution
