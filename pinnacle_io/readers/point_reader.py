"""
Reader for Pinnacle plan.Points files.
"""

from typing import List, Any
from pinnacle_io.models import Point
from pinnacle_io.readers.pinnacle_file_reader import PinnacleFileReader
from pinnacle_io.readers.base_pinnacle_reader import BasePinnacleReader


class PointReader(BasePinnacleReader):
    """
    Reader for Pinnacle plan.Points files.
    """

    @staticmethod
    def read_from_ids(
        institution_id: int, patient_id: int, plan_id: int, mount_id: int = 0, file_service: Any = None
    ) -> List[Point]:
        """
        Read a Pinnacle plan.Points file using institution, patient, and plan IDs.

        Args:
            institution_id: Institution ID number
            patient_id: Patient ID number
            plan_id: Plan ID number (e.g., 0 for Plan_0)
            mount_id: Mount ID number (default: 0)
            file_service: File service object with open_file method

        Returns:
            List of Point models populated with data from the file

        Usage:
            points = PointReader.read_from_ids(1, 5, 0)
            points = PointReader.read_from_ids(1, 5, 0, mount_id=2)
            points = PointReader.read_from_ids(1, 5, 0, file_service=file_service)
        """
        # Construct standard Pinnacle path format
        plan_path = f"Institution_{institution_id}/Mount_{mount_id}/Patient_{patient_id}/Plan_{plan_id}"

        # Delegate to path-based method
        return PointReader.read_from_path(plan_path, file_service)

    @staticmethod
    def read_from_path(plan_path: str, file_service: Any = None) -> List[Point]:
        """
        Read a Pinnacle plan.Points file and create a list of Point models.

        Args:
            plan_path: Path to the patient's plan directory or plan.Points file
            file_service: File service object with open_file method

        Returns:
            List of Point models populated with data from the file

        Usage:
            points = PointReader.read_from_path("/path/to/Patient_0/Plan_0/plan.Points")
            points = PointReader.read_from_path("/path/to/Patient_0/Plan_0")
            points = PointReader.read_from_path("/path/to/Patient_0/Plan_0", file_service=file_service)
        """
        # Resolve file path and name
        file_path, file_name = PointReader._resolve_file_path(plan_path, "plan.Points")

        # Read file content using base class utility
        content_lines = PointReader._read_file_lines(file_path, file_name, file_service)

        return PointReader.parse(content_lines)

    @staticmethod
    def parse(content_lines: list[str]) -> List[Point]:
        """
        Parse a Pinnacle plan.Points file content and create a list of Point models.

        Args:
            content_lines: Lines from a Pinnacle plan.Points file

        Returns:
            List of Point models populated with data from the content
        """
        data = PinnacleFileReader.parse(content_lines)
        return [Point(**point) for point in data["PoiList"]]
