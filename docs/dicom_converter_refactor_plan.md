# DicomConverterApp Refactor Plan

## Overview

This document outlines a comprehensive plan to refactor the `DicomConverterApp` in the SimpleDicomConverter project. The goal is to modernize the architecture, improve maintainability, enable batch and model-based workflows, and ensure robust UID management across all DICOM outputs.

---

## Motivation

- **Current State**: The `DicomConverterApp` uses outdated patterns, direct file access, and lacks flexibility for new workflows (e.g., model-based, batch, UI-driven).
- **Target State**: A class-based, modular, and extensible system that supports loading from TAR/ZIP/directory, batch conversion, and robust UID sharing across all DICOM files.

---

## Refactor Steps

### Phase 1: Core Architecture Changes

#### 1.1 Update DicomConverterApp Constructor and Class Structure **[Complete]**
- Remove constructor parameters (`input_dir`, `output_dir`, `patient_folder`).
- Add class methods for loading from TAR, ZIP, or directory.
- Integrate file service logic (see `MainController`).
- Add a `DicomUidManager` class for UID sharing.

#### 1.2 Create DicomUidManager Class **[Complete]**
- Centralizes UID management for CT, RTSTRUCT, RTPLAN, RTDOSE.
- Generates and stores:
  - `study_instance_uid`
  - `series_instance_uid` (per modality)
  - `frame_of_reference_uid`
- Provides methods to get/set UIDs for each modality and instance.
- Ensures all DICOM files in a study share the correct UIDs.
- **Comprehensive pytest unit tests were created for DicomUidManager and are all passing.**

#### 1.3 Add File Service Integration **[Complete]**
- Use `TarFileService`, `ZipFileService`, `DirectoryFileService` for input abstraction.
- Add methods to load data from these services.
- Abstract Pinnacle file reading for all input types.

---

### Phase 2: Data Loading Methods

#### 2.1 Create a Single Common Loading Method
- Implement a single method (e.g., `load_data`) on DicomConverterApp that uses `self.file_service` (already set by the loader methods) to extract all patients, plans, and trials.
- This method should work regardless of whether the source is a TAR, ZIP, or directory, thanks to the file_service abstraction.
- This approach follows the pattern in `ui/main_view.py`, which uses the same logic for all source types.
- Do **not** create separate methods for each source type.

#### 2.2 Implement Common Loading Logic
- Use `pinnacle_io` readers (`InstitutionReader`, `PatientReader`, etc.) to parse data from the file service.
- Store loaded data in memory for processing.

---

### Phase 3: Static Conversion Method

#### 3.1 Create Static Convert Method
```python
@staticmethod
def convert(
    patient: Patient,
    image_set: ImageSet, 
    trial: Trial,
    dose: Dose,
    roi_list: List[ROI],
    poi_list: List[Point],
    output_path: str,
    uid_manager: DicomUidManager
) -> Dict[str, str]
```

#### 3.2 Implement Conversion Workflow
1. **CT Images**: Convert using `ImageConverter`.
2. **RT Structure Set**: Convert using `StructureConverter`.
3. **RT Plan**: Convert using `PlanConverter`.
4. **RT Dose**: Convert using `DoseConverter`.
5. **UID Coordination**: Use `DicomUidManager` for all UIDs.

---

### Phase 4: Batch Processing

#### 4.1 Create Batch Conversion Method
```python
def convert_all(self, output_path: str) -> Dict[str, List[str]]
```
- Process all patients, plans, and trials.
- Call the static `convert` method for each combination.
- Return mapping of patient/plan/trial to output files.

#### 4.2 Add Progress Tracking
- Integrate with progress modal system (UI).
- Provide callbacks for progress updates.
- Handle errors gracefully and continue processing.

---

### Phase 5: Integration with Existing UI

#### 5.1 Update Main View Integration
- Modify `_on_export_dicom` in `MainView` to use new `DicomConverterApp`.
- Pass loaded data from controller service to converter.
- Maintain existing UI workflow.

#### 5.2 Update Command Line Interface
- Keep existing CLI functionality for backward compatibility.
- Add new CLI options for batch processing.
- Support both old and new workflows.

---

### Phase 6: UID Management Implementation

#### 6.1 DicomUidManager Class Design
```python
class DicomUidManager:
    def __init__(self, study_instance_uid: str = None):
        self.study_instance_uid = study_instance_uid or generate_uid()
        self.frame_of_reference_uid = generate_uid()
        self._series_uids = {}
        self._sop_instance_uids = {}
    
    def get_study_instance_uid(self) -> str
    def get_frame_of_reference_uid(self) -> str
    def get_series_instance_uid(self, modality: str) -> str
    def get_sop_instance_uid(self, modality: str, index: int = 0) -> str
```

#### 6.2 Integrate with Converters
- Modify base converter to accept `DicomUidManager`.
- Update all converters to use shared UIDs.
- Ensure proper DICOM references between files.

---

### Phase 7: Error Handling and Validation

#### 7.1 Add Comprehensive Error Handling
- Validate input data before conversion.
- Handle missing or corrupted files gracefully.
- Provide detailed error messages and logging.

#### 7.2 Add Data Validation
- Validate patient, plan, trial data integrity.
- Check for required fields and relationships.
- Ensure DICOM compliance.

---

### Phase 8: Testing and Documentation

#### 8.1 Update Tests
- Create tests for new `DicomConverterApp` methods.
- Test UID sharing functionality.
- Test batch processing capabilities.

#### 8.2 Update Documentation
- Document new API and usage patterns.
- Provide migration guide from old to new workflow.
- Update examples and tutorials.

---

## Implementation Priority

1. **High Priority**: Steps 1.1-1.3, 2.1-2.2, 6.1-6.2 (Core architecture and UID management)
2. **Medium Priority**: Steps 3.1-3.2, 4.1-4.2 (Conversion methods and batch processing)
3. **Low Priority**: Steps 5.1-5.2, 7.1-7.2, 8.1-8.2 (Integration, error handling, testing)

---

## Key Benefits

- **Cleaner Architecture**: Separation of concerns between loading, conversion, and UID management
- **Better UID Coordination**: Centralized UID management ensures proper DICOM file relationships
- **Flexible Input Sources**: Support for TAR, ZIP, and directory inputs
- **Reusable Components**: Static conversion method can be used independently
- **Batch Processing**: Efficient processing of multiple patients/plans/trials
- **Backward Compatibility**: Maintains existing CLI and UI workflows

---

## Questions/Next Steps

- Should the batch conversion always process all trials, or allow filtering?
- Should UID management be customizable (e.g., for research vs. clinical use)?
- What is the preferred error reporting mechanism for UI vs. CLI?

---

*This document should be referenced for all future work on the DicomConverterApp refactor.* 