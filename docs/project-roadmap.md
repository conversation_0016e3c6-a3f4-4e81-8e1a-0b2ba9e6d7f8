# createdcm.py - Refactoring Roadmap

## Introduction
This roadmap outlines a structured approach to refactoring the `createdcm.py` script to align with Python best practices and improve maintainability, testability, and extensibility. The current script, while functional, relies heavily on global variables, has minimal error handling, and lacks modular design.

## [ ] Phase 1: Initial Cleanup and Setup

### [X] Step 1.1: Project Structure
- Create a proper Python package structure 
- Set up configuration for development tools (linting, formatting)
- Implement logging instead of print statements
- Create basic automated testing framework 

SimpleDicomConverter/
├── dicom_converter/
│   ├── __init__.py
│   ├── config.yaml
│   ├── main.py
│   ├── converters/
│   │   ├── __init__.py
│   │   ├── base_converter.py
│   │   ├── image_converter.py
│   │   ├── structure_converter.py
│   │   ├── plan_converter.py
│   │   └── dose_converter.py
│   └── utils/
│       ├── __init__.py
│       ├── config_loader.py
│       ├── constants.py
│       └── settings.py
├── tests/
│   ├── converters/
│   └── utils/
├── docs/
│   ├── project-roadmap.md
│   └── project-summary.md
└── createdcm.py

### [X] Step 1.2: Configuration Management
- Create configuration file for configurable parameters
- Remove hardcoded constants and values (e.g., PDD values, MLC configurations, colors)
- Create settings module to handle configuration
- Implement a proper configuration file format (YAML or JSON)

### [✓] Step 1.3: Documentation Enhancement
- Add proper docstrings to all modules and functions 
- Implement type hints throughout the codebase 
- Create a detailed API reference
- Document coordinate system transformations
- Create data flow diagrams (deferred until later in the project)

#### Documentation Guidelines

##### Module Docstrings
Each module should have a docstring that follows this format:
```python
"""
Brief description of the module.

This module provides detailed description of the module's purpose and functionality.
"""
```

##### Class Docstrings
Each class should have a docstring that follows this format:
```python
class MyClass:
    """
    Brief description of the class.
    
    This class provides detailed description of the class's purpose and functionality.
    Additional details about the class can be included here.
    """
```

##### Method/Function Docstrings
Each method or function should have a docstring that follows this format:
```python
def my_function(param1: Type1, param2: Type2) -> ReturnType:
    """
    Brief description of the function.
    
    Detailed description of what the function does, special cases,
    exceptions raised, etc.
    
    Args:
        param1: Description of param1.
        param2: Description of param2.
        
    Returns:
        Description of the return value.
        
    Raises:
        ExceptionType: Description of when this exception is raised.
    """
```

##### Type Hints
All function parameters, return values, and variable declarations should include type hints:
```python
def process_data(data: List[Dict[str, Any]]) -> Dict[str, float]:
    result: Dict[str, float] = {}
    # Implementation...
    return result
```

##### Documentation Coverage Status
- IO Modules: ✓
  - base_reader.py: ✓
  - base_writer.py: ✓
  - pinnacle_file_reader.py: ✓
  - pinnacle_readers.py: ✓
  - dicom_writers.py: ✓
  - file_factory.py: ✓
- Model Modules: ✓
  - base_model.py: ✓
  - patient.py: ✓
  - image_set.py: ✓
  - roi.py: ✓
  - plan.py: ✓
  - beam.py: ✓
  - dose.py: ✓
  - institution.py: ✓
  - point.py: ✓
  - trial.py: ✓

## [ ] Phase 2: Architectural Improvements

### [X] Step 2.1: Class-based Design
- Create a `DicomConverter` base class
- Implement specific converter classes for each DICOM file type:
  - `ImageConverter` for CT images
  - `StructureConverter` for RT Structure Sets
  - `PlanConverter` for RT Plans
  - `DoseConverter` for RT Dose

### [X] Step 2.2: Data Models
- Create explicit data models for:
  - `Patient` - patient information
  - `ImageSet` - image metadata and pixel data
  - `ROI` - structure set information
  - `Plan` - treatment plan details
  - `Beam` - beam configuration
  - `ControlPoint` - beam control points
  - `Dose` - dose distribution data
  - `PatientPosition` - position-specific transformations

### [ ] Step 2.3: Eliminate Global Variables
- Replace global variables with class attributes
- Create a context object to share state between components
- Use dependency injection for component interaction
- Implement proper state management

### [ ] Step 2.4: Implement Command Pattern for Processing Pipeline
- Create a processing pipeline with discrete steps
- Allow steps to be added, removed, or reordered
- Implement validation between pipeline steps
- Add checkpoint capabilities to save intermediate state

## [ ] Phase 3: Core Functionality Refactoring

### [X] Step 3.1: File Input/Output Abstraction
- Create file reader classes for each Pinnacle file type 
- Implement file writers for DICOM output
- Add support for different input/output formats
- Create caching mechanisms for repeated file access 

The following Pinnacle reader classes have been implemented:
- `PinnacleFileReader`: Base class for reading hierarchical Pinnacle data files
- `InstitutionFileReader`: Reader for Pinnacle Institution files
- `PatientFileReader`: Reader for Pinnacle patient files
- `PlanFileReader`: Reader for Pinnacle Plan files
- `ImageHeaderFileReader`: Reader for Pinnacle ImageSet header files
- `ImageInfoFileReader`: Reader for Pinnacle ImageSet ImageInfo files
- `ImageFileReader`: Reader for Pinnacle image files
- `TrialFileReader`: Reader for Pinnacle Trial files
- `PointsFileReader`: Reader for Pinnacle Points files
- `ROIFileReader`: Reader for Pinnacle ROI files
- `BinaryDoseFileReader`: Reader for Pinnacle binary dose files
- `DoseFileReader`: Reader for Pinnacle dose files

```python
class PinnacleFileReader:
    """Base class for reading Pinnacle data files."""
    
    def read(self, file_path):
        """Read and parse a Pinnacle file."""
        pass

class PatientFileReader(PinnacleFileReader):
    """Reader for Pinnacle patient files."""
    
    def read(self, file_path):
        """Parse patient information from Pinnacle patient file."""
        # Implement specific parsing logic
        return patient_data
```

### [ ] Step 3.2: Coordinate System Management
- Create a dedicated module for coordinate transformations
- Implement classes for different patient positions
- Add validation for coordinate consistency
- Create utility functions for common transformations

```python
class CoordinateTransformer:
    """Handles coordinate transformations between different systems."""
    
    def __init__(self, patient_position):
        """Initialize with patient position."""
        self.patient_position = patient_position
        
    def pinnacle_to_dicom(self, x, y, z):
        """Convert Pinnacle coordinates to DICOM coordinates."""
        if self.patient_position == 'HFS':
            return [x, -y, -z]
        elif self.patient_position == 'HFP':
            return [-x, y, -z]
        # Handle other positions...
```

### [ ] Step 3.3: DICOM Creation Refactoring
- Implement template-based DICOM creation
- Create factory classes for DICOM object generation
- Add validation for DICOM compliance
- Add support for different DICOM versions/standards

### [ ] Step 3.4: Binary Data Processing Improvement
- Create specialized handlers for binary data formats
- Implement efficient memory management for large datasets
- Add support for compressed formats
- Add validation for binary data integrity

## [ ] Phase 4: Error Handling and Validation

### [ ] Step 4.1: Input Validation
- Add validation for all input files
- Implement schema validation for Pinnacle files
- Create comprehensive error messages
- Add support for malformed input recovery

### [ ] Step 4.2: Robust Error Handling
- Implement proper exception hierarchy
- Add error recovery mechanisms
- Create detailed error logging
- Implement graceful degradation for missing data

```python
class PinnacleError(Exception):
    """Base class for Pinnacle file errors."""
    pass

class PinnacleFileNotFoundError(PinnacleError):
    """Raised when a required Pinnacle file is not found."""
    pass

class PinnacleParsingError(PinnacleError):
    """Raised when a Pinnacle file cannot be parsed."""
    pass
```

### [ ] Step 4.3: Defensive Programming
- Add parameter validation to all functions
- Implement data consistency checks
- Create warning system for potential issues
- Add automatic type conversion where appropriate

## [ ] Phase 5: Testing and Performance

### [ ] Step 5.1: Unit Testing
- Create unit tests for all components
- Implement test fixtures and mocks
- Add test coverage metrics
- Create property-based tests for complex transformations

### [ ] Step 5.2: Integration Testing
- Create end-to-end tests for complete workflow
- Implement regression tests
- Add validation against known-good DICOM files
- Create benchmarks for performance evaluation

### [ ] Step 5.3: Performance Optimization
- Profile code execution
- Optimize memory usage
- Improve processing speed for large datasets
- Implement parallel processing for independent operations

```python
def process_image_slice(slice_data):
    """Process a single image slice."""
    # Processing logic here
    return processed_slice

def process_all_slices(slices):
    """Process all slices in parallel."""
    from concurrent.futures import ProcessPoolExecutor
    
    with ProcessPoolExecutor() as executor:
        return list(executor.map(process_image_slice, slices))
```

## [ ] Phase 6: User Experience and Deployment

### [ ] Step 6.1: Command Line Interface
- Create robust CLI with proper argument parsing
- Add progress indicators
- Implement verbose mode for debugging
- Add interactive mode for troubleshooting

### [ ] Step 6.2: Logging and Monitoring
- Implement structured logging
- Add performance metrics
- Create audit trail functionality
- Add configurable log levels

```python
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='dicom_converter.log'
)

logger = logging.getLogger(__name__)

def convert_images():
    """Convert images to DICOM format."""
    logger.info("Starting image conversion")
    # Conversion logic
    logger.info("Image conversion completed")
```

### [ ] Step 6.3: Documentation and Examples
- Create comprehensive user documentation
- Add example workflows
- Provide troubleshooting guide
- Create visualization tools for verification

### [ ] Step 6.4: Patient Position-Specific Handling
- Create dedicated handlers for each patient position (HFS, HFP, FFS, FFP)
- Implement automatic position detection
- Add validation for position-specific transformations
- Create visualization tools for position verification

## Implementation Guidelines

### [ ] Code Structure Guidelines
- Each function should have a single responsibility
- Classes should follow the single responsibility principle
- Use dependency injection for component interactions
- Follow PEP 8 style guidelines
- Use descriptive variable and function names
- Keep functions short and focused
- Use proper error handling and input validation

### [ ] Example Refactoring: Converting Global Variables

**Before:**
```python
# Global variables
ROI_COUNT = 0
SeriesUID = "NA"
patientname = ""
dob = ""
pid = ""

def readpatientinfo(ds):
    global patientname
    global dob
    global pid
    
    # Read patient info
    patientname = "Patient Name"
    dob = "19700101"
    pid = "12345"
    
    ds.PatientName = patientname
    ds.PatientBirthDate = dob
    ds.PatientID = pid
    
    return ds
```

**After:**
```python
class Patient:
    """Patient data model."""
    
    def __init__(self):
        self.name = ""
        self.birth_date = ""
        self.id = ""

class PatientInfoReader:
    """Reads patient information from Pinnacle files."""
    
    def read(self, file_path, patient):
        """
        Read patient information from file and populate the patient object.
        
        Args:
            file_path (str): Path to the patient information file
            patient (Patient): Patient object to populate
            
        Returns:
            Patient: Updated patient object
        """
        # Read patient info from file
        with open(file_path, 'r') as f:
            # Parse file contents
            # ...
            patient.name = "Patient Name"
            patient.birth_date = "19700101"
            patient.id = "12345"
            
        return patient
        
class DicomPatientWriter:
    """Writes patient information to DICOM datasets."""
    
    def write(self, patient, dicom_dataset):
        """
        Write patient information to DICOM dataset.
        
        Args:
            patient (Patient): Patient object with information
            dicom_dataset (Dataset): DICOM dataset to update
            
        Returns:
            Dataset: Updated DICOM dataset
        """
        dicom_dataset.PatientName = patient.name
        dicom_dataset.PatientBirthDate = patient.birth_date
        dicom_dataset.PatientID = patient.id
        
        return dicom_dataset
```

### Example Refactoring: Function Modularization

**Before:**
```python
def readtrial(ds, planfolder, plannumber):
    global isocenter
    global xshift
    global yshift
    global zshift
    global doserefpt
    
    # Long function with multiple responsibilities
    # ...
    
    return ds
```

**After:**
```python
class PlanReader:
    """Reads and processes treatment plan data."""
    
    def __init__(self, coordinate_transformer):
        """
        Initialize with coordinate transformer.
        
        Args:
            coordinate_transformer (CoordinateTransformer): For coordinate transformations
        """
        self.coordinate_transformer = coordinate_transformer
    
    def read_trial(self, file_path):
        """
        Read trial data from plan.Trial file.
        
        Args:
            file_path (str): Path to plan.Trial file
            
        Returns:
            Plan: Populated Plan object
        """
        plan = Plan()
        
        # Read basic plan information
        self._read_plan_info(file_path, plan)
        
        # Read beam definitions
        self._read_beams(file_path, plan)
        
        # Read dose information
        self._read_dose_info(file_path, plan)
        
        return plan
    
    def _read_plan_info(self, file_path, plan):
        """Read basic plan information."""
        # Implementation
        
    def _read_beams(self, file_path, plan):
        """Read beam definitions."""
        # Implementation
        
    def _read_dose_info(self, file_path, plan):
        """Read dose information."""
        # Implementation
```

### Example Refactoring: Binary Data Processing

**Before:**
```python
def creatertdose(plannumber, planfolder, beamnum, binarynum, beamdosevalue, numfracs):
    # ... lots of global variables ...
    
    # Reading binary data
    with open("%s%s/%s/plan.Trial.binary.%s"%(Inputf,patientfolder, planfolder, binarynum), "rb") as binary_file:
        data_element = binary_file.read(4)
        while data_element:
            value = struct.unpack(">f", data_element)[0]
            value = float(numfracs)*value*beamdosevalue/100
            pixeldatallist.append(value)
            data_element = binary_file.read(4)
            
    # Processing pixel data
    for h in range(0, dosezdim):
        pixelsforframe = []
        for k in range(0, dosexdim*doseydim):
            pixelsforframe.append(float(pixeldatallist[h*doseydim*dosexdim + k]))
        main_pix_array = main_pix_array + list(reversed(pixelsforframe))
    
    main_pix_array = list(reversed(main_pix_array))
    
    # Scaling and packing
    scale = max(main_pix_array) /65530
    temp_beamds.DoseGridScaling = scale
    pixelvaluelist = []
    for pp, element in enumerate(main_pix_array, 0):
        if scale != 0:
            element = round(element/scale)
        else:
            element = 0
        pixelvaluelist.append(element)
    pixel_binary_block = struct.pack('%si' % len(pixelvaluelist), *pixelvaluelist)
    temp_beamds.PixelData = pixel_binary_block
    
    return main_pix_array, ds
```

**After:**
```python
class DoseBinaryReader:
    """Reads and processes binary dose data files."""
    
    def __init__(self, coordinate_transformer):
        """
        Initialize with coordinate transformer.
        
        Args:
            coordinate_transformer (CoordinateTransformer): For coordinate transformations
        """
        self.coordinate_transformer = coordinate_transformer
    
    def read_binary_dose(self, file_path, dose_params):
        """
        Read binary dose data from file.
        
        Args:
            file_path (str): Path to binary dose file
            dose_params (DoseParameters): Parameters for dose calculation
            
        Returns:
            numpy.ndarray: Raw dose data
        """
        # Read binary data using numpy for efficiency
        with open(file_path, "rb") as binary_file:
            raw_data = np.fromfile(binary_file, dtype='>f4')
            
        # Apply dose calculation factors
        return raw_data * float(dose_params.num_fractions) * dose_params.beam_dose_value / 100

class DoseProcessor:
    """Processes dose data for DICOM export."""
    
    def __init__(self, patient_position):
        """
        Initialize with patient position.
        
        Args:
            patient_position (str): Patient position code (HFS, HFP, etc.)
        """
        self.patient_position = patient_position
    
    def process_dose_volume(self, raw_data, dimensions):
        """
        Process raw dose data into properly oriented 3D volume.
        
        Args:
            raw_data (numpy.ndarray): Raw dose data
            dimensions (tuple): x, y, z dimensions
            
        Returns:
            numpy.ndarray: Processed 3D dose volume
        """
        x_dim, y_dim, z_dim = dimensions
        
        # Reshape into 3D volume
        volume = raw_data.reshape(z_dim, y_dim, x_dim)
        
        # Apply patient-position-specific transformations
        if self.patient_position in ('HFS', 'FFS'):
            # Reverse rows
            volume = volume[:, ::-1, :]
            
        # Apply other transformations as needed
        
        return volume

class DicomDoseWriter:
    """Writes dose data to DICOM format."""
    
    def write_dose_to_dicom(self, dose_volume, ds, max_value=65530):
        """
        Write dose volume to DICOM dataset.
        
        Args:
            dose_volume (numpy.ndarray): 3D dose volume
            ds (Dataset): DICOM dataset to update
            max_value (int): Maximum integer value for scaling
            
        Returns:
            Dataset: Updated DICOM dataset with pixel data
        """
        # Flatten volume in correct order
        flat_data = dose_volume.flatten()
        
        # Calculate scaling factor
        scale = np.max(flat_data) / max_value if np.max(flat_data) > 0 else 1.0
        ds.DoseGridScaling = scale
        
        # Scale and convert to integers
        scaled_data = np.round(flat_data / scale).astype(np.int32)
        
        # Pack into binary block
        pixel_binary_block = struct.pack('%si' % len(scaled_data), *scaled_data)
        ds.PixelData = pixel_binary_block
        
        return ds
```

## Conclusion

This refactoring roadmap provides a comprehensive plan to transform the current monolithic script into a robust, maintainable, and testable Python package. The changes are structured to be incremental, allowing for continuous testing and validation throughout the refactoring process.

By following this roadmap, the code will become:
- More modular and maintainable
- Better documented and easier to understand
- More robust with proper error handling
- Testable with comprehensive unit and integration tests
- Extensible for future enhancements
- Optimized for performance with large datasets

The refactoring should be approached methodically, with each phase completed and tested before moving to the next. This ensures that functionality is preserved throughout the process while progressively improving code quality.
