# ID-Based Loading Refactor Plan

## Overview

This document outlines the refactoring plan to implement ID-based loading across all Reader classes in the pinnacle_io library. The goal is to enable loading of Pinnacle data objects using only numeric IDs without requiring users to specify file paths.

## Current Implementation (PatientReader)

The PatientReader class has been successfully updated to demonstrate the ID-based loading pattern:

### PatientReader Structure
```python
class PatientReader(BasePinnacleReader):
    @staticmethod
    def read_from_ids(institution_id: int, patient_id: int, mount_id: int = 0, file_service: Any = None) -> Patient:
        # Construct standard Pinnacle path format
        patient_path = f"Institution_{institution_id}/Mount_{mount_id}/Patient_{patient_id}"
        # Delegate to path-based method
        return PatientReader.read_from_path(patient_path, file_service)

    @staticmethod
    def read_from_path(patient_path: str, file_service: Any = None) -> Patient:
        # Existing path-based implementation
        ...
```

### BaseReaderService Integration
```python
class BaseReaderService:
    def get_patient(self, institution: Institution | int, patient_id: int, mount_id: int = 0) -> Patient:
        # Extract institution ID from Institution model or use integer directly
        if isinstance(institution, Institution):
            if institution.institution_id is None:
                raise ValueError("Institution model has no institution_id")
            institution_id = institution.institution_id
        else:
            institution_id = institution
        
        return PatientReader.read_from_ids(institution_id, patient_id, mount_id, file_service=self)
```

### PinnacleReader API Integration
```python
class PinnacleReader:
    def get_patient(self, institution: Institution | int, patient_id: int, mount_id: int = 0) -> Patient:
        return self._reader_service.get_patient(institution, patient_id, mount_id)
```

## Pinnacle Data Structure Analysis

Based on the Pinnacle file structure, the following ID patterns are used:

```
/root_path/Institution_#/Mount_0/Patient_#/Patient                      # Patient data
/root_path/Institution_#/Mount_0/Patient_#/ImageSet_#.header            # ImageSet header
/root_path/Institution_#/Mount_0/Patient_#/ImageSet_#.img               # ImageSet data
/root_path/Institution_#/Mount_0/Patient_#/ImageSet_#.ImageInfo         # ImageInfo data
/root_path/Institution_#/Mount_0/Patient_#/Plan_#/plan.roi              # ROI data
/root_path/Institution_#/Mount_0/Patient_#/Plan_#/plan.Points           # Points data
/root_path/Institution_#/Mount_0/Patient_#/Plan_#/plan.Trial            # Trial data
/root_path/Institution_#/Mount_0/Patient_#/Plan_#/plan.Trial.binary.### # Dose data
/root_path/Institution_#/Mount_0/Patient_#/Plan_#/plan.PatientSetup     # PatientSetup data
/root_path/Institution_#/Mount_0/Patient_#/Plan_#/plan.Pinnacle.Machines # Machine data
```

## ID Mapping Strategy

### ID Categories

1. **Institution-Level Data**: 
   - Institution: `institution_id`

2. **Patient-Level Data**:
   - Patient: `institution_id`, `patient_id`, `mount_id`
   - ImageSet: `institution_id`, `patient_id`, `mount_id`, `image_set_id`
   - ImageInfo: `institution_id`, `patient_id`, `mount_id`, `image_set_id`

3. **Plan-Level Data**:
   - Trial: `institution_id`, `patient_id`, `mount_id`, `plan_id`
   - ROI: `institution_id`, `patient_id`, `mount_id`, `plan_id`
   - Points: `institution_id`, `patient_id`, `mount_id`, `plan_id`
   - PatientSetup: `institution_id`, `patient_id`, `mount_id`, `plan_id`
   - Machine: `institution_id`, `patient_id`, `mount_id`, `plan_id`
   - Dose: `institution_id`, `patient_id`, `mount_id`, `plan_id`, `trial` (object)

## Implementation Pattern

### Standard Pattern for Reader Classes

Each Reader class should implement:

1. **`read_from_ids()` method**: Accepts appropriate IDs and constructs the path
2. **`read_from_path()` method**: Existing path-based implementation (renamed from `read`)
3. **Path construction**: Uses f-string to build Pinnacle standard paths

### Standard Pattern for BaseReaderService

Each method should:

1. **Accept model objects or IDs**: Use union types like `Institution | int`
2. **Extract IDs from models**: Handle model objects with proper validation
3. **Delegate to Reader**: Call the appropriate Reader's `read_from_ids` method

### Standard Pattern for PinnacleReader API

Each method should:

1. **Match service signature**: Same parameters as BaseReaderService
2. **Delegate to service**: Direct delegation to `_reader_service`
3. **Update documentation**: Examples showing ID-based usage

## Implementation Todo List

### ✅ Completed: PatientReader
- [x] PatientReader.read_from_ids() method implemented
- [x] PatientReader.read_from_path() method (renamed from read)
- [x] BaseReaderService.get_patient() updated
- [x] PinnacleReader.get_patient() updated
- [x] Unit tests updated (test_patient_reader.py, test_base_reader_service.py, test_api.py)

### 📋 Todo List: Apply to All Remaining Readers

#### 1. ImageSetReader
- [x] **Reader Class Updates**:
  - [x] Add `read_from_ids(institution_id: int, patient_id: int, image_set_id: int, mount_id: int = 0, file_service: Any = None) -> ImageSet`
  - [x] Rename `read()` to `read_from_path()`
  - [x] Update path construction: `f"Institution_{institution_id}/Mount_{mount_id}/Patient_{patient_id}/ImageSet_{image_set_id}"`
- [x] **BaseReaderService Updates**:
  - [x] Update `get_image_set()` signature to accept IDs
  - [x] Add model object handling for Institution parameter
- [x] **PinnacleReader API Updates**:
  - [x] Update `get_image_set()` signature to match service
  - [x] Update documentation and examples
- [x] **Test Updates**:
  - [x] Update `test_image_set_reader.py`
  - [x] Update `test_base_reader_service.py` relevant tests
  - [x] Update `test_api.py` relevant tests

#### 2. ImageInfoReader  
- [x] **Reader Class Updates**:
  - [x] Add `read_from_ids(institution_id: int, patient_id: int, image_set_id: int, mount_id: int = 0, file_service: Any = None) -> list[ImageInfo]`
  - [x] Rename `read()` to `read_from_path()`
  - [x] Update path construction: `f"Institution_{institution_id}/Mount_{mount_id}/Patient_{patient_id}/ImageSet_{image_set_id}"`
- [x] **BaseReaderService Updates**:
  - [x] Update `get_image_info()` signature to accept IDs
  - [x] Add model object handling for Institution parameter
- [x] **PinnacleReader API Updates**:
  - [x] Update `get_image_info()` signature to match service
  - [x] Update documentation and examples
- [x] **Test Updates**:
  - [x] Update `test_image_info_reader.py`
  - [x] Update `test_base_reader_service.py` relevant tests
  - [x] Update `test_api.py` relevant tests

#### 3. TrialReader
- [x] **Reader Class Updates**:
  - [x] Add `read_from_ids(institution_id: int, patient_id: int, plan_id: int, mount_id: int = 0, file_service: Any = None) -> list[Trial]`
  - [x] Rename `read()` to `read_from_path()`  
  - [x] Update path construction: `f"Institution_{institution_id}/Mount_{mount_id}/Patient_{patient_id}/Plan_{plan_id}"`
- [x] **BaseReaderService Updates**:
  - [x] Update `get_trials()` signature to accept IDs
  - [x] Add model object handling for Institution parameter
- [x] **PinnacleReader API Updates**:
  - [x] Update `get_trials()` signature to match service
  - [x] Update documentation and examples
- [x] **Test Updates**:
  - [x] Update `test_trial_reader.py`
  - [x] Update `test_base_reader_service.py` relevant tests
  - [x] Update `test_api.py` relevant tests

#### 4. ROIReader
- [x] **Reader Class Updates**:
  - [x] Add `read_from_ids(institution_id: int, patient_id: int, plan_id: int, mount_id: int = 0, file_service: Any = None) -> list[ROI]`
  - [x] Rename `read()` to `read_from_path()`
  - [x] Update path construction: `f"Institution_{institution_id}/Mount_{mount_id}/Patient_{patient_id}/Plan_{plan_id}"`
- [x] **BaseReaderService Updates**:
  - [x] Update `get_rois()` signature to accept IDs
  - [x] Add model object handling for Institution parameter
- [x] **PinnacleReader API Updates**:
  - [x] Update `get_rois()` signature to match service
  - [x] Update documentation and examples
- [x] **Test Updates**:
  - [x] Update `test_roi_reader.py`
  - [x] Update `test_base_reader_service.py` relevant tests
  - [x] Update `test_api.py` relevant tests

#### 5. PointReader
- [x] **Reader Class Updates**:
  - [x] Add `read_from_ids(institution_id: int, patient_id: int, plan_id: int, mount_id: int = 0, file_service: Any = None) -> list[Point]`
  - [x] Rename `read()` to `read_from_path()`
  - [x] Update path construction: `f"Institution_{institution_id}/Mount_{mount_id}/Patient_{patient_id}/Plan_{plan_id}"`
- [x] **BaseReaderService Updates**:
  - [x] Update `get_points()` signature to accept IDs  
  - [x] Add model object handling for Institution parameter
- [x] **PinnacleReader API Updates**:
  - [x] Update `get_points()` signature to match service
  - [x] Update documentation and examples
- [x] **Test Updates**:
  - [x] Update `test_point_reader.py`
  - [x] Update `test_base_reader_service.py` relevant tests
  - [x] Update `test_api.py` relevant tests

#### 6. PatientSetupReader
- [x] **Reader Class Updates**:
  - [x] Add `read_from_ids(institution_id: int, patient_id: int, plan_id: int, mount_id: int = 0, file_service: Any = None) -> PatientSetup`
  - [x] Rename `read()` to `read_from_path()`
  - [x] Update path construction: `f"Institution_{institution_id}/Mount_{mount_id}/Patient_{patient_id}/Plan_{plan_id}"`
- [x] **BaseReaderService Updates**:
  - [x] Update `get_patient_setup()` signature to accept IDs
  - [x] Add model object handling for Institution parameter
- [x] **PinnacleReader API Updates**:
  - [x] Update `get_patient_setup()` signature to match service
  - [x] Update documentation and examples
- [x] **Test Updates**:
  - [x] Update `test_patient_setup_reader.py`
  - [x] Update `test_base_reader_service.py` relevant tests
  - [x] Update `test_api.py` relevant tests

#### 7. MachineReader
- [x] **Reader Class Updates**:
  - [x] Add `read_from_ids(institution_id: int, patient_id: int, plan_id: int, mount_id: int = 0, file_service: Any = None) -> list[Machine]`
  - [x] Rename `read()` to `read_from_path()`
  - [x] Update path construction: `f"Institution_{institution_id}/Mount_{mount_id}/Patient_{patient_id}/Plan_{plan_id}"`
- [x] **BaseReaderService Updates**:
  - [x] Update `get_machines()` signature to accept IDs
  - [x] Add model object handling for Institution parameter
- [x] **PinnacleReader API Updates**:
  - [x] Update `get_machines()` signature to match service
  - [x] Update documentation and examples
- [x] **Test Updates**:
  - [x] Update `test_machine_reader.py`
  - [x] Update `test_base_reader_service.py` relevant tests
  - [x] Update `test_api.py` relevant tests

#### 8. DoseReader
- [x] **Reader Class Updates**:
  - [x] Add `read_from_ids(institution_id: int, patient_id: int, plan_id: int, trial: Trial, mount_id: int = 0, file_service: Any = None) -> Dose`
  - [x] Rename `read()` to `read_from_path()`
  - [x] Update path construction: `f"Institution_{institution_id}/Mount_{mount_id}/Patient_{patient_id}/Plan_{plan_id}"`
  - [x] Note: Dose requires Trial object, not just trial_id
- [x] **BaseReaderService Updates**:
  - [x] Update `get_dose()` signature to accept IDs
  - [x] Add model object handling for Institution parameter
- [x] **PinnacleReader API Updates**:
  - [x] Update `get_dose()` signature to match service
  - [x] Update documentation and examples
- [x] **Test Updates**:
  - [x] Update `test_dose_reader.py`
  - [x] Update `test_base_reader_service.py` relevant tests
  - [x] Update `test_api.py` relevant tests

#### 9. InstitutionReader (Special Case)
- [x] **Analysis**: Institution is at the root level and doesn't need ID-based loading
- [x] **Decision**: Added `read_from_ids()` and `read_from_path()` for consistency, but `read_from_ids()` doesn't use institution_id for path construction
- [x] **Implementation**: Added methods for API consistency while maintaining backward compatibility
- [x] **Verification**: Ensure current `get_institution()` works correctly

#### 10. PlanReader
- [x] **Verify existence**: PlanReader exists and reads Plan data from Patient files
- [x] **Reader Class Updates**:
  - [x] Add `read_from_ids(institution_id: int, patient_id: int, mount_id: int = 0, file_service: Any = None) -> List[Plan]`
  - [x] Rename `read()` to `read_from_path()`
  - [x] Update path construction: `f"Institution_{institution_id}/Mount_{mount_id}/Patient_{patient_id}"`
  - [x] Note: Plans are read from Patient files, not separate Plan files
- [x] **Test Updates**:
  - [x] Update `test_plan_reader.py` with new methods

### ✅ Special Considerations

#### Image-Related Methods
- [x] `get_image_header()`: Uses same IDs as ImageSet - already implemented correctly
- [x] `get_image_slice()`: Uses same IDs as ImageSet - already implemented correctly
- [x] Both methods have proper ID-based signatures and model object handling

#### Method-Specific Image Loading
- [x] All image-related methods in PinnacleReader API have been updated with ID-based signatures
- [x] BaseReaderService methods properly extract IDs from model objects
- [x] Backward compatibility maintained through proper delegation

## Implementation Guidelines

### Code Quality Standards
1. **Type Hints**: All methods must use proper type hints with union types (`|` not `Union`)
2. **Documentation**: Complete docstrings with examples showing ID-based usage
3. **Error Handling**: Proper validation for None values and invalid IDs
4. **Consistent Naming**: Use `read_from_ids` and `read_from_path` consistently
5. **Service Injection**: Maintain optional `file_service` parameter pattern

### Testing Requirements
1. **Unit Tests**: Each reader must have tests for both methods
2. **Integration Tests**: Service and API layers must be tested together
3. **ID Validation**: Tests for invalid/missing IDs  
4. **Service Injection**: Tests with and without file services
5. **Model Handling**: Tests for both Institution objects and integer IDs

### Path Construction Standards
- Use f-strings for path construction
- Follow exact Pinnacle naming: `Institution_#`, `Mount_#`, `Patient_#`, `Plan_#`, `ImageSet_#`
- Default `mount_id=0` for all methods
- Validate ID parameters are integers

### Error Handling Standards
- `ValueError` for invalid/missing IDs from model objects
- `FileNotFoundError` for non-existent files (handled by readers)
- Clear error messages with context about which IDs failed

## Benefits

1. **Simplified API**: Users can load data with just IDs
2. **Type Safety**: Strong typing with union types
3. **Backward Compatibility**: Path-based methods remain available
4. **Consistent Interface**: All readers follow same pattern
5. **Service Flexibility**: File service injection remains optional
6. **Clear Documentation**: Examples show modern ID-based usage

## Migration Strategy

1. **Phase 1**: Complete ImageSetReader and ImageInfoReader (most used) [Status: Complete]
2. **Phase 2**: Complete plan-level readers (Trial, ROI, Points, etc.) [Status: Complete]
3. **Phase 3**: Complete setup and machine readers [Status: Complete]
4. **Phase 4**: Handle DoseReader, InstitutionReader, PlanReader, special cases, and edge cases [Status: Complete]
5. **Phase 5**: Update all examples and documentation [Status: Complete]

## Phase 5 Completion Summary

**Documentation Updates Completed:**
- ✅ Updated README.md with ID-based usage examples throughout
- ✅ Updated examples/basic_usage.py with comprehensive ID-based demonstrations
- ✅ Updated examples/file_formats.py with ID-based methods across all formats
- ✅ Updated examples/README.md with benefits section and ID-based patterns
- ✅ Updated CLAUDE.md project documentation with modern API information
- ✅ Updated PinnacleReader class docstring examples
- ✅ Added comparison sections showing ID-based vs path-based approaches
- ✅ Emphasized the recommendation to use ID-based methods for new development

**Key Documentation Features:**
- Clear examples showing both modern ID-based and legacy path-based approaches
- Benefits section explaining advantages of ID-based methods
- Error handling examples with ID-based access
- Comprehensive coverage across all examples and documentation files
- Consistent messaging about ID-based methods being the recommended approach

## REFACTOR STATUS: COMPLETE ✅

All phases of the ID-based loading refactor have been successfully completed:
- ✅ **Phase 1**: ImageSetReader and ImageInfoReader implementation
- ✅ **Phase 2**: Plan-level readers (Trial, ROI, Points, etc.)  
- ✅ **Phase 3**: Setup and machine readers
- ✅ **Phase 4**: DoseReader, InstitutionReader, PlanReader, and edge cases
- ✅ **Phase 5**: Documentation and examples updates

The pinnacle_io library now provides a modern, intuitive ID-based API while maintaining full backward compatibility with path-based methods.