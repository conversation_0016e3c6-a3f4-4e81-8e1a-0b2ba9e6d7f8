# createdcm.py - Project Summary

## Overview
`createdcm.py` is a Python script designed to convert Pinnacle text files into standardized DICOM files for medical imaging and radiation therapy. The script transforms various Pinnacle-format text and binary data into four primary DICOM file types:

1. CT Images (CT)
2. RT Structure Sets
3. RT Plans
4. RT Dose

The script was authored by <PERSON><PERSON> and <PERSON> as noted in the header comments.

## File Requirements
The script requires the following input files from the Pinnacle system:
- `Patient` (text file under patient folder)
- `plan.Points` (file under Patient folder -> plan label folder)
- `plan.roi` (file under Patient folder -> plan label folder)
- `plan.Trial` (file under Patient folder -> plan label folder)
- `plan.Trial.Binary.xxx` (xxx represents number of file, several needed)
- `ImageSet_%s.ImageInfo` (text file under patient folder)
- `ImageSet_%s.header`
- Image files (either in folder or as `ImageSet_%s` file)

## Code Structure

### Global Variables
The script uses a large number of global variables (80+) to track various aspects of the patient data, DICOM file metadata, and conversion state. These include:
- Patient demographics (name, DOB, ID, etc.)
- Study and series UIDs
- Coordinate shifts
- Equipment information
- Dose information
- Conversion flags 
- Color definitions for ROI visualization
- Dose measurement parameters (PDD values)
- Patient positioning parameters
- Image dimension and pixel spacing information

### Main Function Flow
The `main()` function orchestrates the entire conversion process:

1. **Initialization**
   - Resets all global variables via `initglobalvars()`
   - Takes input/output folder paths as parameters

2. **DICOM Structure Creation**
   - Creates dataset for structure file with `createstructds()`
   - Generates UIDs for structure file

3. **Patient Information Reading**
   - Reads patient information with `readpatientinfo()`
   - Gets image information via `readImageInfo()`
   - Initializes datasets with `initds()`

4. **Image Processing**
   - Converts or creates image files with `convertimages()`
   - Updates existing DICOM image files or creates new ones if not available
   - If images don't exist, creates them from raw data using `createimagefiles()`
   - Properly adjusts pixel data based on patient position

5. **Structure Processing**
   - Processes ROI information with `readroi()`
   - Applies coordinate shifts with `getstructshift()`
   - Creates RT structure files

6. **Plan Processing**
   - Processes each treatment plan
   - Creates RT plan files with beam information from `readtrial()`
   - Sets up treatment beams, control points, and treatment parameters
   - Handles beam types (static, dynamic, step-and-shoot)
   - Processes MLC leaf positions for intensity modulated treatments
   - Manages wedge information

7. **Dose Processing**
   - Generates dose files with `creatertdose()`
   - Reads binary dose data and converts to DICOM format
   - Combines dose data from multiple beams

8. **File Saving**
   - Saves all generated DICOM files to the specified output directory

### Key Functions

- `initglobalvars()`: Resets all global variables to their default values
- `convertimages()`: Converts image data to DICOM format by updating metadata in existing files or creating new ones
- `createimagefiles()`: Creates new image files if none exist by reading raw pixel data
- `getheaderinfo()`: Extracts image metadata from header files
- `getdateandtime()`: Extracts scan date and time information
- `readImageInfo()`: Extracts metadata from image info files for UIDs
- `createstructds()`: Creates a new DICOM dataset for RT Structure Set
- `getstructshift()`: Calculates coordinate shifts based on image dimensions and patient position
- `createplands()`: Creates a new DICOM dataset for RT Plan
- `readpatientinfo()`: Extracts patient demographic information
- `readpoints()`: Processes point data from plan.Points
- `readroi()`: Processes ROI data from plan.roi
- `planinit()`: Initializes treatment plan data
- `getpatientsetup()`: Extracts patient setup information
- `readtrial()`: Processes treatment plan data from plan.Trial, including beam configurations
- `creatertdose()`: Creates RT dose files from binary data, scaling values appropriately

## Data Processing Details

### Patient Position Handling
The script supports four patient positions with specific transformations:
- HFS (Head First Supine)
- HFP (Head First Prone)
- FFS (Feet First Supine)
- FFP (Feet First Prone)

Each position requires specific coordinate transformations for generating correct DICOM files.

### Coordinate System Handling
The script manages multiple coordinate systems and applies transformations:
- Pinnacle coordinates to DICOM coordinates
- Patient setup position adjustments (HFS, FFP, HFP, FFS)
- Isocenter shifts and adjustments

### DICOM Header Creation
Each created DICOM file is assigned:
- Specific UIDs (SOPInstanceUID, SeriesInstanceUID, etc.)
- Required DICOM metadata (dates, times, equipment, etc.)
- Proper references to other related DICOM files

### Binary Data Processing
Binary dose data is:
- Read in 4-byte floating-point format from plan.Trial.binary.xxx files
- Scaled and processed based on prescription values and number of fractions
- Correctly oriented based on patient position
- Packed into DICOM pixel data with appropriate scaling

## Technical Implementation Details

### Command Line Usage
The script can be run directly from the command line, taking three arguments:
1. Patient folder name
2. Input folder path
3. Output folder path

```
python createdcm.py [patient_folder] [input_folder] [output_folder]
```

### File Output
- CT images: CT.[SOPInstanceUID].dcm
- Structure set: RS.[structsopinstuid].dcm
- Treatment plan: RP.[plansopinstuid].[plan_number].dcm
- Dose files: RD.[doseinstuid].[plan_number].dcm

## Refactoring Progress

### Implemented File Readers
The project has successfully implemented a comprehensive set of file readers for all Pinnacle file types. These readers follow object-oriented design principles and provide a structured way to read and parse Pinnacle files.

#### Base Reader Classes
- **FileReader**: Abstract base class for all file readers with caching support
- **ModelReader**: Interface for readers that convert file data to model objects
- **PinnacleFileReader**: Base class for reading hierarchical Pinnacle data files with support for parsing complex nested structures

#### Specific Pinnacle File Readers
- **InstitutionFileReader**: Reads Pinnacle Institution files and converts them to Institution model objects
- **PatientFileReader**: Parses patient information from Pinnacle patient files
- **PlanFileReader**: Reads and parses Pinnacle Plan files containing treatment plan information
- **ImageHeaderFileReader**: Extracts metadata from Pinnacle ImageSet header files
- **ImageInfoFileReader**: Processes ImageInfo files for CT slice information
- **ImageFileReader**: Reads binary image data from Pinnacle image files
- **TrialFileReader**: Parses Trial files containing detailed plan configuration
- **PointsFileReader**: Processes point data (POIs) from Pinnacle files
- **ROIFileReader**: Reads ROI (Region of Interest) data for structures and contours
- **BinaryDoseFileReader**: Reads binary dose data from Pinnacle dose volume files
- **DoseFileReader**: Combines beam doses into complete plan dose distributions

These readers replace the direct file parsing in the original script with modular, maintainable classes. They include proper error handling, type checking, and caching mechanisms to improve performance when reading the same files multiple times.

The implementation follows a consistent pattern where each reader:
1. Inherits from the appropriate base class
2. Implements a `read()` method to parse specific file types
3. Converts parsed data into domain model objects
4. Handles format-specific parsing requirements

This architecture decouples file I/O from data processing and allows for future extensions to support additional file formats.

## Limitations
- The code uses a large number of global variables (over 80), making it difficult to maintain
- Error handling is minimal, with few try-except blocks
- Many hard-coded values and assumptions about file formats and DICOM standards
- Limited modularity and testing capability
- Extensive coupling between functions through global variables
- Documentation is sparse, mostly in the form of brief comments
- The code contains commented-out sections and debugging print statements
- No proper logging mechanism, uses print statements for status updates
- No input validation or error recovery mechanisms
- Inefficient file parsing with repeated open/close operations on the same files
- Hardcoded parameters for MLC configurations, dose scaling, and other treatment parameters
