# DICOM Converter GUI - Detailed Implementation Plan

## 1. Project Structure & Dependencies

### Required Libraries
```python
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import threading
import multiprocessing
import queue
from PIL import Image, ImageTk
import numpy as np
import os
import zipfile
import tarfile
```

### Main Application Structure
```
src/
├── main.py                 # Entry point
├── gui/
│   ├── __init__.py
│   ├── main_window.py      # Main application window
│   ├── components/
│   │   ├── __init__.py
│   │   ├── title_bar.py    # Custom title bar
│   │   ├── header_panel.py # Load buttons panel
│   │   ├── patient_plans_panel.py# Left sidebar
│   │   ├── main_panel.py   # Center content area
│   │   └── progress_modal.py# Progress dialog
│   └── styles/
│       ├── __init__.py
│       └── custom_styles.py# Custom styling
├── core/
│   ├── __init__.py
│   ├── data_loader.py      # File loading logic
│   ├── dicom_converter.py  # DICOM conversion
│   └── patient_data.py     # Data models
└── utils/
    ├── __init__.py
    └── threading_utils.py  # Threading helpers
```

## 2. Main Window Hierarchy

### Root Window (MainWindow)
```python
class MainWindow(ttk_bs.Window):
    def __init__(self):
        super().__init__(themename="litera")
        self.title("DICOM Converter - Patient Data Processing")
        self.geometry("1200x800")
        self.minsize(1000, 600)
        
        # Initialize data
        self.patients = []
        self.selected_patient = None
        self.selected_plan = None
        self.selected_trials = set()
        
        # Setup UI
        self.setup_ui()
        self.setup_bindings()
```

### Frame Hierarchy
```
MainWindow (ttk_bs.Window)
├── title_frame (ttk.Frame) - pack(fill=X, side=TOP)
│   └── TitleBarComponent
├── header_frame (ttk.Frame) - pack(fill=X, side=TOP)
│   └── HeaderPanelComponent  
├── main_container (ttk.Frame) - pack(fill=BOTH, expand=True)
│   ├── patient_frame (ttk.Frame) - pack(fill=Y, side=LEFT)
│   │   └── PatientPanelComponent
│   ├── separator (ttk.Separator) - pack(fill=Y, side=LEFT, padx=2)
│   └── content_frame (ttk.Frame) - pack(fill=BOTH, expand=True, side=RIGHT)
│       └── MainPanelComponent
└── progress_modal (ProgressModal) - place(relx=0.5, rely=0.5, anchor=CENTER)
```

## 3. Component Implementation Details

### 3.1 Title Bar Component (title_bar.py)

```python
class TitleBarComponent(ttk.Frame):
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        self.configure(style='Primary.TFrame', height=60)
        
        # Title label
        self.title_label = ttk.Label(
            self, 
            text="DICOM Converter - Patient Data Processing",
            style='Primary.Inverse.TLabel',
            font=('Segoe UI', 18, 'bold')
        )
        self.title_label.pack(side=LEFT, padx=20, pady=15)
        
        # Window controls (optional - for custom window)
        self.controls_frame = ttk.Frame(self, style='Primary.TFrame')
        self.controls_frame.pack(side=RIGHT, padx=20, pady=15)
```

### 3.2 Header Panel Component (header_panel.py)

```python
class HeaderPanelComponent(ttk.Frame):
    def __init__(self, parent, callback_handler, **kwargs):
        super().__init__(parent, **kwargs)
        self.callback_handler = callback_handler
        self.configure(style='Secondary.TFrame', height=80)
        
        # Load data label
        self.load_label = ttk.Label(
            self,
            text="Load Patient Data:",
            font=('Segoe UI', 16, 'bold')
        )
        self.load_label.pack(side=LEFT, padx=20, pady=20)
        
        # Button frame
        self.button_frame = ttk.Frame(self)
        self.button_frame.pack(side=LEFT, padx=10, pady=20)
        
        # Load buttons
        self.tar_button = ttk.Button(
            self.button_frame,
            text="Load TAR File",
            style='Success.TButton',
            command=self.load_tar_file,
            width=15
        )
        self.tar_button.pack(side=LEFT, padx=5)
        
        self.zip_button = ttk.Button(
            self.button_frame,
            text="Load ZIP File", 
            style='Success.TButton',
            command=self.load_zip_file,
            width=15
        )
        self.zip_button.pack(side=LEFT, padx=5)
        
        self.dir_button = ttk.Button(
            self.button_frame,
            text="Select Directory",
            style='Success.TButton', 
            command=self.load_directory,
            width=18
        )
        self.dir_button.pack(side=LEFT, padx=5)
        
    def load_tar_file(self):
        filename = filedialog.askopenfilename(
            title="Select TAR file",
            filetypes=[("TAR files", "*.tar"), ("All files", "*.*")]
        )
        if filename:
            self.callback_handler.load_data_source(filename, 'tar')
    
    def load_zip_file(self):
        filename = filedialog.askopenfilename(
            title="Select ZIP file", 
            filetypes=[("ZIP files", "*.zip"), ("All files", "*.*")]
        )
        if filename:
            self.callback_handler.load_data_source(filename, 'zip')
            
    def load_directory(self):
        dirname = filedialog.askdirectory(title="Select patient data directory")
        if dirname:
            self.callback_handler.load_data_source(dirname, 'directory')
```

### 3.3 Patient Panel Component (patient_panel.py)

```python
class PatientPanelComponent(ttk.Frame):
    def __init__(self, parent, callback_handler, **kwargs):
        super().__init__(parent, **kwargs)
        self.callback_handler = callback_handler
        self.configure(width=350)
        
        # Header
        self.header_label = ttk.Label(
            self,
            text="Patients & Plans",
            font=('Segoe UI', 14, 'bold')
        )
        self.header_label.pack(anchor=W, padx=10, pady=(10, 5))
        
        # Scrollable frame setup
        self.setup_scrollable_frame()
        
    def setup_scrollable_frame(self):
        # Canvas and scrollbar for scrolling
        self.canvas = tk.Canvas(self, highlightthickness=0)
        self.scrollbar = ttk.Scrollbar(self, orient=VERTICAL, command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)
        
        # Configure scrolling
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        
        # Pack components
        self.canvas.pack(side=LEFT, fill=BOTH, expand=True, padx=(10, 0), pady=10)
        self.scrollbar.pack(side=RIGHT, fill=Y, pady=10)
        
        # Bind mousewheel
        self.bind_mousewheel()
        
    def bind_mousewheel(self):
        def _on_mousewheel(event):
            self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        self.canvas.bind("<MouseWheel>", _on_mousewheel)
        
    def add_patient(self, patient_data):
        patient_widget = PatientWidget(
            self.scrollable_frame, 
            patient_data, 
            self.callback_handler
        )
        patient_widget.pack(fill=X, padx=5, pady=5)
        
    def clear_patients(self):
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

class PatientWidget(ttk.Frame):
    def __init__(self, parent, patient_data, callback_handler):
        super().__init__(parent, style='Card.TFrame')
        self.patient_data = patient_data
        self.callback_handler = callback_handler
        self.selected_plan = None
        
        self.configure(relief='solid', borderwidth=1)
        self.pack_propagate(False)
        self.configure(height=120)
        
        self.setup_ui()
        
    def setup_ui(self):
        # Patient info
        self.info_label = ttk.Label(
            self,
            text=f"Patient: {self.patient_data.name} (ID: {self.patient_data.id})",
            font=('Segoe UI', 12, 'bold')
        )
        self.info_label.pack(anchor=W, padx=10, pady=(5, 0))
        
        # Content frame for thumbnail and plans
        self.content_frame = ttk.Frame(self)
        self.content_frame.pack(fill=BOTH, expand=True, padx=10, pady=5)
        
        # CT Thumbnail
        self.thumbnail_frame = ttk.Frame(self.content_frame)
        self.thumbnail_frame.pack(side=LEFT, padx=(0, 10))
        
        self.thumbnail_label = ttk.Label(
            self.thumbnail_frame,
            text="CT",
            background='gray',
            foreground='white',
            width=8,
            height=4,
            anchor=CENTER
        )
        self.thumbnail_label.pack()
        
        # Plans frame
        self.plans_frame = ttk.Frame(self.content_frame)
        self.plans_frame.pack(side=LEFT, fill=BOTH, expand=True)
        
        self.plans_label = ttk.Label(
            self.plans_frame,
            text="Plans:",
            font=('Segoe UI', 11)
        )
        self.plans_label.pack(anchor=W)
        
        # Plan buttons
        for plan in self.patient_data.plans:
            plan_button = ttk.Button(
                self.plans_frame,
                text=f"▶ {plan.name}",
                style='Outline.TButton',
                command=lambda p=plan: self.select_plan(p),
                width=25
            )
            plan_button.pack(fill=X, pady=1)
            
    def select_plan(self, plan):
        # Update button styles
        for widget in self.plans_frame.winfo_children():
            if isinstance(widget, ttk.Button):
                if plan.name in widget.cget('text'):
                    widget.configure(style='Primary.TButton')
                else:
                    widget.configure(style='Outline.TButton')
                    
        self.selected_plan = plan
        self.callback_handler.plan_selected(self.patient_data, plan)
```

### 3.4 Main Panel Component (main_panel.py)

```python
class MainPanelComponent(ttk.Frame):
    def __init__(self, parent, callback_handler, **kwargs):
        super().__init__(parent, **kwargs)
        self.callback_handler = callback_handler
        self.current_patient = None
        self.current_plan = None
        
        self.setup_ui()
        
    def setup_ui(self):
        # Header frame
        self.header_frame = ttk.Frame(self, style='Info.TFrame', height=40)
        self.header_frame.pack(fill=X, pady=(0, 10))
        self.header_frame.pack_propagate(False)
        
        self.header_label = ttk.Label(
            self.header_frame,
            text="Select a plan to view details",
            font=('Segoe UI', 16, 'bold')
        )
        self.header_label.pack(side=LEFT, padx=15, pady=10)
        
        # Main content frame
        self.content_frame = ttk.Frame(self)
        self.content_frame.pack(fill=BOTH, expand=True)
        
        # Top section - CT image and ROI/POI panels
        self.top_frame = ttk.Frame(self.content_frame)
        self.top_frame.pack(fill=BOTH, expand=True, pady=(0, 10))
        
        # CT Display frame
        self.ct_frame = ttk.LabelFrame(self.top_frame, text="CT Image with Overlays")
        self.ct_frame.pack(side=LEFT, fill=BOTH, expand=True, padx=(0, 10))
        
        self.ct_canvas = tk.Canvas(
            self.ct_frame,
            bg='#2d3436',
            highlightthickness=0
        )
        self.ct_canvas.pack(fill=BOTH, expand=True, padx=5, pady=5)
        
        # ROI/POI panel
        self.roi_frame = ttk.LabelFrame(self.top_frame, text="Structures & Points", width=200)
        self.roi_frame.pack(side=RIGHT, fill=Y)
        self.roi_frame.pack_propagate(False)
        
        self.setup_roi_panel()
        
        # Bottom section - Trials
        self.trials_frame = ttk.LabelFrame(self.content_frame, text="Available Trials for Export")
        self.trials_frame.pack(fill=X, pady=10)
        
        self.setup_trials_panel()
        
        # Export button
        self.export_frame = ttk.Frame(self.content_frame)
        self.export_frame.pack(fill=X, pady=10)
        
        self.export_button = ttk.Button(
            self.export_frame,
            text="Export 0 Trials to DICOM",
            style='Danger.TButton',
            state=DISABLED,
            command=self.export_trials,
            width=30
        )
        self.export_button.pack(side=LEFT, padx=10)
        
    def setup_roi_panel(self):
        # ROI section
        self.roi_title = ttk.Label(
            self.roi_frame,
            text="ROIs (Structures)",
            font=('Segoe UI', 12, 'bold')
        )
        self.roi_title.pack(anchor=W, padx=10, pady=(10, 5))
        
        self.roi_list_frame = ttk.Frame(self.roi_frame)
        self.roi_list_frame.pack(fill=X, padx=10)
        
        # POI section
        self.poi_title = ttk.Label(
            self.roi_frame,
            text="POIs (Points)",
            font=('Segoe UI', 12, 'bold')
        )
        self.poi_title.pack(anchor=W, padx=10, pady=(20, 5))
        
        self.poi_list_frame = ttk.Frame(self.roi_frame)
        self.poi_list_frame.pack(fill=X, padx=10)
        
    def setup_trials_panel(self):
        # Trials list frame
        self.trials_list_frame = ttk.Frame(self.trials_frame)
        self.trials_list_frame.pack(side=LEFT, fill=BOTH, expand=True, padx=10, pady=10)
        
        # Trial details frame
        self.trial_details_frame = ttk.LabelFrame(
            self.trials_frame, 
            text="Trial Details",
            width=380
        )
        self.trial_details_frame.pack(side=RIGHT, fill=Y, padx=10, pady=10)
        self.trial_details_frame.pack_propagate(False)
        
    def update_plan_view(self, patient, plan):
        self.current_patient = patient
        self.current_plan = plan
        
        # Update header
        self.header_label.configure(
            text=f"Plan: {plan.name} - Patient: {patient.name}"
        )
        
        # Update CT display
        self.update_ct_display(plan)
        
        # Update ROI/POI lists
        self.update_roi_list(plan.rois)
        self.update_poi_list(plan.pois)
        
        # Update trials
        self.update_trials_list(plan.trials)
        
    def update_roi_list(self, rois):
        # Clear existing
        for widget in self.roi_list_frame.winfo_children():
            widget.destroy()
            
        # Add ROI items
        colors = ['#e17055', '#00b894', '#fdcb6e', '#6c5ce7', '#fd79a8']
        for i, roi in enumerate(rois):
            roi_frame = ttk.Frame(self.roi_list_frame)
            roi_frame.pack(fill=X, pady=1)
            
            color_label = tk.Label(
                roi_frame,
                bg=colors[i % len(colors)],
                width=2,
                height=1
            )
            color_label.pack(side=LEFT, padx=(0, 5))
            
            name_label = ttk.Label(roi_frame, text=roi.name, font=('Segoe UI', 10))
            name_label.pack(side=LEFT)
            
    def update_poi_list(self, pois):
        # Clear existing
        for widget in self.poi_list_frame.winfo_children():
            widget.destroy()
            
        # Add POI items
        for poi in pois:
            poi_label = ttk.Label(
                self.poi_list_frame,
                text=f"• {poi.name}",
                font=('Segoe UI', 10)
            )
            poi_label.pack(anchor=W, pady=1)
            
    def update_trials_list(self, trials):
        # Clear existing
        for widget in self.trials_list_frame.winfo_children():
            widget.destroy()
            
        self.trial_checkboxes = {}
        
        # Add trial items
        for trial in trials:
            trial_widget = TrialWidget(
                self.trials_list_frame,
                trial,
                self.on_trial_selection_changed
            )
            trial_widget.pack(fill=X, pady=2)
            self.trial_checkboxes[trial.id] = trial_widget
            
    def on_trial_selection_changed(self, trial, selected):
        if selected:
            self.callback_handler.trial_selected(trial)
        else:
            self.callback_handler.trial_deselected(trial)
            
        # Update export button
        selected_count = len(self.callback_handler.get_selected_trials())
        if selected_count > 0:
            self.export_button.configure(
                text=f"Export {selected_count} Trial{'s' if selected_count > 1 else ''} to DICOM",
                state=NORMAL
            )
        else:
            self.export_button.configure(
                text="Export 0 Trials to DICOM",
                state=DISABLED
            )
            
    def export_trials(self):
        self.callback_handler.export_selected_trials()

class TrialWidget(ttk.Frame):
    def __init__(self, parent, trial, selection_callback):
        super().__init__(parent)
        self.trial = trial
        self.selection_callback = selection_callback
        self.selected = tk.BooleanVar()
        
        self.configure(relief='solid', borderwidth=1, style='Card.TFrame')
        
        self.setup_ui()
        
    def setup_ui(self):
        # Checkbox
        self.checkbox = ttk.Checkbutton(
            self,
            variable=self.selected,
            command=self.on_selection_changed,
            style='Success.TCheckbutton'
        )
        self.checkbox.pack(side=LEFT, padx=10, pady=10)
        
        # Trial info
        self.info_frame = ttk.Frame(self)
        self.info_frame.pack(side=LEFT, fill=BOTH, expand=True, padx=(0, 10), pady=10)
        
        self.name_label = ttk.Label(
            self.info_frame,
            text=self.trial.name,
            font=('Segoe UI', 11, 'bold')
        )
        self.name_label.pack(anchor=W)
        
        self.details_label = ttk.Label(
            self.info_frame,
            text=f"Beams: {self.trial.beam_count} | Dose: {self.trial.dose} | Fractions: {self.trial.fractions}",
            font=('Segoe UI', 9),
            foreground='gray'
        )
        self.details_label.pack(anchor=W)
        
        # Bind click events
        self.bind("<Button-1>", self.on_click)
        for child in self.winfo_children():
            child.bind("<Button-1>", self.on_click)
            
    def on_click(self, event):
        # Show trial details
        pass
        
    def on_selection_changed(self):
        self.selection_callback(self.trial, self.selected.get())
```

### 3.5 Progress Modal Component (progress_modal.py)

```python
class ProgressModal(tk.Toplevel):
    def __init__(self, parent, title="Processing..."):
        super().__init__(parent)
        self.parent = parent
        self.title(title)
        
        # Modal setup
        self.transient(parent)
        self.grab_set()
        self.resizable(False, False)
        
        # Center on parent
        self.geometry("600x300")
        self.center_on_parent()
        
        # Progress tracking
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar()
        self.detail_var = tk.StringVar()
        self.cancelled = False
        
        self.setup_ui()
        
    def center_on_parent(self):
        self.update_idletasks()
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        x = parent_x + (parent_width // 2) - (600 // 2)
        y = parent_y + (parent_height // 2) - (300 // 2)
        
        self.geometry(f"600x300+{x}+{y}")
        
    def setup_ui(self):
        # Header
        self.header_frame = ttk.Frame(self, style='Primary.TFrame')
        self.header_frame.pack(fill=X)
        
        self.header_label = ttk.Label(
            self.header_frame,
            text="Processing...",
            style='Primary.Inverse.TLabel',
            font=('Segoe UI', 16, 'bold')
        )
        self.header_label.pack(pady=15)
        
        # Content
        self.content_frame = ttk.Frame(self)
        self.content_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        # Status labels
        self.status_label = ttk.Label(
            self.content_frame,
            textvariable=self.status_var,
            font=('Segoe UI', 14)
        )
        self.status_label.pack(anchor=W, pady=(0, 5))
        
        self.detail_label = ttk.Label(
            self.content_frame,
            textvariable=self.detail_var,
            font=('Segoe UI', 12),
            foreground='gray'
        )
        self.detail_label.pack(anchor=W, pady=(0, 20))
        
        # Progress bar
        self.progress_bar = ttk.Progressbar(
            self.content_frame,
            variable=self.progress_var,
            maximum=100,
            length=560,
            style='Success.Horizontal.TProgressbar'
        )
        self.progress_bar.pack(pady=(0, 10))
        
        # Progress percentage
        self.progress_label = ttk.Label(
            self.content_frame,
            text="0%",
            font=('Segoe UI', 10)
        )
        self.progress_label.pack()
        
        # Step info
        self.step_label = ttk.Label(
            self.content_frame,
            text="",
            font=('Segoe UI', 11),
            foreground='gray'
        )
        self.step_label.pack(pady=(20, 0))
        
        # Cancel button
        self.cancel_button = ttk.Button(
            self.content_frame,
            text="Cancel",
            style='Secondary.TButton',
            command=self.cancel_operation
        )
        self.cancel_button.pack(pady=(20, 0))
        
    def update_progress(self, progress, status=None, detail=None, step=None):
        self.progress_var.set(progress)
        self.progress_label.configure(text=f"{int(progress)}%")
        
        if status:
            self.status_var.set(status)
        if detail:
            self.detail_var.set(detail)
        if step:
            self.step_label.configure(text=step)
            
        self.update()
        
    def cancel_operation(self):
        self.cancelled = True
        self.cancel_button.configure(text="Cancelling...", state=DISABLED)
        
    def close_modal(self):
        self.grab_release()
        self.destroy()
```

## 4. Main Application Implementation (main_window.py)

```python
class MainWindow(ttk_bs.Window):
    def __init__(self):
        super().__init__(themename="litera")
        self.title("DICOM Converter - Patient Data Processing")
        self.geometry("1200x800")
        self.minsize(1000, 600)
        
        # Data management
        self.patients = []
        self.selected_patient = None
        self.selected_plan = None
        self.selected_trials = set()
        
        # Threading
        self.task_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.current_task = None
        
        self.setup_ui()
        self.setup_styles()
        self.setup_bindings()
        self.start_queue_processor()
        
    def setup_ui(self):
        # Title bar
        self.title_frame = ttk.Frame(self, style='Primary.TFrame')
        self.title_frame.pack(fill=X, side=TOP)
        self.title_bar = TitleBarComponent(self.title_frame)
        self.title_bar.pack(fill=BOTH, expand=True)
        
        # Header
        self.header_frame = ttk.Frame(self, style='Secondary.TFrame')
        self.header_frame.pack(fill=X, side=TOP)
        self.header_panel = HeaderPanelComponent(self.header_frame, self)
        self.header_panel.pack(fill=BOTH, expand=True)
        
        # Main container
        self.main_container = ttk.Frame(self)
        self.main_container.pack(fill=BOTH, expand=True)
        
        # Patient panel (left)
        self.patient_frame = ttk.Frame(self.main_container, width=350)
        self.patient_frame.pack(fill=Y, side=LEFT)
        self.patient_frame.pack_propagate(False)
        self.patient_panel = PatientPanelComponent(self.patient_frame, self)
        self.patient_panel.pack(fill=BOTH, expand=True)
        
        # Separator
        self.separator = ttk.Separator(self.main_container, orient=VERTICAL)
        self.separator.pack(fill=Y, side=LEFT, padx=2)
        
        # Main content panel (right)
        self.content_frame = ttk.Frame(self.main_container)
        self.content_frame.pack(fill=BOTH, expand=True, side=RIGHT)
        self.main_panel = MainPanelComponent(self.content_frame, self)
        self.main_panel.pack(fill=BOTH, expand=True)
        
    def setup_styles(self):
        style = ttk_bs.Style()
        
        # Custom styles
        style.configure('Primary.TFrame', background='#0d6efd')
        style.configure('Primary.Inverse.TLabel', 
                       background='#0d6efd', 
                       foreground='white')
        style.configure('Secondary.TFrame', background='#e9ecef')
        style.configure('Card.TFrame', background='white', relief='solid')
        
    def setup_bindings(self):
        self.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def start_queue_processor(self):
        """Start the queue processor for handling results from background tasks"""
        self.after(100, self.process_queue)
        
    def process_queue(self):
        """Process items from the result queue"""
        try:
            while True:
                result = self.result_queue.get_nowait()
                self.handle_task_result(result)
        except queue.Empty:
            pass
        finally:
            self.after(100, self.process_queue)
            
    # Callback handlers
    def load_data_source(self, source_path, source_type):
        """Handle loading patient data from various sources"""
        self.show_progress_modal("Loading Patient Data...")
        
        # Start background task
        task = threading.Thread(
            target=self.load_data_background,
            args=(source_path, source_type),
            daemon=True
        )
        task.start()
        
    def load_data_background(self, source_path, source_type):
        """Background task for loading patient data"""
        try:
            # Update progress
            self.result_queue.put({
                'type': 'progress',
                