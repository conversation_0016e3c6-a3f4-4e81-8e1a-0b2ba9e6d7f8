# Simple DICOM Converter

A Python application for converting Pinnacle text files into standardized DICOM files for medical imaging and radiation therapy.

## Features

- Converts Pinnacle files to four DICOM types:
  - CT Images (CT)
  - RT Structure Sets
  - RT Plans
  - RT Dose
- Modern GUI interface with CT visualization
- Support for multiple patient positions (HFS, HFP, FFS, FFP)
- ROI and dose overlay visualization
- Progress tracking for long operations

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd simple_dicom_converter
```

2. Create a virtual environment and activate it:
```bash
python -m venv venv
source venv/bin/activate  # On Linux/Mac
# or
venv\Scripts\activate  # On Windows
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Set up environment variables:
```bash
cp .env.example .env
# Edit .env file with your configuration
```

## Usage

Run the converter GUI:
```bash
python run.py
```

### Command Line Arguments
```bash
python run.py [options]

Options:
  --tar TAR     Path to a TAR file to load
  --zip ZIP     Path to a ZIP file to load  
  --dir DIR     Path to a directory to load
  --plan PLAN   Plan to select after loading (plan name or index)
```

### Examples
```bash
# Load a TAR file and automatically select the first plan (index 0)
python run.py --tar data.tar --plan 0

# Load a ZIP file and automatically select a plan by name
python run.py --zip data.zip --plan "BRAIN"

# Load a directory and select the second plan (index 1)
python run.py --dir /path/to/data --plan 1
```

### Legacy Command Line Usage
```bash
python -m dicom_converter.main [patient_folder] [input_folder] [output_folder]
```

## Input Requirements

The converter requires the following Pinnacle files:
- `Patient` (text file under patient folder)
- `plan.Points` (under Patient folder -> plan label folder)
- `plan.roi`
- `plan.Trial`
- `plan.Trial.Binary.xxx` (multiple files)
- `ImageSet_%s.ImageInfo`
- `ImageSet_%s.header`
- Image files

## Project Structure

- `dicom_converter/`: Main package directory
  - `converters/`: DICOM conversion modules
  - `io/`: File reading/writing operations
  - `models/`: Data models for patient, image, plan info
  - `utils/`: Utility functions and constants
- `tests/`: Test files and test data
- `ui/`: User interface components
- `docs/`: Project documentation

## Testing

Run the test suite:
```bash
pytest tests/
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

See LICENSE file for details.