#%% Find Dicom files

import os
import glob
import pydicom
# from ui.main_window import main as launch_main_window

import ttkbootstrap as ttk  # type: ignore
from ui.ct_viewer import CTViewer


# DICOM files created by the createdcm.py script
CREATE_DCM_EXPORT_PATH = 'Q:/Scripts/Pinnacle Dicom Converter/Test Data/000019240 - 9.0 Pelvis Prostate/CreateDcmOutput/CRALEY,ALLEN,13499'

# DICOM files exported directly from Pinnacle
PINN_DCM_EXPORT_PATH = 'Q:/Scripts/Pinnacle Dicom Converter/Test Data/000019240 - 9.0 Pelvis Prostate/PinnacleDicomExport'

# Find all DICOM files in the directories
create_dcm_files = glob.glob(f'{CREATE_DCM_EXPORT_PATH}/*.dcm')
pinn_dcm_files = glob.glob(f'{PINN_DCM_EXPORT_PATH}/*.dcm')


#%% Check CT slices

# Get CT files from both datasets
create_dcm_ct_files = [os.path.basename(file) for file in create_dcm_files if os.path.basename(file).startswith('CT')]
pinn_dcm_ct_files = [os.path.basename(file) for file in pinn_dcm_files if os.path.basename(file).startswith('CT')]


# Load a matching CT slice from each dataset
extracted_ct_ds = pydicom.dcmread(os.path.join(CREATE_DCM_EXPORT_PATH, create_dcm_ct_files[0]))
original_ct_ds = pydicom.dcmread(os.path.join(PINN_DCM_EXPORT_PATH, pinn_dcm_ct_files[0]))

if len(create_dcm_ct_files) != len(pinn_dcm_ct_files):
    print("❌ CT files do not have the same number of slices")
    print(f"    Extracted CT Slices: {len(create_dcm_ct_files)}")
    print(f"    Original CT Slices: {len(pinn_dcm_ct_files)}")

# Note: Use print statements instead of assert to show all mismatched DICOM tags
def CompareTags(tag_name):
    if extracted_ct_ds[tag_name] != original_ct_ds[tag_name]:
        print(f"❌ CT files do not have the same {tag_name}")
        print("    Extracted:", extracted_ct_ds[tag_name])
        print("    Original: ", original_ct_ds[tag_name])

CompareTags('SOPInstanceUID')
CompareTags('StudyDate')
CompareTags('SeriesDate')
CompareTags('PatientName')
CompareTags('PatientID')
CompareTags('InstanceNumber')
CompareTags('ImagePositionPatient')
CompareTags('ImageOrientationPatient')
CompareTags('FrameOfReferenceUID')
CompareTags('PixelSpacing')


#%% Load the CT slices
# Load all CT slices from the CREATE_DCM_EXPORT_PATH directory
create_dcm_ct_files = [f for f in create_dcm_files if os.path.basename(f).startswith('CT')]
create_dcm_ct_datasets = [pydicom.dcmread(f, force=True) for f in create_dcm_ct_files]

pinn_dcm_ct_files = [f for f in pinn_dcm_files if os.path.basename(f).startswith('CT')]
pinn_dcm_ct_datasets = [pydicom.dcmread(f, force=True) for f in pinn_dcm_ct_files]


#%% Launch CT Viewer with all CT slices from CREATE_DCM_EXPORT_PATH

app = ttk.Window(themename="darkly")
app.title("Radiotherapy DICOM Visualizer")
app.geometry("1900x1080+10+10")  # Set a reasonable default size
if create_dcm_ct_datasets is not None:
    viewer = CTViewer(app, create_dcm_ct_datasets)
    viewer.pack(side='left', fill='both', expand=True)

# Add a second CT Viewer to the right of the first
if pinn_dcm_ct_datasets is not None:
    viewer2 = CTViewer(app, pinn_dcm_ct_datasets)
    viewer2.pack(side='right', fill='both', expand=True)
app.mainloop()

# launch_main_window(create_dcm_ct_datasets, pinn_dcm_ct_datasets)


#%% Check the RT Plan

create_dcm_plan_files = [file for file in create_dcm_files if os.path.basename(file).startswith('RP')]
pinn_dcm_plan_files = [file for file in pinn_dcm_files if os.path.basename(file).startswith('RP')]

