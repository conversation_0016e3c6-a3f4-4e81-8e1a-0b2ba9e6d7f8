"""
Configuration loader for SimpleDicomConverter.

This module provides functions for loading, validating, and initializing
the configuration for the SimpleDicomConverter application.
"""

import os
import sys
import yaml
import logging
from typing import Dict, Any, Optional

# Setup logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", datefmt="%Y-%m-%d %H:%M:%S")
logger = logging.getLogger(__name__)


def get_default_config_path() -> str:
    """
    Get the default path to the configuration file.

    Returns:
        Path to the default configuration file.
    """
    return os.path.join(os.path.dirname(os.path.dirname(__file__)), "config.yaml")


def load_config(config_path: Optional[str] = None) -> Dict[str, Any]:
    """
    Load configuration from a YAML file.

    Args:
        config_path: Path to the configuration file. If None, uses the default path.

    Returns:
        Dictionary containing the configuration.
    """
    if config_path is None:
        config_path = get_default_config_path()

    try:
        with open(config_path, "r") as f:
            config = yaml.safe_load(f)
            logger.info(f"Configuration loaded from {config_path}")
            return config
    except FileNotFoundError:
        logger.error(f"Configuration file not found: {config_path}")
        return {}
    except yaml.YAMLError as e:
        logger.error(f"Error parsing configuration file: {e}")
        return {}


def validate_config(config: Dict[str, Any]) -> bool:
    """
    Validate the configuration.

    Args:
        config: Configuration dictionary to validate.

    Returns:
        True if the configuration is valid, False otherwise.
    """
    # Check for required sections
    required_sections = ["dicom", "equipment", "roi_colors", "pdd_values", "mlc"]
    for section in required_sections:
        if section not in config:
            logger.error(f"Required configuration section '{section}' is missing")
            return False

    # Check for required DICOM settings
    dicom_settings = ["implementation_class_uid", "transfer_syntax_uid", "sop_class_uids"]
    for setting in dicom_settings:
        if setting not in config.get("dicom", {}):
            logger.error(f"Required DICOM setting '{setting}' is missing")
            return False

    # Check for required SOP Class UIDs
    sop_classes = ["rt_structure_set", "rt_plan", "ct_image", "study_component"]
    for sop_class in sop_classes:
        if sop_class not in config.get("dicom", {}).get("sop_class_uids", {}):
            logger.error(f"Required SOP Class UID '{sop_class}' is missing")
            return False

    # Check for required equipment settings
    equipment_settings = ["manufacturer", "station_name"]
    for setting in equipment_settings:
        if setting not in config.get("equipment", {}):
            logger.error(f"Required equipment setting '{setting}' is missing")
            return False

    # Check for required patient position settings
    positions = ["HFS", "HFP", "FFS", "FFP"]
    for position in positions:
        if position not in config.get("patient_position", {}):
            logger.error(f"Required patient position '{position}' is missing")
            return False

    return True


def create_default_config(config_path: Optional[str] = None) -> Dict[str, Any]:
    """
    Create a default configuration file if one doesn't exist.

    Args:
        config_path: Path to the configuration file. If None, uses the default path.

    Returns:
        Dictionary containing the default configuration.
    """
    if config_path is None:
        config_path = get_default_config_path()

    # Default configuration
    default_config = {
        "dicom": {
            "implementation_class_uid": "1.2.826.0.1.3680043.8.498.75006884747854523615841001",
            "transfer_syntax_uid": "1.2.840.10008.1.2",  # ImplicitVRLittleEndian
            "sop_class_uids": {
                "rt_structure_set": "1.2.840.10008.*******.1.481.3",
                "rt_plan": "1.2.840.10008.*******.1.481.5",
                "ct_image": "1.2.840.10008.*******.1.2",
                "study_component": "1.2.840.10008.*******.2",
            },
        },
        "equipment": {"manufacturer": "Pinnacle Philips", "station_name": "adacp3u7", "software_version": "SimpleDicomConverter 1.0"},
        "roi_colors": {
            "red": [255, 0, 0],
            "pink": [255, 20, 147],
            "blue": [0, 0, 255],
            "green": [0, 255, 0],
            "purple": [125, 38, 205],
            "yellow": [255, 255, 0],
            "orange": [255, 140, 0],
            "dark green": [0, 100, 0],
            "sky blue": [0, 191, 255],
            "light pink": [255, 192, 203],
            "turquoise": [72, 209, 204],
            "brown": [139, 69, 19],
            "gold": [255, 193, 37],
            "light purple": [221, 160, 221],
            "olive": [107, 142, 35],
            "brick": [142, 35, 35],
            "peach": [245, 204, 176],
            "light blue": [191, 239, 255],
            "maroon": [139, 28, 98],
            "tomato": [255, 99, 71],
        },
        "pdd_values": {"6MV": 0.6683, "10MV": 0.6683, "15MV": 0.7658, "16MV": 0.7658},  # Placeholder value
        "mlc": {
            "leaf_jaw_pairs": 60,
            "leaf_position_boundaries": [
                -200,
                -190,
                -180,
                -170,
                -160,
                -150,
                -140,
                -130,
                -120,
                -110,
                -100,
                -95,
                -90,
                -85,
                -80,
                -75,
                -70,
                -65,
                -60,
                -55,
                -50,
                -45,
                -40,
                -35,
                -30,
                -25,
                -20,
                -15,
                -10,
                -5,
                0,
                5,
                10,
                15,
                20,
                25,
                30,
                35,
                40,
                45,
                50,
                55,
                60,
                65,
                70,
                75,
                80,
                85,
                90,
                95,
                100,
                110,
                120,
                130,
                140,
                150,
                160,
                170,
                180,
                190,
                200,
            ],
        },
        "ct_image": {
            "bits_allocated": 16,
            "bits_stored": 16,
            "high_bit": 15,
            "pixel_representation": 1,
            "rescale_intercept": -1024,
            "rescale_slope": 1.0,
            "spatial_resolution": 0.35,
            "gantry_detector_tilt": 0.0,
            "table_height": -158.0,
            "rotation_direction": "CW",
            "exposure_time": 1000,
            "xray_tube_current": 398,
            "generator_power": 48,
            "focal_spots": 1.2,
            "convolution_kernel": "STND",
        },
        "dose": {"default_dose_rate": 400},
        "patient_position": {
            "HFS": {  # Head First Supine
                "x_transform": 1.0,
                "y_transform": -1.0,
                "z_transform": -1.0,
                "image_orientation": [1.0, 0.0, 0.0, 0.0, 1.0, -0.0],
            },
            "HFP": {  # Head First Prone
                "x_transform": -1.0,
                "y_transform": 1.0,
                "z_transform": -1.0,
                "image_orientation": [-1.0, 0.0, 0.0, 0.0, -1.0, -0.0],
            },
            "FFS": {  # Feet First Supine
                "x_transform": -1.0,
                "y_transform": -1.0,
                "z_transform": 1.0,
                "image_orientation": [1.0, 0.0, 0.0, 0.0, 1.0, -0.0],
            },
            "FFP": {  # Feet First Prone
                "x_transform": 1.0,
                "y_transform": 1.0,
                "z_transform": 1.0,
                "image_orientation": [-1.0, 0.0, 0.0, 0.0, -1.0, -0.0],
            },
        },
        "paths": {"default_input": "", "default_output": ""},
        "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "date_format": "%Y-%m-%d %H:%M:%S"},
    }

    try:
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(config_path), exist_ok=True)

        # Write the default configuration to file
        with open(config_path, "w") as f:
            yaml.dump(default_config, f, default_flow_style=False)
            logger.info(f"Default configuration created at {config_path}")
    except Exception as e:
        logger.error(f"Error creating default configuration: {e}")

    return default_config


def initialize_config(config_path: Optional[str] = None) -> Dict[str, Any]:
    """
    Initialize the configuration by loading it from file or creating a default.

    Args:
        config_path: Path to the configuration file. If None, uses the default path.

    Returns:
        Dictionary containing the configuration.
    """
    if config_path is None:
        config_path = get_default_config_path()

    # Try to load the configuration
    config = load_config(config_path)

    # If the configuration is empty or invalid, create a default one
    if not config or not validate_config(config):
        logger.warning("Configuration is missing or invalid, creating default")
        config = create_default_config(config_path)

    return config


if __name__ == "__main__":
    # If this module is run directly, initialize the configuration
    config_path = sys.argv[1] if len(sys.argv) > 1 else None
    config = initialize_config(config_path)

    if validate_config(config):
        logger.info("Configuration is valid")
    else:
        logger.error("Configuration is invalid")
