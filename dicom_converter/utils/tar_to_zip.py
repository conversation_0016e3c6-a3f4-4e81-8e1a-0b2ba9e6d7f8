"""
tar_to_zip.py

Given an input path or glob pattern, find all *.tar or *.tar.gz files and
extract files relevant to the DICOM conversion process to *.zip files. These
files include:

Institution
Institution_#/Mount_0/Patient_#/Patient
Institution_#/Mount_0/Patient_#/ImageSet_#.header
Institution_#/Mount_0/Patient_#/ImageSet_#.ImageInfo
Institution_#/Mount_0/Patient_#/ImageSet_#.DICOM/*
Institution_#/Mount_0/Patient_#/Plan_#/plan.Trial
Institution_#/Mount_0/Patient_#/Plan_#/plan.Trial.binary.#
Institution_#/Mount_0/Patient_#/Plan_#/plan.Points
Institution_#/Mount_0/Patient_#/Plan_#/plan.roi
Institution_#/Mount_0/Patient_#/Plan_#/plan.PatientSetup
Institution_#/Mount_0/Patient_#/Plan_#/plan.Pinnacle.Machines

Data will be stored to a zip file in the specified output directory.

    OutputFolder/ArchiveName.zip

Usage:

    tar_to_zip.py --input-archive /path/to/input/ArchiveName.tar.gz --output-path /path/to/output
    tar_to_zip.py --input-path /path/to/input --output-path /path/to/output
"""

import os
import glob
import logging
import tarfile
import zipfile
import datetime
from logging.handlers import RotatingFileHandler

from pinnacle_io.readers import (
    InstitutionReader,
    PatientReader,
    TrialReader,
)
from dicom_converter.utils.tar_utils import (
    verify_pinnacle_tarfile,
    fix_corrupt_tar,
    recover_tar_to_new_file,
)

from pinnacle_io.models.institution import Institution, PatientLite
from pinnacle_io.models.patient import Patient

DECODE_TYPE = "latin1"


# Configure logging
def setup_logging():
    """Configure logging to output to both terminal and a time-stamped log file"""
    # Create logs directory if it doesn't exist
    log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "logs")
    os.makedirs(log_dir, exist_ok=True)

    # Create a timestamp for the log filename
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"tar_to_zip_{timestamp}.log")

    # Set up root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)

    # Clear any existing handlers
    if root_logger.handlers:
        root_logger.handlers.clear()

    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_format = logging.Formatter("%(levelname)s: %(message)s")
    console_handler.setFormatter(console_format)

    # Create file handler
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
    )
    file_handler.setLevel(logging.INFO)
    file_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    file_handler.setFormatter(file_format)

    # Add handlers to logger
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)

    return root_logger


# Initialize logging
logger = setup_logging()


def copy_tar_to_zip(tar_file: tarfile.TarFile, member: tarfile.TarInfo, zip_file: zipfile.ZipFile) -> bool:
    """
    Copy a file from the tar archive to the zip file only if it isn't already part of the zip file.

    Args:
        tar_file: The tarfile.TarFile object to read from
        member: The tarfile.TarInfo member to copy
        zip_file: The zipfile.ZipFile object to write to

    Returns:
        bool: True if the file was copied, False if it was already in the zip file
    """
    # Check if the file is already in the zip file
    if member.name in zip_file.namelist():
        return False

    # Copy the file from the tar to the zip
    tar_member = tar_file.extractfile(member)
    if not tar_member:
        logger.warning(f"Failed to extract file {member.name} from tar file")
        return False

    zip_file.writestr(member.name, tar_member.read())
    logger.info(f"Copied {member.name} to zip file")
    return True


def get_zip_path(tar_path: str, output_path: str) -> str:
    """
    Generate the output zip file path based on the input tar file path.

    Args:
        tar_path (str): The path to the tar file.
        output_path (str): The directory to save the zip file to.

    Returns:
        str: The path to the output zip file.
    """
    tar_file_name = os.path.basename(tar_path)
    tar_file_base = os.path.splitext(tar_file_name)[0]  # May split .tar or .gz
    if tar_file_base.endswith(".tar"):
        tar_file_base = os.path.splitext(tar_file_base)[0]  # To split .tar for .tar.gz files
    return os.path.join(output_path, tar_file_base + ".zip")


def check_existing_zip(zip_path: str, tar_path: str, overwrite: bool, update: bool) -> bool:
    """
    Check if the output zip file already exists and handle according to overwrite/update flags.

    Args:
        zip_path (str): The path to the output zip file.
        tar_path (str): The path to the input tar file (for logging).
        overwrite (bool): Whether to overwrite the existing zip file.
        update (bool): Whether to update the existing zip file.

    Returns:
        bool: True if processing should continue, False if it should be skipped.
    """
    if os.path.exists(zip_path):
        if overwrite:
            logger.info("Deleting and recreating %s", zip_path)
            os.remove(zip_path)
            return True
        elif update:
            logger.info(
                "Updating existing zip file %s with new files from %s",
                zip_path,
                tar_path,
            )
            return True
        else:
            logger.info(
                "Skipping %s: output file %s already exists (use --overwrite to force or --update to add new files)",
                tar_path,
                zip_path,
            )
            return False
    return True


def open_tar_file(tar_path: str) -> tuple[tarfile.TarFile | None, list, bool]:
    """
    Open a tar file and handle potential corruption using a three-step recovery process:
    1. Try to verify and open the tar file directly
    2. If that fails, attempt a quick fix by fixing line endings
    3. If that still fails, perform a full recovery to a new file

    Args:
        tar_path (str): The path to the tar file.

    Returns:
        tuple: (tar_file | None, members, success_flag)
            - tar_file: The opened tarfile.TarFile object or None if failed
            - members: List of members in the tar file or empty list if failed
            - success_flag: True if successful, False if failed
    """

    def try_open_tar(path: str) -> tuple[tarfile.TarFile | None, list, bool]:
        """Helper function to attempt opening a tar file and getting its members."""
        try:
            verify_pinnacle_tarfile(path)
            logger.info(f"Loading tar file {path}...")
            tar_file = tarfile.open(path, "r:*")
            members = tar_file.getmembers()
            # logger.info(f"Successfully opened tar file: {path}")
            return tar_file, members, True
        except Exception as e:
            if "tar_file" in locals():
                tar_file.close()
            logger.warning(f"Failed to open tar file {path}: {str(e)}")
            return None, [], False

    # Step 1: Try to open the original file directly
    result = try_open_tar(tar_path)
    if result[2]:  # If successful
        return result

    # Step 2: Try quick fix (CRLF -> LF)
    logger.info(f"Attempting quick fix for corrupt tar file: {tar_path}")
    success, error_msg = fix_corrupt_tar(tar_path)
    if success:
        result = try_open_tar(tar_path)
        if result[2]:  # If successful after quick fix
            return result

    # Step 3: Try full recovery to a new file
    logger.info(f"Attempting full recovery of tar file: {tar_path}")
    success, recovered_path, error_msg = recover_tar_to_new_file(tar_path)
    if success and recovered_path:
        result = try_open_tar(recovered_path)
        if result[2]:  # If successful after full recovery
            # Keep the recovered file for future use
            logger.info(f"Successfully recovered tar file to: {recovered_path}")
            return result
        # Clean up the recovered file if we couldn't open it
        try:
            os.remove(recovered_path)
        except OSError:
            pass

    logger.error(f"All recovery attempts failed for tar file: {tar_path}")
    return None, [], False


def get_institution_data(tar_file: tarfile.TarFile, members: list, tar_path: str) -> tuple[tarfile.TarInfo | None, Institution | None, int, bool]:
    """
    Extract and parse the Institution file from the tar archive.

    Args:
        tar_file (tarfile.TarFile): The opened tar file.
        members (list): List of members in the tar file.
        tar_path (str): The path to the tar file (for logging).

    Returns:
        tuple: (institution_member, institution, institution_id, success_flag)
            - institution_member: The TarInfo object for the Institution file
            - institution: The parsed Institution object
            - institution_id: The institution ID
            - success_flag: True if successful, False if failed
    """
    institution_member = next((m for m in members if m.name == "Institution"), None)
    if institution_member is None:
        logger.error("Institution file not found in tar file: %s", tar_path)
        return None, None, 0, False

    institution_file = tar_file.extractfile(institution_member)
    if institution_file is None:
        logger.error("Failed to extract institution file from tar file: %s", tar_path)
        return None, None, 0, False

    institution_content = institution_file.read().decode(DECODE_TYPE)
    institution = InstitutionReader.parse(institution_content)
    institution_id = institution.institution_id
    success = institution_id is not None
    if success:
        logger.info(f"Loaded Institution_{institution_id}")
    else:
        logger.error("Institution ID not found in institution file: %s", tar_path)
    return institution_member, institution, institution_id, success


def get_patient_data(
    tar_file: tarfile.TarFile,
    members: list,
    patient_lite: PatientLite,
    institution_id: int,
) -> tuple[tarfile.TarInfo | None, Patient | None, int, list, bool]:
    """
    Extract and parse the Patient file for a specific patient.

    Args:
        tar_file (tarfile.TarFile): The opened tar file.
        members (list): List of members in the tar file.
        patient_lite: The patient lite object from the institution.
        institution_id (int): The institution ID.

    Returns:
        tuple: (patient_member, patient, patient_id, primary_image_set_ids, success_flag)
            - patient_member: The TarInfo object for the Patient file
            - patient: The parsed Patient object
            - patient_id: The patient ID
            - primary_image_set_ids: List of primary image set IDs
            - success_flag: True if successful, False if failed
    """
    patient_id = patient_lite.patient_id
    if patient_id is None:
        logger.error(f"Patient ID not found for patient {patient_lite}")
        return None, None, 0, [], False

    # Read the patient file for the current patient_lite
    patient_path = f"Institution_{institution_id}/Mount_0/Patient_{patient_id}/Patient"
    patient_member = next(
        (m for m in members if m.name == patient_path),
        None,
    )
    if not patient_member:
        logger.error(f"Patient file not found for patient {patient_id}")
        return None, None, 0, [], False

    # Load the patient
    patient_file = tar_file.extractfile(patient_member)
    if patient_file is None:
        logger.error(f"Failed to extract patient file from tar file: {patient_path}")
        return None, None, 0, [], False

    patient_content = patient_file.read().decode(DECODE_TYPE)
    patient = PatientReader.parse(patient_content)
    logger.info(f"Loaded Patient_{patient_id}")

    # Determine which image sets are primary image sets
    primary_image_set_ids = list(set([plan.primary_ct_image_set_id for plan in patient.plan_list]))

    return patient_member, patient, patient_id, primary_image_set_ids, True


def identify_dicom_folders(members: list) -> set:
    """
    Identify all DICOM folders in the tar archive to filter out corresponding .img files.

    Args:
        members (list): List of members in the tar file.

    Returns:
        set: Set of DICOM folder names.
    """
    dicom_folders = set()
    for member in members:
        if member.name.endswith(".DICOM"):
            dicom_folders.add(member.name)
    return dicom_folders


def process_trial_file(tar_file: tarfile.TarFile, member: tarfile.TarInfo, member_name: str) -> list:
    """
    Process a plan.Trial file to extract binary dose file paths.

    Args:
        tar_file (tarfile.TarFile): The opened tar file.
        member (tarfile.TarInfo): The TarInfo object for the plan.Trial file.
        member_name (str): The name of the member.

    Returns:
        list: List of binary dose file paths.
    """
    binary_dose_files = []
    trial_file = tar_file.extractfile(member)
    if trial_file is None:
        logger.error(f"Failed to extract trial file from tar file: {member_name}")
        return []

    trial_content = trial_file.read().decode(DECODE_TYPE)
    trials = TrialReader.parse(trial_content)
    for trial in trials:
        for beam in trial.beam_list:
            binary_dose_files.append(member_name.replace("plan.Trial", beam.dose_volume_file))
    return binary_dose_files


def categorize_members(
    members: list,
    primary_image_set_ids: list,
    tar_file: tarfile.TarFile,
    exclude_ct_data: bool,
) -> tuple[dict, dict, dict, list]:
    """
    Categorize members into image sets, plans, machines, and identify binary dose files.

    Args:
        members (list): List of members in the tar file.
        primary_image_set_ids (list): List of primary image set IDs.
        tar_file (tarfile.TarFile): The opened tar file.
        exclude_ct_data (bool): Whether to exclude CT data.

    Returns:
        tuple: (image_set_members, plan_members, machine_members, binary_dose_files)
            - image_set_members: Dictionary of image set members
            - plan_members: Dictionary of plan members
            - machine_members: Dictionary of machine members
            - binary_dose_files: List of binary dose file paths
    """
    image_set_members = {}
    plan_members = {}
    machine_members = {}
    binary_dose_files = []

    # First identify all DICOM folders
    dicom_folders = identify_dicom_folders(members)

    # Then categorize members
    for member in members:
        member_name = member.name

        # Process ImageSet files for primary image sets
        if any(f"ImageSet_{img_id}" in member_name for img_id in primary_image_set_ids):
            # Skip CT data if exclude_ct_data is True
            if exclude_ct_data and any(member_name.endswith(ext) for ext in [".DICOM", ".dcm", ".img"]):
                logger.debug(f"Excluding CT data: {member_name}")
                continue

            # Skip .img files that have corresponding .DICOM folders
            if member_name.endswith(".img") and member_name.replace(".img", ".DICOM") in dicom_folders:
                logger.debug(f"Skipping {member_name} as it has a corresponding DICOM folder")
                continue

            if any(member_name.endswith(ext) for ext in [".header", ".ImageInfo", ".img", ".dcm"]):
                image_set_members[member_name] = member

        # Process Plan files
        elif "/Plan_" in member_name:
            plan_files = [
                "plan.Trial",
                "plan.Points",
                "plan.roi",
                "plan.PatientSetup",
                "plan.Pinnacle.Machines",
            ]
            if any(member_name.endswith(plan_file) for plan_file in plan_files) and ".auto." not in member_name:
                plan_members[member_name] = member

                # Extract binary dose file paths from plan.Trial files
                if member_name.endswith("plan.Trial"):
                    binary_dose_files.extend(process_trial_file(tar_file, member, member_name))

        # Process Machine files
        elif "/Machine." in member_name and "Pinnacle.Machine" in member_name:
            machine_members[member_name] = member

    return image_set_members, plan_members, machine_members, binary_dose_files


def collect_dose_file_members(members: list, binary_dose_files: list) -> dict:
    """
    Collect binary dose file members.

    Args:
        members (list): List of members in the tar file.
        binary_dose_files (list): List of binary dose file paths.

    Returns:
        dict: Dictionary of dose file members.
    """
    dose_file_members = {}
    for member in members:
        if member.name in binary_dose_files:
            dose_file_members[member.name] = member
    return dose_file_members


def copy_members_to_zip(tar_file: tarfile.TarFile, zip_file: zipfile.ZipFile, member_dicts: list):
    """
    Copy members from the tar file to the zip file.

    Args:
        tar_file (tarfile.TarFile): The opened tar file.
        zip_file (zipfile.ZipFile): The opened zip file.
        member_dicts (list): List of dictionaries containing members to copy.
    """
    for member_dict in member_dicts:
        for member in member_dict.values():
            copy_tar_to_zip(tar_file, member, zip_file)


def extract_tar(
    tar_path: str,
    output_path: str,
    overwrite: bool = False,
    update: bool = False,
    exclude_ct_data: bool = False,
):
    """
    Extract files from a tar file and save them to a zip file.

    This function will extract the Institution file, patient files, and image set files
    from a tar file and save them to a zip file. The Institution file is first extracted
    and loaded to determine which patients to extract. The patient files for each
    patient are then extracted and loaded to determine which image sets are primary.
    The image set files for each primary image set are then extracted and added to the
    zip file.

    Args:
        tar_path (str): The path to the tar file to extract from.
        output_path (str): The path to save the zip file to. If None or an empty string, it will be the same as the tar file path.
        overwrite (bool): Set to True to overwrite the output file if it already exists.
        update (bool): Set to True to update the output file if it already exists.
        exclude_ct_data (bool): Set to True to exclude ImageSet_#.DICOM directory and ImageSet_#.img files to minimize zip file size.
    """
    # Generate the output zip file path
    if not output_path:
        output_path = os.path.abspath(os.path.dirname(tar_path))
    zip_path = get_zip_path(tar_path, output_path)

    # Check if the zip file already exists and handle according to flags
    if not check_existing_zip(zip_path, tar_path, overwrite, update):
        return

    # Open the tar file and handle potential corruption
    tar_file, members, success = open_tar_file(tar_path)
    if not tar_file or not success:
        # Create an empty zip file to indicate we attempted to process the corrupted tar file
        with zipfile.ZipFile(zip_path, "w") as zip_file:
            # Add a text file explaining the error
            zip_file.writestr(
                "ERROR.txt",
                f"Failed to process corrupted or invalid TAR file: {os.path.basename(tar_path)}",
            )
        logger.warning(f"Created empty ZIP file for corrupted/invalid TAR file: {tar_path}")
        return

    # Use a with statement to ensure the file is properly closed
    with tar_file:
        # Get the Institution file and data
        institution_member, institution, institution_id, success = get_institution_data(tar_file, members, tar_path)
        if not success or not institution_member or not institution or not institution.patient_lite_list:
            # Create an empty zip file to indicate we attempted to process the corrupted tar file
            with zipfile.ZipFile(zip_path, "w") as zip_file:
                # Add a text file explaining the error
                zip_file.writestr(
                    "ERROR.txt",
                    f"Failed to process corrupted or invalid Institution data in TAR file: {os.path.basename(tar_path)}",
                )
            logger.warning(
                f"Created empty ZIP file as the Institution data in TAR file {tar_path} could not be processed. The TAR file may be corrupted."
            )
            return

        # Open the zip file in write mode for new files or append mode for updates
        zip_mode = "a" if update and os.path.exists(zip_path) else "w"
        with zipfile.ZipFile(zip_path, zip_mode, zipfile.ZIP_DEFLATED) as zip_file:
            # Add the Institution file to the zip file
            copy_tar_to_zip(tar_file, institution_member, zip_file)

            # Process each patient
            for patient_lite in institution.patient_lite_list:
                # Get patient data
                patient_member, patient, patient_id, primary_image_set_ids, success = get_patient_data(
                    tar_file, members, patient_lite, institution_id
                )
                if not success or not patient_member or not patient:
                    continue

                # Add the Patient file to the zip file
                copy_tar_to_zip(tar_file, patient_member, zip_file)

                # Categorize members and identify binary dose files
                image_set_members, plan_members, machine_members, binary_dose_files = categorize_members(
                    members, primary_image_set_ids, tar_file, exclude_ct_data
                )

                # Collect binary dose file members
                dose_file_members = collect_dose_file_members(members, binary_dose_files)

                # Copy files to the zip archive
                copy_members_to_zip(
                    tar_file,
                    zip_file,
                    [
                        image_set_members,
                        plan_members,
                        machine_members,
                        dose_file_members,
                    ],
                )


def main(
    input_path: str | None = None,
    output_path: str | None = None,
    input_archive: str | None = None,
    overwrite: bool = False,
    update: bool = False,
    exclude_ct_data: bool = False,
):
    """
    Main function to handle the extraction of tar files to zip files for DICOM conversion.

    This function processes either a specific tar archive or all tar files in a given directory.
    It extracts relevant files for DICOM conversion and saves them as zip files.

    Args:
        input_path (str): Directory containing tar/tar.gz files. Ignored if input_archive is specified.
        output_path (str): Directory where output zip files will be saved.
        input_archive (str): Specific tar/tar.gz file to process. Overrides input_path if specified.
        overwrite (bool): If True, overwrite existing zip files. Defaults to False.
        update (bool): If True, update existing zip files with new files from the tar archive. Defaults to False.
        exclude_ct_data (bool): If True, exclude ImageSet_#.DICOM directory and ImageSet_#.img files to minimize zip file size. Defaults to False.
    """
    # Check if we have a specific archive to process
    if input_archive:
        if os.path.exists(input_archive) and (input_archive.endswith(".tar") or input_archive.endswith(".tar.gz")):
            # If output_path is None, set it to the same directory as the input archive
            if output_path is None:
                output_path = os.path.abspath(os.path.dirname(input_archive))
            extract_tar(input_archive, output_path, overwrite, update, exclude_ct_data)
            return
        else:
            logger.error(f"Specified archive {input_archive} does not exist or is not a tar/tar.gz file")
            return

    # Otherwise process files from the input path
    if not input_path:
        logger.error("No input path specified")
        return
    # If * in input_path, then glob the path for *.tar and *.tar.gz files
    if "*" in input_path:
        tar_paths = glob.glob(input_path + "/*.tar")
        tar_paths += glob.glob(input_path + "/*.tar.gz")
        zip_paths = glob.glob(input_path + "/*.zip")
    else:
        tar_paths = [os.path.join(input_path, f) for f in os.listdir(input_path) if f.endswith(".tar") or f.endswith(".tar.gz")]
        zip_paths = [os.path.join(input_path, f) for f in os.listdir(input_path) if f.endswith(".zip")]

    if not tar_paths:
        logger.error("No tar files found in input directory")
        return

    # If not overwrite or update, then remove all tar_paths which already have a corresponding zip file
    if not (overwrite or update):
        tar_paths = [tar_path for tar_path in tar_paths if tar_path.replace(".tar.gz", ".zip").replace(".tar", ".zip") not in zip_paths]

    # Sort tar paths alphabetically
    tar_paths.sort(key=lambda x: x.lower())

    # Loop through each file in input
    for idx, tar_path in enumerate(tar_paths):
        # Log progress
        logger.info(f"Processing archive {idx + 1} / {len(tar_paths)} ({round((idx + 1) / len(tar_paths) * 100, 1)}%)")

        # If file is a tar file, extract it
        if tar_path.endswith(".tar") or tar_path.endswith(".tar.gz"):
            if not output_path:
                output_path = os.path.abspath(os.path.dirname(tar_path))
            extract_tar(tar_path, output_path, overwrite, update, exclude_ct_data)


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Convert Pinnacle archives to DICOM files")
    parser.add_argument("--input-path", type=str, help="Input directory containing tar/tar.gz files")
    parser.add_argument(
        "--output-path",
        type=str,
        required=False,
        default="",
        help="Output directory for zip files",
    )
    parser.add_argument("--input-archive", type=str, help="Specific tar/tar.gz file to process")
    parser.add_argument("--overwrite", action="store_true", help="Overwrite existing ZIP files")
    parser.add_argument(
        "--update",
        action="store_true",
        help="Update existing ZIP files with new files from the tar archive",
    )
    parser.add_argument(
        "--exclude-ct-data",
        action="store_true",
        help="Exclude ImageSet_#.DICOM directory and ImageSet_#.img files to minimize zip file size",
    )
    args = parser.parse_args()

    # Ensure we have either input_path or input_archive
    if not args.input_path and not args.input_archive:
        parser.error("Either --input-path or --input-archive must be specified")

    main(
        args.input_path,
        args.output_path,
        args.input_archive,
        args.overwrite,
        args.update,
        args.exclude_ct_data,
    )
