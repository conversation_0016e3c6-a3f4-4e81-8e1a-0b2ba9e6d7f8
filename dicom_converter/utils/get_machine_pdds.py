"""
Machine PDDs Extraction Utility for Pinnacle Treatment Planning System

This script extracts photon energy output factors (PDDs) from Pinnacle machine
definition files and exports them to a CSV file for analysis.

IMPORTANT: This script is specifically written for compatibility with Python 2.7
to run on the old Pinnacle Solaris servers. It uses Python 2.7 specific syntax
and libraries, such as:
- dict.itervalues() instead of dict.values()
- Traditional string formatting
- Path handling compatible with Solaris systems

Usage:
    Copy this script to the Solaris server and run with:
    $ python GetMachinePDDs.py

Output:
    Creates a MachinePDDs.csv file in the current directory with machine
    output factors for all institutions and energies.
"""

import os
import re
import glob
import argparse

from dicom_converter.utils.pinnacle_file_to_dict import PinnacleFileToDict

DEFAULT_ROOT_PATH = "/pinnacle_patient_expansion/NewPatients/Institution_*/Physics"


def get_pdds_from_physics_data(physics_path: str) -> list:
    """
    Load PDDs from Pinnacle machine definition files.

    Args:
        physics_path (str): Path to the physics directory. In Pinnacle, the physics
        directory is one level beneath the institution directory. In a Pinnacle archive,
        the physics directory is at the same level as the institution directory.

    Returns:
        list: List of machine output factors for all institutions and energies
    """
    machine_file_pattern = os.path.join(
        physics_path,
        "ReadOnlyMachineDB",
        "Machine.*",
        "Pinnacle.Machine*",
    )
    machine_files = glob.glob(machine_file_pattern)

    institution = ""
    if "Institution_" in physics_path:
        # In Pinnacle, the physics directory is a subfolder of the Institution directory
        institution_match = re.search(r"Institution_\d+", physics_path)
        institution = institution_match.group(0)
    else:
        # In a Pinnacle archive, the physics directory is at the same level as the institution directory
        for inst_path in os.listdir(os.path.dirname(physics_path)):
            if os.path.isdir(os.path.join(os.path.dirname(physics_path), inst_path)) and inst_path.startswith("Institution_"):
                institution = inst_path
                break

    machine_output_factors = []
    for machine_file_path in machine_files:
        machine_id_match = re.search(r"Machine\.\d+", machine_file_path)
        machine_id = machine_id_match.group(0) if machine_id_match else ""
        
        machine = PinnacleFileToDict(machine_file_path)
        photon_energy_list = machine["PhotonEnergyList"]
        for energy in photon_energy_list.values():
            physics_data = energy["PhysicsData"]
            output_factor = physics_data["OutputFactor"]
            dose_cal = output_factor["DosePerMuAtCalibration"]

            machine_output_factors.append(
                (
                    institution,
                    machine_id,
                    os.path.basename(machine_file_path),
                    machine["Name"],
                    machine["VersionTimestamp"],
                    energy["Name"],
                    dose_cal,
                )
            )
    
    return machine_output_factors


def get_pdds_from_plan_machines(plan_path: str) -> list:
    """
    Load PDDs from Pinnacle machine definition files.

    Args:
        plan_path (str): Path to the plan directory, typically at the same
        level as the Institution_#### directory.

    Returns:
        list: List of machine output factors
    """
    institution_match = re.search(r"Institution_\d+", plan_path)
    institution = institution_match.group(0)
    machine_file_path = os.path.join(plan_path, "plan.Pinnacle.Machines")
    machine_list = PinnacleFileToDict(machine_file_path)
    
    machine_output_factors = []
    for i, machine in enumerate(machine_list.values()):
        photon_energy_list = machine["PhotonEnergyList"]
        for energy in photon_energy_list.values():
            physics_data = energy["PhysicsData"]
            output_factor = physics_data["OutputFactor"]
            dose_cal = output_factor["DosePerMuAtCalibration"]

            machine_output_factors.append(
                (
                    institution,
                    f"Machine.{i}",
                    os.path.basename(machine_file_path),
                    machine["Name"],
                    machine["VersionTimestamp"],
                    energy["Name"],
                    dose_cal,
                )
            )
    
    return machine_output_factors


def save_pdds(machine_output_factors, output_path, mode="replace"):
    """
    Save PDDs to CSV. If mode is 'replace', overwrite file. If 'modify', append only new, 
    unique lines (no duplicates, header only once).

    Args:
        machine_output_factors (list): List of tuples to write as CSV rows
        output_path (str): Path to output CSV
        mode (str): 'replace' or 'modify'
    """
    header = "Institution,Machine,Machine File,Machine Name,Version,Energy,Dose Cal\n"
    new_lines = [
        ",".join([str(data) for data in output_factor])
        for output_factor in machine_output_factors
    ]

    if mode == "replace" or not os.path.exists(output_path):
        with open(output_path, "w") as f:
            f.write(header)
            for line in new_lines:
                f.write(line + "\n")
    elif mode == "modify":
        # Read existing lines (excluding header)
        with open(output_path, "r") as f:
            existing_lines = set(line.strip() for i, line in enumerate(f) if i > 0)
        # Only add lines not already present
        unique_new_lines = [line for line in new_lines if line not in existing_lines]
        if unique_new_lines:
            with open(output_path, "a") as f:
                for line in unique_new_lines:
                    f.write(line + "\n")


def main(institution_path: str, output_path: str, mode: str = "replace") -> None:
    machine_output_factors = get_pdds_from_physics_data(institution_path)
    save_pdds(machine_output_factors, output_path, mode)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Extract and save Pinnacle Machine PDDs."
    )
    parser.add_argument(
        "-r",
        "--root",
        type=str,
        default=DEFAULT_ROOT_PATH,
        help="Root path to institution directory (default: %(default)s)",
    )
    parser.add_argument(
        "-o",
        "--output",
        type=str,
        default="MachinePDDs.csv",
        help="Output CSV file path (default: %(default)s)",
    )
    parser.add_argument(
        "-m",
        "--mode",
        type=str,
        choices=["replace", "modify"],
        default="replace",
        help="Save mode: 'replace' (overwrite) or 'modify' (append new, unique lines). Default: %(default)s",
    )
    args = parser.parse_args()
    main(args.root, args.output, args.mode)
