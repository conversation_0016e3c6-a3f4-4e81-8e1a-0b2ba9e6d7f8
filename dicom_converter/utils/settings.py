"""
Settings module for SimpleDicomConverter.

This module loads configuration from the config.yaml file and provides
access to settings throughout the application. It replaces hardcoded
constants from the original createdcm.py script.
"""

import os
import yaml
from typing import Any, Dict, List, Optional, Union

# Default configuration path
DEFAULT_CONFIG_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), "config.yaml")


class Settings:
    """
    Settings class for managing application configuration.

    This class loads settings from a YAML configuration file and provides
    access to those settings throughout the application.
    """

    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize Settings with configuration from a YAML file.

        Args:
            config_path: Path to the configuration file. If None, uses the default path.
        """
        self.config_path = config_path or DEFAULT_CONFIG_PATH
        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """
        Load configuration from the YAML file.

        Returns:
            Dict containing the configuration.
        """
        try:
            with open(self.config_path, "r") as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            print(f"Configuration file not found: {self.config_path}")
            return {}
        except yaml.YAMLError as e:
            print(f"Error parsing configuration file: {e}")
            return {}

    def get(self, key: str, default: Any = None) -> Any:
        """
        Get a configuration value by key.

        Args:
            key: Dot-separated path to the configuration value.
            default: Default value to return if the key is not found.

        Returns:
            The configuration value, or the default if not found.
        """
        keys = key.split(".")
        value = self.config

        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default

        return value

    def set(self, key: str, value: Any) -> None:
        """
        Set a configuration value.

        Args:
            key: Dot-separated path to the configuration value.
            value: Value to set.
        """
        keys = key.split(".")
        config = self.config

        for i, k in enumerate(keys[:-1]):
            if k not in config:
                config[k] = {}
            config = config[k]

        config[keys[-1]] = value

    def save(self, config_path: Optional[str] = None) -> None:
        """
        Save the current configuration to a YAML file.

        Args:
            config_path: Path to save the configuration file. If None, uses the current path.
        """
        save_path = config_path or self.config_path

        try:
            with open(save_path, "w") as f:
                yaml.dump(self.config, f, default_flow_style=False)
        except Exception as e:
            print(f"Error saving configuration file: {e}")


# Create a singleton instance of Settings
settings = Settings()


# Convenience functions for accessing settings
def get_setting(key: str, default: Any = None) -> Any:
    """
    Get a configuration value by key.

    Args:
        key: Dot-separated path to the configuration value.
        default: Default value to return if the key is not found.

    Returns:
        The configuration value, or the default if not found.
    """
    return settings.get(key, default)


def set_setting(key: str, value: Any) -> None:
    """
    Set a configuration value.

    Args:
        key: Dot-separated path to the configuration value.
        value: Value to set.
    """
    settings.set(key, value)


def save_settings(config_path: Optional[str] = None) -> None:
    """
    Save the current configuration to a YAML file.

    Args:
        config_path: Path to save the configuration file. If None, uses the current path.
    """
    settings.save(config_path)


# Specific getters for commonly used settings
def get_dicom_implementation_class_uid() -> str:
    """Get the DICOM Implementation Class UID."""
    return get_setting("dicom.implementation_class_uid", "1.2.826.0.1.3680043.8.498.75006884747854523615841001")


def get_dicom_transfer_syntax_uid() -> str:
    """Get the DICOM Transfer Syntax UID."""
    return get_setting("dicom.transfer_syntax_uid", "1.2.840.10008.1.2")  # ImplicitVRLittleEndian


def get_dicom_sop_class_uid(sop_type: str) -> str:
    """
    Get a DICOM SOP Class UID by type.

    Args:
        sop_type: Type of SOP Class (e.g., 'rt_structure_set', 'rt_plan').

    Returns:
        The SOP Class UID.
    """
    sop_class_uids = {
        "rt_structure_set": "1.2.840.10008.*******.1.481.3",
        "rt_plan": "1.2.840.10008.*******.1.481.5",
        "ct_image": "1.2.840.10008.*******.1.2",
        "study_component": "1.2.840.10008.*******.2",
    }

    return get_setting(f"dicom.sop_class_uids.{sop_type}", sop_class_uids.get(sop_type, ""))


def get_manufacturer() -> str:
    """Get the equipment manufacturer."""
    return get_setting("equipment.manufacturer", "Pinnacle Philips")


def get_station_name() -> str:
    """Get the station name."""
    return get_setting("equipment.station_name", "adacp3rtp")


def get_rgb_colors() -> Dict[str, str]:
    """Get the ROI colors."""
    default_colors = {
        "red": "#FF0000",
        "pink": "#FF1493",
        "blue": "#0000FF",
        "green": "#00FF00",
        "purple": "#7D26CD",
        "yellow": "#FFFF00",
        "orange": "#FF8C00",
        "dark green": "#006400",
        "sky blue": "#00BFFF",
        "light pink": "#FFC0CB",
        "turquoise": "#48D1CC",
        "brown": "#8B4513",
        "gold": "#FFC125",
        "light purple": "#DDA0DD",
        "olive": "#6B8E23",
        "brick": "#8E2323",
        "peach": "#F5CCB0",
        "light blue": "#BFEFFF",
        "maroon": "#8B1C62",
        "tomato": "#FF6347",
        "gray": "#888888",
    }
    return get_setting("roi_colors", default_colors)


def get_hex_colors() -> Dict[str, str]:
    """Get the ROI colors as hex values."""
    rgb_colors = get_rgb_colors()
    hex_colors = {}
    for key, value in rgb_colors.items():
        hex_colors[key] = "#" + "".join([f"{c:02X}" for c in value])
    return hex_colors


def get_mlc_leaf_jaw_pairs() -> int:
    """Get the number of MLC leaf jaw pairs."""
    return get_setting("mlc.leaf_jaw_pairs", 60)


def get_mlc_leaf_position_boundaries() -> List[int]:
    """Get the MLC leaf position boundaries."""
    default_boundaries = [
        -200,
        -190,
        -180,
        -170,
        -160,
        -150,
        -140,
        -130,
        -120,
        -110,
        -100,
        -95,
        -90,
        -85,
        -80,
        -75,
        -70,
        -65,
        -60,
        -55,
        -50,
        -45,
        -40,
        -35,
        -30,
        -25,
        -20,
        -15,
        -10,
        -5,
        0,
        5,
        10,
        15,
        20,
        25,
        30,
        35,
        40,
        45,
        50,
        55,
        60,
        65,
        70,
        75,
        80,
        85,
        90,
        95,
        100,
        110,
        120,
        130,
        140,
        150,
        160,
        170,
        180,
        190,
        200,
    ]

    return get_setting("mlc.leaf_position_boundaries", default_boundaries)


def get_patient_position_transform(position: str) -> Dict[str, Union[float, List[float]]]:
    """
    Get the coordinate transformation for a patient position.

    Args:
        position: Patient position code (e.g., 'HFS', 'HFP', 'FFS', 'FFP').

    Returns:
        Dictionary with transformation parameters.
    """
    default_transforms = {
        "HFS": {"x_transform": 1.0, "y_transform": -1.0, "z_transform": -1.0, "image_orientation": [1.0, 0.0, 0.0, 0.0, 1.0, -0.0]},
        "HFP": {"x_transform": -1.0, "y_transform": 1.0, "z_transform": -1.0, "image_orientation": [-1.0, 0.0, 0.0, 0.0, -1.0, -0.0]},
        "FFS": {"x_transform": -1.0, "y_transform": -1.0, "z_transform": 1.0, "image_orientation": [1.0, 0.0, 0.0, 0.0, 1.0, -0.0]},
        "FFP": {"x_transform": 1.0, "y_transform": 1.0, "z_transform": 1.0, "image_orientation": [-1.0, 0.0, 0.0, 0.0, -1.0, -0.0]},
    }

    return get_setting(f"patient_position.{position}", default_transforms.get(position, {}))


def get_default_dose_rate() -> int:
    """Get the default dose rate."""
    return get_setting("dose.default_dose_rate", 400)


def get_ct_image_parameters() -> Dict[str, Any]:
    """Get the CT image parameters."""
    default_params = {
        "bits_allocated": 16,
        "bits_stored": 16,
        "high_bit": 15,
        "pixel_representation": 1,
        "rescale_intercept": -1024,
        "rescale_slope": 1.0,
        "spatial_resolution": 0.35,
        "gantry_detector_tilt": 0.0,
        "table_height": -158.0,
        "rotation_direction": "CW",
        "exposure_time": 1000,
        "xray_tube_current": 398,
        "generator_power": 48,
        "focal_spots": 1.2,
        "convolution_kernel": "STND",
    }

    return get_setting("ct_image", default_params)


def get_logging_config() -> Dict[str, str]:
    """Get the logging configuration."""
    default_config = {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "date_format": "%Y-%m-%d %H:%M:%S"}

    return get_setting("logging", default_config)


def create_pdd_value_dict(machine_output_factors):
    pdd_data = {}
    for pdd_info in machine_output_factors:
        institution = pdd_info[0]
        machine_name_and_version = f"{pdd_info[3]}: {pdd_info[4]}"
        energy_name = pdd_info[5]
        pdd_value = float(pdd_info[6])
        pdd_key = (institution, machine_name_and_version, energy_name)
        pdd_data[pdd_key] = pdd_value
    return pdd_data


def load_pdd_values() -> Dict[str, str]:
    """Load a dictionary of PDD values from the machine_pdds csv file."""
    # Use the same directory as this constants.py file
    csv_path = get_setting("paths.machine_pdds", "")
    if not os.path.isfile(csv_path):
        base_dir = os.path.dirname(os.path.abspath(__file__))
        csv_path = os.path.join(base_dir, csv_path)

    if not os.path.isfile(csv_path):
        raise FileNotFoundError(f"The machine PDDs file could not be found at {csv_path}")

    with open(csv_path, "r") as file:
        machine_output_factors = [line.strip().split(",") for line in file.readlines()[1:]]  # Skip the header
        return create_pdd_value_dict(machine_output_factors)


# Singleton instance of the PDD Values
PDD_DATA = load_pdd_values()


def get_pdd_value(institution: str, machine_name_and_version: str, energy_name: str) -> float:
    """
    Get the PDD value for a specific institution, machine name and version, and energy name.

    Args:
        institution (str): The institution name.
        machine_name_and_version (str): The machine name and version.
        energy_name (str): The energy name.

    Returns:
        float: The PDD value, or None if the PDD is not found.
    """
    if not PDD_DATA:
        load_pdd_values()
    pdd_key = (institution, machine_name_and_version, energy_name)
    if pdd_key in PDD_DATA:
        return PDD_DATA[pdd_key]

    # Return None if the PDD is not found
    return None
