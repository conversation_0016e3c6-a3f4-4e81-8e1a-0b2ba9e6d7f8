from typing import Optional, Dict
from pydicom.uid import generate_uid


class DicomUidManager:
    """
    Centralized UID manager for DICOM file generation.
    Ensures consistent sharing of StudyInstanceUID, FrameOfReferenceUID, SeriesInstanceUID, and SOPInstanceUIDs
    across all DICOM files (CT, RTSTRUCT, RTPLAN, RTDOSE) for a given export.
    """

    def __init__(self, study_instance_uid: Optional[str] = None):
        """
        Initialize the UID manager. Optionally provide a StudyInstanceUID, otherwise one is generated.
        """
        self.study_instance_uid: str = study_instance_uid or generate_uid()
        self.frame_of_reference_uid: str = generate_uid()
        self._series_uids: Dict[str, str] = {}  # modality -> series UID
        self._sop_instance_uids: Dict[str, Dict[int, str]] = {}  # modality -> {index: sop UID}

    def get_study_instance_uid(self) -> str:
        """Return the StudyInstanceUID for this export."""
        return self.study_instance_uid

    def get_frame_of_reference_uid(self) -> str:
        """Return the FrameOfReferenceUID for this export."""
        return self.frame_of_reference_uid

    def get_series_instance_uid(self, modality: str, index: int = 0) -> str:
        """
        Get or generate a SeriesInstanceUID for a given modality (and optional index for multiple series).
        """
        key = f"{modality}_{index}"
        if key not in self._series_uids:
            self._series_uids[key] = generate_uid()
        return self._series_uids[key]

    def get_sop_instance_uid(self, modality: str, index: int = 0) -> str:
        """
        Get or generate a SOPInstanceUID for a given modality and index (e.g., CT slice, plan, etc).
        """
        if modality not in self._sop_instance_uids:
            self._sop_instance_uids[modality] = {}
        if index not in self._sop_instance_uids[modality]:
            self._sop_instance_uids[modality][index] = generate_uid()
        return self._sop_instance_uids[modality][index]

    def reset(self):
        """
        Reset all UIDs except the StudyInstanceUID and FrameOfReferenceUID.
        Useful for starting a new export within the same session.
        """
        self._series_uids.clear()
        self._sop_instance_uids.clear()
