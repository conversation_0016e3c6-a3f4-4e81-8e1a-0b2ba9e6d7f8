"""
Pinnacle Data Formatter for pymedphys compatibility.

This module provides utilities for formatting pinnacle_io model data into the
dictionary format expected by pymedphys DICOM conversion functions.
"""

from typing import Dict, Any, Optional
from datetime import datetime

from pinnacle_io.models import Patient, Plan, Trial, ImageSet, ROI


class PinnacleDataFormatter:
    """Utilities for formatting pinnacle_io data for pymedphys compatibility."""

    @staticmethod
    def format_patient_data(patient: Patient) -> Dict[str, Any]:
        """
        Format patient data using built-in to_dict() with additional fields for pymedphys.

        Args:
            patient: Patient model from pinnacle_io

        Returns:
            Dictionary formatted for pymedphys compatibility with proper field names
        """
        # Get base dictionary from pinnacle_io model
        patient_dict = patient.to_dict(include_relationships=False)
        
        # Add pymedphys-required fields that may be missing or need transformation
        formatted_dict = patient_dict.copy()
        
        # Ensure FullName is properly formatted for DICOM (Last^First^Middle^)
        if hasattr(patient, 'dicom_name'):
            formatted_dict["FullName"] = patient.dicom_name
        else:
            # Fallback: construct DICOM name format
            first = patient_dict.get("FirstName", "")
            middle = patient_dict.get("MiddleName", "")  
            last = patient_dict.get("LastName", "")
            formatted_dict["FullName"] = f"{last}^{first}^{middle}^"
        
        # Format date of birth for pymedphys (YYYYMMDD string format)
        if "DateOfBirth" in patient_dict and patient_dict["DateOfBirth"]:
            dob = patient_dict["DateOfBirth"]
            if isinstance(dob, datetime):
                formatted_dict["DOB"] = dob.strftime("%Y%m%d")
            else:
                formatted_dict["DOB"] = str(dob)
        else:
            formatted_dict["DOB"] = ""
        
        # Map MedicalRecordNumber to MRN for pymedphys
        formatted_dict["MRN"] = patient_dict.get("MedicalRecordNumber", "")
        
        return formatted_dict

    @staticmethod
    def format_plan_data(plan: Plan) -> Dict[str, Any]:
        """
        Format plan data using built-in to_dict().

        Args:
            plan: Plan model from pinnacle_io

        Returns:
            Dictionary formatted for pymedphys compatibility
        """
        # pinnacle_io Plan.to_dict() doesn't support include_relationships parameter
        plan_dict = plan.to_dict()
        
        # Add derived fields that pymedphys might expect
        formatted_dict = plan_dict.copy()
        
        # Map name to PlanName for consistency
        if "name" in plan_dict:
            formatted_dict["PlanName"] = plan_dict["name"]
        
        # Add primary CT image set ID if available
        if hasattr(plan, 'primary_ct_image_set_id'):
            formatted_dict["PrimaryCTImageSetID"] = plan.primary_ct_image_set_id
        
        return formatted_dict

    @staticmethod
    def format_trial_data(trial: Trial) -> Dict[str, Any]:
        """
        Format trial data using built-in to_dict().

        Args:
            trial: Trial model from pinnacle_io

        Returns:
            Dictionary formatted for pymedphys compatibility
        """
        trial_dict = trial.to_dict()
        
        # Add derived fields that pymedphys might expect
        formatted_dict = trial_dict.copy()
        
        # Map TrialName for consistency
        if hasattr(trial, 'name'):
            formatted_dict["TrialName"] = trial.name
        elif "trial_name" in trial_dict:
            formatted_dict["TrialName"] = trial_dict["trial_name"]
        
        # Add beam information if available
        if hasattr(trial, 'beam_list') and trial.beam_list:
            # Note: We'll need to format beam data separately
            # For now, just indicate presence
            formatted_dict["BeamCount"] = len(trial.beam_list)
            formatted_dict["HasBeams"] = True
        else:
            formatted_dict["BeamCount"] = 0
            formatted_dict["HasBeams"] = False
        
        # Add dose grid information if available
        if hasattr(trial, 'dose_grid') and trial.dose_grid:
            formatted_dict["HasDoseGrid"] = True
        else:
            formatted_dict["HasDoseGrid"] = False
        
        return formatted_dict

    @staticmethod
    def format_image_set_data(image_set: ImageSet) -> Dict[str, Any]:
        """
        Format image set data using built-in to_dict().

        Args:
            image_set: ImageSet model from pinnacle_io

        Returns:
            Dictionary formatted for pymedphys compatibility
        """
        image_dict = image_set.to_dict()
        
        # Add derived fields that pymedphys might expect
        formatted_dict = image_dict.copy()
        
        # Ensure patient position is properly formatted
        if "PatientPosition" in image_dict:
            formatted_dict["PatientPosition"] = image_dict["PatientPosition"]
        
        # Add image dimensions information
        if hasattr(image_set, 'get_image_dimensions'):
            try:
                dims = image_set.get_image_dimensions()
                formatted_dict["ImageDimensions"] = dims
            except Exception:
                pass  # Skip if method fails
        
        # Add pixel spacing information
        if hasattr(image_set, 'get_pixel_spacing'):
            try:
                spacing = image_set.get_pixel_spacing()
                formatted_dict["PixelSpacing"] = spacing
            except Exception:
                pass  # Skip if method fails
        
        return formatted_dict

    @staticmethod
    def format_roi_data(roi: ROI) -> Dict[str, Any]:
        """
        Format ROI data using built-in to_dict().

        Args:
            roi: ROI model from pinnacle_io

        Returns:
            Dictionary formatted for pymedphys compatibility
        """
        roi_dict = roi.to_dict()
        
        # Add derived fields that pymedphys might expect
        formatted_dict = roi_dict.copy()
        
        # Map ROI name for consistency
        if "Name" in roi_dict:
            formatted_dict["ROIName"] = roi_dict["Name"]
        
        # Add contour information if available
        if hasattr(roi, 'curve_list') and roi.curve_list:
            formatted_dict["ContourCount"] = len(roi.curve_list)
            formatted_dict["HasContours"] = True
        else:
            formatted_dict["ContourCount"] = 0
            formatted_dict["HasContours"] = False
        
        return formatted_dict

    @staticmethod
    def _format_datetime_for_dicom(dt: Optional[datetime], default: str = "") -> str:
        """
        Format datetime for DICOM compatibility.

        Args:
            dt: datetime object to format
            default: default value if dt is None

        Returns:
            Formatted datetime string (YYYYMMDD) or default
        """
        if dt is None:
            return default
        
        if isinstance(dt, datetime):
            return dt.strftime("%Y%m%d")
        
        return str(dt)

    @staticmethod
    def validate_required_fields(data_dict: Dict[str, Any], required_fields: list[str]) -> list[str]:
        """
        Validate that required fields are present and non-empty.

        Args:
            data_dict: Dictionary to validate
            required_fields: List of required field names

        Returns:
            List of missing or empty required fields
        """
        missing_fields = []
        
        for field in required_fields:
            if field not in data_dict or not data_dict[field]:
                missing_fields.append(field)
        
        return missing_fields

    @classmethod
    def get_patient_required_fields(cls) -> list[str]:
        """Get list of required patient fields for DICOM export."""
        return ["FirstName", "LastName", "MedicalRecordNumber"]

    @classmethod
    def get_plan_required_fields(cls) -> list[str]:
        """Get list of required plan fields for DICOM export."""
        return ["name", "plan_id"]

    @classmethod
    def get_trial_required_fields(cls) -> list[str]:
        """Get list of required trial fields for DICOM export."""
        return ["TrialName", "trial_id"]

    @classmethod
    def get_image_set_required_fields(cls) -> list[str]:
        """Get list of required image set fields for DICOM export."""
        return ["Modality", "PatientPosition", "XDim", "YDim", "ZDim"]

    @classmethod
    def get_roi_required_fields(cls) -> list[str]:
        """Get list of required ROI fields for DICOM export."""
        return ["Name", "ROINumber"]