"""
Converts a Pinnacle store file to a python ordered dictionary

IMPORTANT: This script is specifically written for compatibility with Python 2.7
to run on the old Pinnacle Solaris servers. It uses Python 2.7 specific syntax
and libraries, such as:
- dict.itervalues() instead of dict.values()
- Traditional string formatting
- Path handling compatible with Solaris systems

Scott Robertson

Department of Radiation Oncology and Molecular Radiation Sciences
The Johns Hopkins University School of Medicine
01/04/2017 - Modified from StoreToScript.py
"""

# import os
import sys
import codecs

try:
    from collections import OrderedDict
except ImportError:
    try:
        from ordereddict import OrderedDict
    except:
        OrderedDict = dict
        

def ConvertValue(val):
    try:
        return int(val)
    except:
        try:
            return float(val)
        except:
            return str(val)
                            
                            
def ProcessPinnacleFile(storeData, lines, i, parentKey='', parentData=None):
    while i < len(lines):
        line = lines[i].strip()
        if not line:
            i += 1
            continue
            
        if line.startswith('/*'):
            i += 1
            while '*/' not in line:
                line = lines[i].strip()
                i += 1
            continue
            
        # Start of nested dict/list
        if line[-1] == '{':
            lineparts = line[:-1].split('=', 1)
            key = lineparts[0].strip()
            #print 'Adding sub-dict/sub-list:', key
            if '[]' in key:
                key = key.replace('[]', '')
                (d, i) = ProcessPinnacleDataBlock(lines, i+1)
                storeData[key] = d
            else:
                (d, i) = ProcessPinnacleFile(OrderedDict(), lines, i+1, key, storeData)
                if key in storeData:
                    storeData[key+'_0'] = storeData.pop(key)
                if key+'_0' in storeData:
                    key += '_' + str(len(storeData))
                storeData[key] = d
                
        # End of nested dict/list
        elif line[-2:] == '};':
            return (storeData, i)
            
        # Evaluate data element
        else:
            key, val = None, None
            if '=' in line and line.endswith(';'):
                lineparts = line.split('=')
                key = lineparts[0].strip()
                val = '='.join(lineparts[1:]).strip().rstrip(';')
            
            elif ':' in line:
                lineparts = line.split(':')
                key = lineparts[0].strip()
                val = ':'.join(lineparts[1:]).strip()
                
            if key is None:
                print('Warning: No key identified for line:', line)
                
            else:
                if val.startswith('"') and val.endswith('"'):
                    storeData[key] = val[1:-1]
                else:
                    storeData[key] = ConvertValue(val)
        
        i += 1
    return (storeData, i)
    
    
def ProcessPinnacleDataBlock(lines, i):
    data = []
    line = lines[i]
    while '};' not in line:
        data.append([float(x) for x in line.strip().split(',') if x])
        i += 1
        line = lines[i]
    return (data, i)
    
    
def PinnacleFileToDict(PinnacleFileName):
    ''' Read a store file saved from Pinnacle '''
    # with open(PinnacleFileName, 'r', encoding='utf-8', errors='ignore') as f:
    with codecs.open(PinnacleFileName, 'r') as f:
        lines = f.readlines()
        # lines = [line.decode('utf-8').strip() for line in f.readlines()]
        return PinnacleLinesToDict(lines)


def PinnacleLinesToDict(lines):
    storeData = OrderedDict()
    i = 0
    while i < len(lines):
        (storeData, i) = ProcessPinnacleFile(storeData, lines, i)
    return storeData
    

def PrintPinnacleFileData(storeData, indent=0):
    if isinstance(storeData, OrderedDict):
        for k, v in storeData.iteritems():
            print('{0}{1} ({2}):'.format('  '*indent, k, type(v)))
            PrintPinnacleFileData(v, indent+1)
    elif isinstance(storeData, list):
        for i, v in enumerate(storeData):
            print('{0}#{1}:'.format('  '*indent, i))
            PrintPinnacleFileData(v, indent+1)
    else:
        print('{0}{1}'.format('  '*indent, storeData))


def Stringify(x):
    try:
        if int(x) == float(x):
            return str(int(x))
    except:
        pass
    return str(x)

def SavePinnacleFile(fileHandler, storeData, indent=0):
    for k, v in storeData.iteritems():
        if isinstance(v, list):
            fileHandler.write('{0}{1}[] ={{\n'.format('  '*indent, k))
            for i, dataRow in enumerate(v):
                dataString = ','.join([Stringify(x) for x in dataRow])
                trailingComma = ',' if i<len(v)-1 else ''
                fileHandler.write('{0}{1}{2}\n'.format('  '*(indent+1), dataString, trailingComma))
            fileHandler.write('{0}}};\n'.format('  '*indent))
        elif isinstance(v, OrderedDict):
            kParts = k.split('_')
            if len(kParts) > 1 and kParts[-1].isdigit():
                k = '_'.join(kParts[:-1])
            fileHandler.write('{0}{1} ={{\n'.format('  '*indent, k))
            SavePinnacleFile(fileHandler, v, indent+1)
            fileHandler.write('{0}}};\n'.format('  '*indent))
        elif isinstance(v, int) or isinstance(v, float) or '\\XDR' in v:
            fileHandler.write('{0}{1} = {2};\n'.format('  '*indent, k, v))
        else:
            fileHandler.write('{0}{1} = "{2}";\n'.format('  '*indent, k, v))
            
if __name__=='__main__':
    if len(sys.argv)<=1:
        sys.exit('Usage: PinnacleFileToScript.py InputPinnacleFile\n')
    
    PinnacleFileName = sys.argv[1]
    print 
    print('Pinnacle File:', PinnacleFileName)
    
    pyObject = PinnacleFileToDict(PinnacleFileName)
    #print '-------------------------------------------------------'
    #print pyObject
    #PrintPinnacleFileData(pyObject)
    
    # if len(sys.argv) > 2:
    #     SavePinnacleFileData(pyObject, sys.argv[2])


