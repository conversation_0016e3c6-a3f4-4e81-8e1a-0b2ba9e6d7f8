"""
tar_recovery.py - Utility for recovering data from damaged tar files

This module provides functionality to recover valid data from corrupt tar files
by scanning for valid tar headers and copying the associated data. It's adapted
from the C program 'ft' (fix tar) with Python-specific optimizations.

The main function is 'recover_damaged_tar' which reads a damaged tar file and
writes a repaired version.
"""

import os
import sys
import logging
from typing import Tuple, Optional
import tarfile
import re

# Configure logging
logger = logging.getLogger(__name__)

def verify_pinnacle_tarfile(inputFile):
    if not tarfile.is_tarfile(inputFile):
        raise ValueError(f'Not a tar file: {inputFile}')

    # Open the tar file and get the table of contents
    with tarfile.open(inputFile) as tf:
        tf_names = tf.getnames()

        # At a minimum, there should be an "Institution" file and an "Institution_#" directory
        if 'Institution' not in tf_names or not any(re.match(r'Institution_\d{1,4}', name) for name in tf_names):
            raise ValueError(f'Tar file missing Institution file and/or Institution_# directory: {inputFile}')
        
        # The last line of the Institution file should end with the checksum comment (ends with */)
        institution_file = tf.extractfile('Institution')
        institution_lines = [line.decode('latin1').strip() for line in institution_file.readlines() if line.strip()]
        if not institution_lines[-1].endswith('*/'):
            raise ValueError(f'Institution file missing checksum comment: {inputFile}')
    

def fix_corrupt_tar(tar_path: str) -> Tuple[bool, Optional[str]]:
    """
    Attempt to fix a corrupt tar file by replacing Windows-style line endings (CRLF) with Unix-style line endings (LF).
    
    This function addresses a common issue with tar files that have been corrupted due to 
    text mode transfers between different operating systems. It reads the file in binary mode,
    replaces all occurrences of CRLF with LF, and writes the result back to the file.
    
    Args:
        tar_path (str): Path to the potentially corrupt tar file
        
    Returns:
        Tuple[bool, Optional[str]]: A tuple containing:
            - bool: True if the file was fixed or is already valid, False otherwise
            - Optional[str]: Error message if an error occurred, None otherwise
    
    Example:
        >>> success, error = fix_corrupt_tar('/path/to/archive.tar')
        >>> if success:
        >>>     print('File is valid or was successfully fixed')
        >>> else:
        >>>     print(f'Failed to fix file: {error}')
    """
    # First check if the file is already a valid tar file
    try:
        verify_pinnacle_tarfile(tar_path)
    except Exception as e:
        logger.warning(f"Corrupt tar file detected: {tar_path}. Error: {str(e)}")
        
        # Attempt to fix the file by replacing CRLF with LF
        try:
            logger.info(f"Attempting to fix corrupt tar file: {tar_path}")
            with open(tar_path, "rb") as f:
                buffer = f.read()
            with open(tar_path, "wb") as f:
                f.write(buffer.replace(b'\r\n', b'\n'))
            
            # Check if the fix worked
            try:
                verify_pinnacle_tarfile(tar_path)
                logger.info(f"Successfully fixed tar file: {tar_path}")
                return True, None
            except Exception as e1:
                logger.error(f"Failed to fix tar file: {tar_path}\nError: {str(e1)}")
                return False, "File could not be fixed by replacing CRLF with LF"
        except Exception as e2:
            logger.error(f"Error attempting to fix tar file: {tar_path}\nError: {str(e2)}")
            return False, f"Error during fix attempt: {str(e2)}"
        

# Magic strings used in tar headers
MAGIC_STRINGS = [
    b'\x00\x00\x00\x00\x00\x00\x00\x00',  # old tar
    b'ustar  \x00',                       # GNU tar
    b'ustar\x0000'                        # ustar/POSIX
]

def round_to_512(size: int) -> int:
    """Round up to the nearest multiple of 512."""
    return size + (512 - (size % 512)) if size % 512 else size

def calculate_checksum(buffer: bytes) -> Tuple[int, int, int]:
    """
    Calculate the checksum of a tar header.
    
    Args:
        buffer: 512-byte tar header block
        
    Returns:
        Tuple containing:
            - The checksum value from the header
            - The calculated signed checksum
            - The calculated unsigned checksum
    """
    # Extract the stored checksum (octal string)
    checksum_field = buffer[148:156]
    try:
        stored_checksum = int(checksum_field.decode('ascii').strip('\x00 '), 8)
    except ValueError:
        return 0, 0, 0
    
    # Calculate checksums (both signed and unsigned)
    signed_sum = 8 * ord(' ')  # Initialize with 8 spaces (checksum field is 8 bytes)
    unsigned_sum = 8 * ord(' ')
    
    # Sum bytes before checksum field
    for i in range(148):
        signed_sum += buffer[i]
        unsigned_sum += buffer[i] & 0xFF
    
    # Skip checksum field (bytes 148-155)
    
    # Sum bytes after checksum field
    for i in range(156, 512):
        signed_sum += buffer[i]
        unsigned_sum += buffer[i] & 0xFF
    
    return stored_checksum, signed_sum, unsigned_sum

def is_valid_header(buffer: bytes) -> bool:
    """
    Check if a 512-byte buffer is a valid tar header.
    
    Args:
        buffer: 512-byte buffer to check
        
    Returns:
        True if the buffer appears to be a valid tar header, False otherwise
    """
    # Quick heuristic test
    if not buffer[0] or buffer[511] != 0 or (buffer[257] != ord('u') and buffer[257] != 0):
        return False
    
    # Check checksum
    stored_checksum, signed_sum, unsigned_sum = calculate_checksum(buffer)
    if stored_checksum != signed_sum and stored_checksum != unsigned_sum:
        return False
    
    # Check magic string
    magic_bytes = buffer[257:265]
    for magic in MAGIC_STRINGS:
        if magic_bytes == magic:
            return True
    
    return False

def get_fullname(header: bytes) -> str:
    """
    Extract the full filename from a tar header.
    
    Args:
        header: 512-byte tar header
        
    Returns:
        The full filename (including prefix if present)
    """
    # Get the name (first 100 bytes)
    name = header[:100].split(b'\x00')[0].decode('utf-8', errors='replace')
    
    # Check for prefix (POSIX/ustar format)
    prefix = b''
    if header[345]:  # If prefix field is used
        prefix = header[345:500].split(b'\x00')[0]
        if prefix:
            prefix = prefix.decode('utf-8', errors='replace') + '/'
        else:
            prefix = ''
    
    return prefix + name

def recover_damaged_tar(input_file: str, output_file: str) -> Tuple[bool, Optional[str]]:
    """
    Recover valid data from a damaged tar file.
    
    This function scans the input file for valid tar headers and copies the
    associated data to the output file. It can recover data even if blocks
    have been moved or corrupted.
    
    Args:
        input_file: Path to the damaged tar file
        output_file: Path where the repaired tar file should be written
        
    Returns:
        Tuple[bool, Optional[str]]: A tuple containing:
            - bool: True if recovery was successful, False otherwise
            - Optional[str]: Error message if an error occurred, None otherwise
    """
    try:
        with open(input_file, 'rb') as infile, open(output_file, 'wb') as outfile:
            # Initialize buffer with zeros
            buffer = bytearray(1024)
            
            # Read first 512 bytes
            bytes_read = infile.read(512)
            if len(bytes_read) < 512:
                return False, "Input file is too small to be a valid tar file"
            
            buffer[512:] = bytes_read
            
            # Process the file
            while True:
                # Read one byte and shift buffer
                c = infile.read(1)
                if not c:  # EOF
                    break
                
                # Shift buffer and add new byte
                buffer[:511] = buffer[1:512]
                buffer[511] = c[0]
                
                # Check if we have a valid header
                if not is_valid_header(buffer[:512]):
                    continue
                
                # Get the header type flag
                header_flag = buffer[156:157]
                
                # Handle extended headers (types other than 0-7, V, g)
                if (header_flag != b'\x00' and 
                    not (b'0' <= header_flag <= b'7') and 
                    header_flag != b'V' and 
                    header_flag != b'g'):
                    
                    # Read extended header data
                    size_field = buffer[124:136]
                    try:
                        data_size = int(size_field.decode('ascii').strip('\x00 '), 8)
                    except ValueError:
                        continue
                    
                    # Calculate total size including header and padding
                    total_size = round_to_512(data_size + 512)
                    
                    # Allocate memory for extended header and data
                    ext_data = bytearray(total_size)
                    ext_data[:512] = buffer[:512]
                    
                    # Read the rest of the extended data
                    remaining = total_size - 512
                    read_data = infile.read(remaining)
                    if len(read_data) < remaining:
                        logger.error("Unexpected end of file while reading extended header data")
                        return False, "Unexpected end of file while reading extended header data"
                    
                    ext_data[512:512+len(read_data)] = read_data
                    
                    # Read next header
                    next_header = infile.read(512)
                    if len(next_header) < 512:
                        logger.error("Unexpected end of file while reading next header after extended header")
                        return False, "Unexpected end of file while reading next header after extended header"
                    
                    # Check if next header is valid
                    buffer[:512] = next_header
                    if not is_valid_header(buffer[:512]):
                        continue
                    
                    # Write extended header and data
                    outfile.write(ext_data)
                
                # Get file size from header
                size_field = buffer[124:136]
                try:
                    file_size = int(size_field.decode('ascii').strip('\x00 '), 8)
                except ValueError:
                    continue
                
                # Write header
                outfile.write(buffer[:512])
                
                # Calculate padding bytes
                fill_bytes = 512 - (file_size % 512) if file_size % 512 else 0
                
                # Read and write file data
                remaining_size = file_size
                while remaining_size > 0:
                    chunk_size = min(remaining_size, 8192)  # Read in chunks
                    chunk = infile.read(chunk_size)
                    
                    if not chunk:  # Unexpected EOF
                        fullname = get_fullname(buffer[:512])
                        logger.error(f"Unexpected end of file at '{fullname}', filling with newlines")
                        
                        # Fill with newlines
                        outfile.write(b'\n' * remaining_size)
                        break
                    
                    outfile.write(chunk)
                    remaining_size -= len(chunk)
                
                # Write padding bytes
                if fill_bytes:
                    outfile.write(b'\0' * fill_bytes)
                
                # Reset buffer
                buffer[:512] = b'\0' * 512
            
            # Write two EOT blocks
            outfile.write(b'\0' * 1024)
            
            return True, None
            
    except Exception as e:
        logger.error(f"Error during tar recovery: {str(e)}")
        return False, f"Error during tar recovery: {str(e)}"

def recover_tar_to_new_file(tar_path: str) -> Tuple[bool, Optional[str], Optional[str]]:
    """
    Recover a damaged tar file to a new file with '_recovered' suffix.
    
    Args:
        tar_path: Path to the damaged tar file
        
    Returns:
        Tuple[bool, Optional[str], Optional[str]]: A tuple containing:
            - bool: True if recovery was successful, False otherwise
            - Optional[str]: Path to the recovered file if successful, None otherwise
            - Optional[str]: Error message if an error occurred, None otherwise
    """
    try:
        # Generate output file path
        base, ext = os.path.splitext(tar_path)
        output_path = f"{base}_recovered{ext}"
        
        # Recover the tar file
        success, error = recover_damaged_tar(tar_path, output_path)
        
        if success:
            logger.info(f"Successfully recovered tar file to: {output_path}")
            return True, output_path, None
        else:
            logger.error(f"Failed to recover tar file: {error}")
            # Clean up the output file if it was created
            if os.path.exists(output_path):
                os.remove(output_path)
            return False, None, error
            
    except Exception as e:
        logger.error(f"Error during tar recovery: {str(e)}")
        return False, None, f"Error during tar recovery: {str(e)}"

def main():
    """
    Command-line interface for the tar recovery utility.
    
    Usage:
        python -m dicom_converter.utils.tar_recovery input.tar output.tar
        
    If output.tar is not specified, input.tar_recovered will be used.
    """
    if len(sys.argv) < 2:
        print("Usage: python -m dicom_converter.utils.tar_recovery input.tar [output.tar]")
        return
    
    input_file = sys.argv[1]
    
    if len(sys.argv) >= 3:
        output_file = sys.argv[2]
    else:
        base, ext = os.path.splitext(input_file)
        output_file = f"{base}_recovered{ext}"
    
    print(f"Recovering {input_file} to {output_file}...")
    success, error = recover_damaged_tar(input_file, output_file)
    
    if success:
        print(f"Successfully recovered tar file to: {output_file}")
    else:
        print(f"Failed to recover tar file: {error}")

if __name__ == "__main__":
    main()
