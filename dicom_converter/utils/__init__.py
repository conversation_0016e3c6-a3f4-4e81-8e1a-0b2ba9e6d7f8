"""
Utility modules for SimpleDicomConverter.

This package contains utility modules for the SimpleDicomConverter application.
"""

from dicom_converter.utils.settings import (
    settings,
    get_setting,
    set_setting,
    save_settings,
    get_dicom_implementation_class_uid,
    get_dicom_transfer_syntax_uid,
    get_dicom_sop_class_uid,
    get_manufacturer,
    get_station_name,
    get_rgb_colors,
    get_hex_colors,
    get_mlc_leaf_jaw_pairs,
    get_mlc_leaf_position_boundaries,
    get_patient_position_transform,
    get_default_dose_rate,
    get_ct_image_parameters,
    create_pdd_value_dict,
    load_pdd_values,
    PDD_DATA,
    get_pdd_value,
)

from dicom_converter.utils.config_loader import load_config, validate_config, create_default_config, initialize_config

from dicom_converter.utils.constants import (
    IMPLEMENTATION_CLASS_UID,
    TRANSFER_SYNTAX_UID,
    RT_STRUCTURE_SET_SOP_CLASS_UID,
    RT_PLAN_SOP_CLASS_UID,
    CT_IMAGE_SOP_CLASS_UID,
    STUDY_COMPONENT_SOP_CLASS_UID,
    MANUFACTURER,
    STATION_NAME,
    RGB_COLORS,
    MLC_LEAF_JAW_PAIRS,
    MLC_LEAF_POSITION_BOUNDARIES,
    DEFAULT_DOSE_RATE,
    CT_IMAGE_PARAMETERS,
)

from .uid_manager import DicomUidManager

__all__ = [
    "settings",
    "get_setting",
    "set_setting",
    "save_settings",
    "get_dicom_implementation_class_uid",
    "get_dicom_transfer_syntax_uid",
    "get_dicom_sop_class_uid",
    "get_manufacturer",
    "get_station_name",
    "get_roi_colors",
    "get_mlc_leaf_jaw_pairs",
    "get_mlc_leaf_position_boundaries",
    "get_patient_position_transform",
    "get_default_dose_rate",
    "get_ct_image_parameters",
    "create_pdd_value_dict",
    "load_pdd_values",
    "PDD_DATA",
    "get_pdd_value",
    "load_config",
    "validate_config",
    "create_default_config",
    "initialize_config",
    "IMPLEMENTATION_CLASS_UID",
    "TRANSFER_SYNTAX_UID",
    "RT_STRUCTURE_SET_SOP_CLASS_UID",
    "RT_PLAN_SOP_CLASS_UID",
    "CT_IMAGE_SOP_CLASS_UID",
    "STUDY_COMPONENT_SOP_CLASS_UID",
    "MANUFACTURER",
    "STATION_NAME",
    "RGB_COLORS",
    "MLC_LEAF_JAW_PAIRS",
    "MLC_LEAF_POSITION_BOUNDARIES",
    "PATIENT_POSITION_TRANSFORMS",
    "DEFAULT_DOSE_RATE",
    "CT_IMAGE_PARAMETERS",
]
