"""
Main module for SimpleDicomConverter.

This module provides the main entry point for the SimpleDicomConverter application.
"""

import os
import sys
import logging
import argparse
from typing import List

from dicom_converter.converters import ImageConverter, StructureConverter, PlanConverter, DoseConverter
from dicom_converter.utils.config_loader import initialize_config
from dicom_converter.utils.uid_manager import DicomUidManager
from ui.services.tar_file_service import TarFileService
from ui.services.zip_file_service import ZipFileService
from ui.services.directory_file_service import DirectoryFileService

# Setup logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", datefmt="%Y-%m-%d %H:%M:%S")
logger = logging.getLogger(__name__)


class DicomConverterApp:
    """
    Main application class for SimpleDicomConverter (Refactored).

    This class will orchestrate the conversion process using the individual converters.
    It is being refactored to:
      - Remove direct file/folder arguments from the constructor
      - Support loading from TAR, ZIP, or directory via class methods
      - Integrate a DicomUidManager for UID sharing across DICOM files
      - Enable batch/model-based workflows
    See docs/dicom_converter_refactor_plan.md for full details.
    """

    def __init__(self):
        """
        Initialize the DICOM converter application (refactored).
        No input arguments; all data loading is handled by class methods.
        """
        self.logger = logging.getLogger(__name__)
        initialize_config()
        # Placeholder for DicomUidManager integration
        self.uid_manager = DicomUidManager()
        # Placeholders for file service and source type
        self.file_service = None
        self.source_type = None

    @classmethod
    def convert_tar(cls, tar_path: str, output_path: str) -> "DicomConverterApp":
        """
        Create a DicomConverterApp instance using a TAR archive as the data source.

        Args:
            tar_path: Path to the TAR archive.
            output_path: Path to the output directory.
        """
        app = cls()
        app.file_service = TarFileService(tar_path)
        app.source_type = "tar"
        app.output_path = output_path
        app.load_data()
        return app

    @classmethod
    def convert_zip(cls, zip_path: str, output_path: str) -> "DicomConverterApp":
        """
        Create a DicomConverterApp instance using a ZIP archive as the data source.

        Args:
            zip_path: Path to the ZIP archive.
            output_path: Path to the output directory.
        """
        app = cls()
        app.file_service = ZipFileService(zip_path)
        app.source_type = "zip"
        app.output_path = output_path
        app.load_data()
        return app

    @classmethod
    def convert_directory(cls, input_path: str, output_path: str) -> "DicomConverterApp":
        """
        Create a DicomConverterApp instance using a directory as the data source.

        Args:
            input_path: Path to the input directory.
            output_path: Path to the output directory.
        """
        app = cls()
        app.file_service = DirectoryFileService(input_path)
        app.source_type = "directory"
        app.output_path = output_path
        app.load_data()
        return app

    def load_data(self):
        """
        Load all patients, plans, and trials from the current file_service (TAR, ZIP, or directory).
        Populates self.data with a list of loaded Patient objects (each with their plans).
        """
        self.logger.info("Loading Institution data using file_service...")
        try:
            institution = self.file_service.load_institution()
            self.logger.info(f"Institution parsed successfully. Found {len(institution.patient_lite_list)} patients.")
            for i, patient in enumerate(self.file_service.load_patients(institution)):
                self.logger.info(
                    f"Loaded patient: {patient.last_and_first_name} (MRN: {patient.medical_record_number}), {len(patient.plan_list)} plans."
                )
                for j, plan in enumerate(patient.plan_list):
                    self.logger.info(f"  Plan {j}: {plan.name} (ID: {plan.plan_id})")
                    planning_ct = self.file_service.load_planning_ct(patient, plan)
                    roi_list = self.file_service.load_roi_list(patient, plan)
                    point_list = self.file_service.load_point_list(patient, plan)
                    trial_list = self.file_service.load_trial_list(patient, plan)
                    for k, trial in enumerate(trial_list):
                        dose = None  # TODO: load dose
                        self.logger.info(f"    Trial {k}: {trial.name} (ID: {trial.trial_id})")
                        self.convert(patient, plan, planning_ct, roi_list, point_list, trial, dose)
        except Exception as e:
            self.logger.error(f"Error loading data: {e}")

    def get_plan_folders(self) -> List[str]:
        """
        Get the list of plan folders for the patient.

        Returns:
            List of plan folder names.
        """
        patient_dir = os.path.join(self.input_dir, self.patient_folder)
        plan_folders = []

        try:
            for item in os.listdir(patient_dir):
                if os.path.isdir(os.path.join(patient_dir, item)) and item.startswith("Plan_"):
                    plan_folders.append(item)
        except FileNotFoundError:
            self.logger.error(f"Patient directory not found: {patient_dir}")
            raise

        return plan_folders

    def get_image_set_number(self) -> str:
        """
        Get the image set number for the patient.

        Returns:
            Image set number as a string.
        """
        patient_dir = os.path.join(self.input_dir, self.patient_folder)

        try:
            with open(os.path.join(patient_dir, "Patient"), "rt", encoding="latin1") as f:
                for line in f:
                    if "PrimaryCTImageSetID =" in line:
                        import re

                        match = re.search(r"[-+]?\d*\.\d+|\d+", line)
                        if match:
                            return match.group(0)
        except FileNotFoundError:
            self.logger.error(f"Patient file not found: {os.path.join(patient_dir, 'Patient')}")
        except Exception as e:
            self.logger.error(f"Error reading Patient file: {e}")

        return "0"  # Default to image set 0

    def convert(self) -> None:
        """
        Convert Pinnacle files to DICOM.
        """
        try:
            # Get image set number
            image_set_number = self.get_image_set_number()
            self.logger.info(f"Using image set number: {image_set_number}")

            # Convert images
            image_converter = ImageConverter(self.input_dir, self.output_dir, self.patient_folder, image_set_number)
            self.logger.info("Converting images...")
            image_converter.convert()

            # Get plan folders
            plan_folders = self.get_plan_folders()
            self.logger.info(f"Found {len(plan_folders)} plan folders: {plan_folders}")

            # Process each plan
            for i, plan_folder in enumerate(plan_folders):
                self.logger.info(f"Processing plan {i+1}/{len(plan_folders)}: {plan_folder}")

                # Convert structures
                structure_converter = StructureConverter(self.input_dir, self.output_dir, self.patient_folder, plan_folder, image_converter)
                self.logger.info(f"Converting structures for plan {plan_folder}...")
                structure_converter.convert()

                # Convert plan
                plan_converter = PlanConverter(self.input_dir, self.output_dir, self.patient_folder, plan_folder, structure_converter)
                self.logger.info(f"Converting plan {plan_folder}...")
                plan_converter.convert()

                # Convert dose
                dose_converter = DoseConverter(self.input_dir, self.output_dir, self.patient_folder, plan_folder, i, plan_converter)
                self.logger.info(f"Converting dose for plan {plan_folder}...")
                dose_converter.convert()

            self.logger.info("Conversion completed successfully")
        except Exception as e:
            self.logger.error(f"Error during conversion: {e}")
            raise


def parse_args() -> argparse.Namespace:
    """
    Parse command line arguments.

    Returns:
        Parsed arguments.
    """
    parser = argparse.ArgumentParser(description="Convert Pinnacle files to DICOM")
    parser.add_argument("patient_folder", help="Name of the patient folder")
    parser.add_argument("input_dir", help="Directory containing the input files")
    parser.add_argument("output_dir", help="Directory where output DICOM files will be saved")

    return parser.parse_args()


def main() -> None:
    """
    Main entry point for the application.
    """
    args = parse_args()

    try:
        converter = DicomConverterApp()
        converter.file_service = DirectoryFileService(args.input_dir)  # Assuming input_dir is the source for now
        converter.source_type = "directory"
        converter.patient_folder = args.patient_folder
        converter.input_dir = args.input_dir
        converter.output_dir = args.output_dir

        converter.load_data()
        converter.convert()
    except Exception as e:
        logger.error(f"Conversion failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
