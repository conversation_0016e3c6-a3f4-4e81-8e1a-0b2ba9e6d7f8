"""
Image converter for SimpleDicomConverter.

This module provides functionality to convert Pinnacle image files to DICOM CT Image files.
It supports both direct file-based conversion from Pinnacle archives and in-memory conversion from loaded models.

Key Classes:
    - ImageConverter: Main class for converting Pinnacle image data to DICOM CT Image.

Typical Usage:
    # Image Converter From Models
    image_converter = ImageConverter(patient)
    image_ds = image_converter.convert(image_set)
    image_converter.save_dataset(image_ds, output_path)

    # Image Converter From Archive
    image_converter = ImageConverter.from_archive(patient_path)
    image_set = image_converter.load_planning_ct(plan_path)
    image_ds = image_converter.convert(image_set)
    image_converter.save_dataset(image_ds, output_path)
"""

import os
import logging
import sys
import argparse
import pydicom
from pydicom.dataset import FileDataset

from dicom_converter.converters.base_converter import PlanBaseConverter

from pinnacle_io.models import Patient, ImageSet
from pinnacle_io.readers import ImageSetReader

from dicom_converter.utils.constants import (
    MODALITY_CT,
    CT_IMAGE_SOP_CLASS_UID,
    IMAGE_TYPE_ORIGINAL_PRIMARY_AXIAL,
    PHOTOMETRIC_INTERPRETATION_MONOCHROME2,
    CT_IMAGE_PARAMETERS,
)

logger = logging.getLogger(__name__)


class ImageConverter(PlanBaseConverter):
    """
    Converter for CT images.

    This class handles the conversion of Pinnacle image files to DICOM CT images. Inherit
    from the PlanBaseConverter to support loading the planning CT for a plan_path.
    """

    MODALITY = MODALITY_CT

    def __init__(self, patient: Patient):
        """
        Initialize the image converter with a Patient model.

        Args:
            patient: Patient model object.
        """
        super().__init__(patient)

    @classmethod
    def from_archive(cls, patient_path: str):
        """
        Create an ImageConverter instance from a Pinnacle archive path.

        Args:
            patient_path: Full path to the patient folder.

        Returns:
            ImageConverter instance initialized with patient data.
        """
        return super().from_archive(patient_path)

    def convert(self, image_set: ImageSet) -> list[FileDataset]:
        """
        Convert Pinnacle image files to DICOM CT images.

        Args:
            image_set: ImageSet to convert.

        Returns:
            FileDataset containing the converted DICOM images.
        """
        try:
            dicom_dir = os.path.join(self.patient_path, f"ImageSet_{image_set.id}.DICOM")

            if os.path.exists(dicom_dir):
                self.logger.info("Converting existing DICOM images")
                ct_datasets = self.convert_existing_images(image_set)
            else:
                self.logger.info("Creating new DICOM images from raw data")
                ct_datasets = self.create_ct_images(image_set)

            self.logger.info("Image conversion completed")
            return ct_datasets
        except Exception as e:
            self.logger.error(f"Error during image conversion: {e}")
            raise

    def convert_existing_images(self, image_set: ImageSet) -> list[FileDataset]:
        """
        Convert existing DICOM images by updating their metadata.

        Args:
            image_set: ImageSet model object containing image metadata.
        """
        dicom_dir = os.path.join(self.patient_path, f"ImageSet_{image_set.id}.DICOM")

        if not os.path.exists(dicom_dir):
            self.logger.warning(f"DICOM directory not found: {dicom_dir}")
            raise FileNotFoundError(f"DICOM directory not found: {dicom_dir}")

        ct_datasets = []
        try:
            for file in os.listdir(dicom_dir):
                if file.endswith(".dcm") or file.endswith(".img"):
                    image_path = os.path.join(dicom_dir, file)
                    ds = pydicom.dcmread(image_path, force=True)

                    # Update patient information
                    ds.PatientName = "{}^{}^{}".format(self.last_name, self.first_name, self.middle_name)
                    ds.PatientID = self.patient_id
                    ds.PatientBirthDate = self.patient_birth_date

                    # Save slice location and UID
                    ct_datasets.append(ds)

            return ct_datasets

        except Exception as e:
            self.logger.error(f"Error converting existing images: {e}")
            raise

    def create_ct_images(self, image_set: ImageSet) -> list[FileDataset]:
        """
        Create a series of DICOM CT image datasets from an ImageSet.

        This consolidated method handles both the creation of individual CT slices and
        the generation of a complete series of images from raw pixel data.

        Args:
            image_set: ImageSet model object containing image metadata.

        Returns:
            List of FileDataset objects representing the CT image series.
        """
        try:
            image_file = os.path.join(self.patient_path, f"ImageSet_{image_set.id}.img")
            ct_datasets = []

            # Read the full image data if needed
            if image_set.pixel_data is None:
                # image_reader = ImageFileReader()
                # image_set = image_reader.read(image_file)
                image_set = ImageSetReader.read_from_path(image_file)

            if image_set is None:
                raise ValueError(f"Image set not found: {image_file}")

            if not image_set.image_info_list:
                raise ValueError(f"Image set has no image info: {image_file}")

            for i, image_info in enumerate(image_set.image_info_list):
                # Create a dataset for this slice
                file_meta = self.create_file_meta(CT_IMAGE_SOP_CLASS_UID, image_info.instance_uid)
                ds = self.create_dataset(file_meta)

                # Set common elements
                self.set_common_elements(ds)

                # Set CT-specific elements
                ds.Modality = MODALITY_CT
                ds.ImageType = IMAGE_TYPE_ORIGINAL_PRIMARY_AXIAL
                ds.InstanceNumber = image_info.slice_number
                ds.SliceLocation = image_info.couch_pos
                ds.ImagePositionPatient = [
                    -image_set.x_pixdim * image_set.x_dim / 2,
                    -image_set.y_pixdim * image_set.y_dim / 2,
                    image_info.couch_pos,
                ]

                # Set patient position and orientation
                # Determine orientation based on patient position
                if "HFS" in image_set.patient_position or "FFS" in image_set.patient_position:
                    ds.ImageOrientationPatient = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]
                elif "HFP" in image_set.patient_position or "FFP" in image_set.patient_position:
                    ds.ImageOrientationPatient = [-1.0, 0.0, 0.0, 0.0, -1.0, 0.0]
                else:
                    # Default orientation if position is unknown
                    ds.ImageOrientationPatient = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]

                # Set pixel-related elements
                ds.SamplesPerPixel = 1
                ds.PhotometricInterpretation = PHOTOMETRIC_INTERPRETATION_MONOCHROME2
                ds.Rows = image_set.x_dim
                ds.Columns = image_set.y_dim
                ds.PixelSpacing = [image_set.x_pixdim, image_set.y_pixdim]
                ds.BitsAllocated = CT_IMAGE_PARAMETERS.get("bits_allocated", 16)
                ds.BitsStored = CT_IMAGE_PARAMETERS.get("bits_stored", 16)
                ds.HighBit = CT_IMAGE_PARAMETERS.get("high_bit", 15)
                ds.PixelRepresentation = CT_IMAGE_PARAMETERS.get("pixel_representation", 1)
                ds.RescaleIntercept = CT_IMAGE_PARAMETERS.get("rescale_intercept", -1024)
                ds.RescaleSlope = CT_IMAGE_PARAMETERS.get("rescale_slope", 1.0)
                ds.SliceThickness = image_set.z_pixdim

                # Set pixel data if available
                if image_set.pixel_data is not None and image_set.pixel_data.shape[0] > i:
                    ds.PixelData = image_set.pixel_data[i].tobytes()

                # Add the dataset to our collection
                ct_datasets.append(ds)

            self.logger.info(f"Created {len(ct_datasets)} CT image slices")
            return ct_datasets

        except Exception as e:
            self.logger.error(f"Error creating CT images: {e}")
            raise


def main(patient_path: str, plan_path: str, image_set_index: int, output_path: str) -> None:
    """
    Main function to run the StructureConverter as a standalone script.

    Args:
        patient_path (str): Full path to the patient folder containing the image files.
        plan_path (str): Full path to the plan folder containing the structure files.
        image_set_index (int): Index of the image set to load for conversion, starting at 0.
        output_path (str): Directory where output DICOM files will be saved.

    Raises:
        SystemExit: If an error occurs during conversion.
    """
    # Initialize the image converter
    if patient_path == "" and plan_path == "":
        raise ValueError("Either the patient path or the plan path must be provided")
    elif patient_path == "":
        patient_path = os.path.dirname(plan_path)

    converter = ImageConverter.from_archive(patient_path)

    # Load the image set header info
    image_set = None
    if plan_path == "" and image_set_index < 0:
        raise ValueError("Either the plan path or the image set index must be provided")
    elif plan_path == "":
        # Get the CT with the given index
        image_set = converter.load_image_set(image_set_index)
    elif image_set_index < 0:
        # Get the planning CT
        image_set = converter.load_planning_ct(plan_path)
    else:
        raise ValueError("Either the plan path or the image set index must be provided (not both)")

    if not image_set:
        raise ValueError("No image set found for conversion")

    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )
    logger = logging.getLogger(__name__)
    logger.info(f"Starting structure conversion for primary image set {image_set.id} in {patient_path}")

    try:
        ct_datasets = converter.convert(image_set)
        for ct_dataset in ct_datasets:
            output_file = converter.save_dataset(ct_dataset, output_path)
            logger.info(f"CT conversion completed. Output file: {output_file}")
    except Exception as e:
        logger.error(f"Error during CT conversion: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    # Create argument parser
    parser = argparse.ArgumentParser(description="Convert Pinnacle CT images to DICOM CT files")
    # Add required arguments
    parser.add_argument(
        "--patient-path",
        "-p",
        required=False,
        default="",
        type=str,
        help="Path to the patient folder",
    )
    parser.add_argument(
        "--plan-path",
        "-f",
        required=False,
        default="",
        type=str,
        help="Path to the plan folder",
    )
    parser.add_argument(
        "--image-set-index",
        "-i",
        required=False,
        type=int,
        default=-1,
        help="Index of the image set to load for conversion, starting at 0",
    )
    parser.add_argument(
        "--output-path",
        "-o",
        required=True,
        type=str,
        help="Path where output DICOM files will be saved",
    )
    # Parse arguments
    args = parser.parse_args()
    # Call main function with parsed arguments
    main(
        patient_path=args.patient_path,
        plan_path=args.plan_path,
        image_set_index=args.image_set_index,
        output_path=args.output_path,
    )
