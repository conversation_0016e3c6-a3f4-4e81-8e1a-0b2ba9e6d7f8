"""
Structure converter for SimpleDicomConverter.

This module provides functionality to convert Pinnacle ROI files to DICOM RT Structure Sets (RTSTRUCT).
It supports both direct file-based conversion from Pinnacle archives and in-memory conversion from loaded models.

Key Classes:
    - StructureConverter: Main class for converting Pinnacle structure data to DICOM RT Structure Set.

Typical Usage:
    # Structure Converter From Models
    structure_converter = StructureConverter(patient)
    structure_set_ds = structure_converter.convert(structure_set, point_set, planning_ct)
    structure_converter.save_dataset(structure_set_ds, output_path)

    # Structure Converter From Archive
    structure_converter = StructureConverter.from_archive(patient_path)
    structure_set = structure_converter.load_structure_set(plan_path)
    point_set = structure_converter.load_point_set(plan_path)
    planning_ct = structure_converter.load_planning_ct(plan_path)
    structure_set_ds = structure_converter.convert(structure_set, point_set, planning_ct)
    structure_converter.save_dataset(structure_set_ds, output_path)

"""

import os
import sys
import argparse
import logging
from pydicom.dataset import Dataset, FileDataset
from pydicom.sequence import Sequence
from pydicom.uid import UID, generate_uid

from dicom_converter.converters.base_converter import TrialBaseConverter

from pinnacle_io.models import ImageSet, Patient, Point, ROI
from pinnacle_io.readers import ImageSetReader, PointReader, ROIReader

from dicom_converter.utils.constants import (
    RT_STRUCTURE_SET_SOP_CLASS_UID,
    MODALITY_RTSTRUCT,
    STUDY_COMPONENT_SOP_CLASS_UID,
    STRUCTURE_SET_NAME_DEFAULT,
    DEFAULT_SERIES_NUMBER,
    APPROVAL_STATUS_UNAPPROVED,
)

logger = logging.getLogger(__name__)


class StructureConverter(TrialBaseConverter):
    """
    Converter for Pinnacle RT Structure Set (RTSTRUCT) DICOM objects.

    This class provides methods to read Pinnacle ROI and image files, apply coordinate shifts, and generate
    a DICOM-compliant RT Structure Set dataset. It supports both archive-based and in-memory workflows,
    and can be used as a command-line tool or as a Python class.

    The converter handles various structure components including:
    - Structure set metadata
    - ROI contours with proper coordinate transformations
    - Reference points
    - Patient position-specific transformations
    - References to the associated image set
    """

    MODALITY = MODALITY_RTSTRUCT

    def __init__(self, patient: Patient) -> None:
        """
        Initialize the structure converter with a Patient model.

        Args:
            patient (Patient): Patient model object.
        """
        # Initialize the base converter with the patient model
        super().__init__(patient)

    @classmethod
    def from_archive(cls, patient_path: str) -> "StructureConverter":
        """
        Create a StructureConverter instance from a Pinnacle archive.

        This method loads the patient data from files in the archive
        and returns a StructureConverter instance that can be used to convert the data
        to DICOM RT Structure Set format.

        Args:
            patient_path (str): Full path to the patient folder in the Pinnacle archive.
                              This should contain the patient's demographic and structure data.

        Returns:
            StructureConverter: Instance initialized with patient data from the archive.
        """
        return super().from_archive(patient_path)

    def load_structure_set(self, plan_path: str) -> list[ROI]:
        """
        Load a structure set from the specified plan path.

        This method reads the plan.roi file from the specified plan path and
        constructs a list of ROI model objects containing all ROI information.

        Args:
            plan_path (str): Full path to the plan folder containing the structure set.

        Returns:
            list[ROI]: The loaded list of ROI objects.

        Raises:
            FileNotFoundError: If the plan path does not exist.
        """
        if not os.path.exists(plan_path):
            self.logger.warning(f"Plan path not found: {plan_path}")
            raise FileNotFoundError(f"Plan path not found: {plan_path}")

        return ROIReader.read_from_path(plan_path)

    def load_point_set(self, plan_path: str) -> list[Point]:
        """
        Load a point set from the specified plan path.

        This method reads the plan.points file from the specified plan path and
        constructs a list of Point model object containing all reference point information.

        Args:
            plan_path (str): Full path to the plan folder containing the point set.

        Returns:
            list[Point]: The loaded list of Point objects.

        Raises:
            FileNotFoundError: If the plan path does not exist.
        """
        if not os.path.exists(plan_path):
            self.logger.warning(f"Plan path not found: {plan_path}")
            raise FileNotFoundError(f"Plan path not found: {plan_path}")

        return PointReader.read_from_path(plan_path)

    def convert(self, roi_list: list[ROI], poi_list: list[Point], image_set: ImageSet) -> FileDataset:
        """
        Convert a Pinnacle structure set to a DICOM RT Structure Set dataset.

        This method loads the primary image information from the plan, calculates the
        coordinate shift based on patient position and image geometry, creates a base
        DICOM RT Structure Set dataset, and adds reference points and ROIs to the
        dataset.

        Args:
            roi_list (list[ROI]): list of ROI model objects containing the structure information.
            poi_list (list[Point]): list of Point model objects containing the reference point information.
            image_set (ImageSet): ImageSet model object containing the image information.

        Returns:
            FileDataset: The converted DICOM RT Structure Set dataset.

        Raises:
            Exception: If any step of the conversion fails.
        """
        try:
            # Calculate the coordinate shift based on patient position and image geometry
            x_shift, y_shift, z_shift = self.calculate_structure_shift(image_set)

            # Create the base DICOM RT Structure Set dataset
            ds: FileDataset = self.create_structure_dataset(image_set)

            # Read and add reference points and ROIs to the dataset
            self.add_points_to_dataset(ds, poi_list)
            self.add_rois_to_dataset(ds, roi_list, x_shift, y_shift, z_shift, image_set)

            self.logger.info("Structure conversion completed")
            return ds
        except Exception as e:
            self.logger.error(f"Error during structure conversion: {e}")
            raise

    def read_image_info(self, image_set: ImageSet) -> None:
        """
        Extract image slice and UID information from the provided ImageSet.

        This method sets the study_instance_uid, series_instance_uid, and frame_of_reference_uid
        attributes based on the provided ImageSet. These UIDs are used to establish proper
        references between the structure set and the images it's associated with.

        Args:
            image_set (ImageSet): ImageSet model object containing the image information.

        Returns:
            None
        """
        if image_set:
            # Extract DICOM reference information from the ImageSet model
            self.study_instance_uid = image_set.study_instance_uid
            self.series_instance_uid = image_set.series_uid
            self.frame_of_reference_uid = image_set.frame_of_reference_uid
        else:
            self.logger.warning("No image_set available to read image info.")

    def calculate_structure_shift(self, image_set: ImageSet) -> tuple[float, float, float]:
        """
        Calculate the DICOM coordinate shifts for the structure set based on image geometry and patient position.
        The shift values are used to align Pinnacle coordinates with DICOM patient coordinates.

        Args:
            image_set (ImageSet): ImageSet model object containing the image information.

        Returns:
            tuple[float, float, float]: Coordinate shifts (x_shift, y_shift, z_shift) in mm.

        Logs a warning and returns zeros if no image_set is provided.
        """
        if not image_set:
            self.logger.warning("No image_set available to calculate structure shift.")
            return 0.0, 0.0, 0.0

        # Extract values from the ImageSet model (dimensions in pixels, spacings and starts in cm)
        x_dim = getattr(image_set, "x_dim", 0)
        y_dim = getattr(image_set, "y_dim", 0)
        z_dim = getattr(image_set, "z_dim", 0)
        x_pixdim = getattr(image_set, "x_pixdim", 0.0)
        y_pixdim = getattr(image_set, "y_pixdim", 0.0)
        z_pixdim = getattr(image_set, "z_pixdim", 0.0)
        x_start = getattr(image_set, "x_start", 0.0)
        y_start = getattr(image_set, "y_start", 0.0)
        z_start = getattr(image_set, "z_start", 0.0)
        patient_position = getattr(image_set, "patient_position", "HFS")

        # Convert all positions from cm to mm (multiply by 10)
        # The shift formulas depend on the patient orientation
        if patient_position == "HFS":
            x_shift = ((x_dim * x_pixdim / 2) + x_start) * 10
            y_shift = -((y_dim * y_pixdim / 2) + y_start) * 10
            z_shift = -((z_dim * z_pixdim / 2) + z_start) * 10
        elif patient_position == "HFP":
            x_shift = -((x_dim * x_pixdim / 2) + x_start) * 10
            y_shift = ((y_dim * y_pixdim / 2) + y_start) * 10
            z_shift = -((z_dim * z_pixdim / 2) + z_start) * 10
        elif patient_position == "FFP":
            x_shift = ((x_dim * x_pixdim / 2) + x_start) * 10
            y_shift = ((y_dim * y_pixdim / 2) + y_start) * 10
            z_shift = ((z_dim * z_pixdim / 2) + z_start) * 10
        elif patient_position == "FFS":
            x_shift = -((x_dim * x_pixdim / 2) + x_start) * 10
            y_shift = -((y_dim * y_pixdim / 2) + y_start) * 10
            z_shift = ((z_dim * z_pixdim / 2) + z_start) * 10
        else:
            self.logger.warning(f"Unknown patient position '{patient_position}', using zero shift.")
            x_shift = y_shift = z_shift = 0.0

        self.logger.info(f"Structure shifts calculated: x={x_shift}, y={y_shift}, z={z_shift}")
        return x_shift, y_shift, z_shift

    def create_structure_dataset(self, image_set: ImageSet) -> FileDataset:
        """
        Create a new DICOM FileDataset for an RT Structure Set (RTSTRUCT).

        This method sets all required DICOM attributes, creates necessary sequences, and references
        the related study, frame of reference, and CT image instances. The resulting dataset is ready
        to be filled with ROI and point information.

        Args:
            image_set (ImageSet): ImageSet model object containing the image information.

        Returns:
            FileDataset: DICOM dataset for the RT Structure Set with all required attributes and sequences.
        """
        # Generate a unique SOP Instance UID for the structure set
        self.sop_instance_uid = generate_uid()

        # Create file meta information and the base dataset
        file_meta = self.create_file_meta(RT_STRUCTURE_SET_SOP_CLASS_UID, self.sop_instance_uid)
        ds = self.create_dataset(file_meta)

        # Set common DICOM attributes (patient, study, etc.)
        self.set_common_elements(ds)

        # Set RT Structure Set-specific DICOM attributes
        ds.Modality = MODALITY_RTSTRUCT
        ds.StructureSetLabel = image_set.series_description
        ds.StructureSetName = STRUCTURE_SET_NAME_DEFAULT
        ds.SeriesNumber = DEFAULT_SERIES_NUMBER
        ds.ApprovalStatus = APPROVAL_STATUS_UNAPPROVED

        # Create and populate DICOM reference sequences
        ds.ReferencedFrameOfReferenceSequence = Sequence()
        ds.ReferencedStudySequence = Sequence()

        # Add a referenced study (study-level reference)
        ref_study = Dataset()
        ds.ReferencedStudySequence.append(ref_study)
        ref_study.ReferencedSOPClassUID = UID(STUDY_COMPONENT_SOP_CLASS_UID)
        ref_study.ReferencedSOPInstanceUID = UID(self.study_instance_uid)

        # Add a referenced frame of reference
        ref_frame = Dataset()
        ds.ReferencedFrameOfReferenceSequence.append(ref_frame)
        ref_frame.FrameOfReferenceUID = UID(self.frame_of_reference_uid)

        # Add RT referenced study sequence (linking to study)
        ref_frame.RTReferencedStudySequence = Sequence()
        rt_ref_study = Dataset()
        ref_frame.RTReferencedStudySequence.append(rt_ref_study)
        rt_ref_study.ReferencedSOPClassUID = UID(STUDY_COMPONENT_SOP_CLASS_UID)
        rt_ref_study.ReferencedSOPInstanceUID = UID(self.study_instance_uid)

        # Add RT referenced series sequence (linking to CT series)
        rt_ref_study.RTReferencedSeriesSequence = Sequence()
        rt_ref_series = Dataset()
        rt_ref_study.RTReferencedSeriesSequence.append(rt_ref_series)
        rt_ref_series.SeriesInstanceUID = UID(self.series_instance_uid)

        # Add ContourImageSequence for each referenced CT slice
        rt_ref_series.ContourImageSequence = Sequence()
        if image_set and hasattr(image_set, "instance_uids"):
            for i, uid in enumerate(image_set.instance_uids):
                contour_image = Dataset()
                rt_ref_series.ContourImageSequence.append(contour_image)
                contour_image.ReferencedSOPClassUID = UID("1.2.840.10008.5.1.4.1.1.2")  # CT Image Storage
                contour_image.ReferencedSOPInstanceUID = UID(uid)

        return ds

    def add_points_to_dataset(self, ds: FileDataset, poi_list: list[Point]) -> None:
        """
        Read reference points from the point_set and add them to the DICOM dataset.

        This method adds points as entries in the StructureSetROISequence and
        RTROIObservationsSequence. Each point is assigned a unique ROI number.

        Args:
            ds (Dataset): DICOM dataset to update with point information.
            poi_list (list[Point]): list of Point model objects containing the reference point information.
        """
        if not poi_list or len(poi_list) == 0:
            self.logger.info("No points available to add to the structure set.")
            return

        # Create empty sequences if not present
        if not hasattr(ds, "ROIContourSequence"):
            ds.ROIContourSequence = Sequence()
        if not hasattr(ds, "StructureSetROISequence"):
            ds.StructureSetROISequence = Sequence()
        if not hasattr(ds, "RTROIObservationsSequence"):
            ds.RTROIObservationsSequence = Sequence()

        # Add each point as a Structure Set ROI and RT ROI Observation
        for idx, point in enumerate(poi_list):
            # StructureSetROISequence: add a reference to the point
            roi = Dataset()
            roi.ROINumber = idx + 1
            roi.ReferencedFrameOfReferenceUID = getattr(self, "frame_of_reference_uid", "")
            roi.ROIName = getattr(point, "name", f"Point_{idx + 1}")
            roi.ObservationNumber = idx + 1
            ds.StructureSetROISequence.append(roi)

            # RTROIObservationsSequence: add observation for the point
            obs = Dataset()
            obs.ObservationNumber = idx + 1
            obs.ReferencedROINumber = idx + 1
            obs.ROIObservationLabel = getattr(point, "name", f"Point_{idx + 1}")
            ds.RTROIObservationsSequence.append(obs)

            # Optionally, add to ROIContourSequence if spatial info is needed
            # (typically, points may not have a contour, but you could add a minimal one if required)

    def add_rois_to_dataset(
        self,
        ds: FileDataset,
        roi_list: list[ROI],
        x_shift: float = 0.0,
        y_shift: float = 0.0,
        z_shift: float = 0.0,
        image_set: ImageSet | None = None,
    ) -> None:
        """
        Read ROI information from the structure_set and add to the DICOM dataset.

        This method populates the ROIContourSequence, StructureSetROISequence, and
        RTROIObservationsSequence with data from the structure_set. It applies coordinate
        transformations based on patient position and the provided shifts.

        Args:
            ds (Dataset): DICOM dataset to update with ROI information.
            roi_list (list[ROI]): list of ROI model objects containing the structure information.
            x_shift (float, optional): X-axis coordinate shift in mm. Defaults to 0.0.
            y_shift (float, optional): Y-axis coordinate shift in mm. Defaults to 0.0.
            z_shift (float, optional): Z-axis coordinate shift in mm. Defaults to 0.0.
            image_set (ImageSet, optional): ImageSet model object containing the image information. Defaults to None.
        """
        if not roi_list or len(roi_list) == 0:
            self.logger.info("No ROIs available to add to the structure set.")
            return

        # Ensure DICOM sequences exist
        if not hasattr(ds, "ROIContourSequence"):
            ds.ROIContourSequence = Sequence()
        if not hasattr(ds, "StructureSetROISequence"):
            ds.StructureSetROISequence = Sequence()
        if not hasattr(ds, "RTROIObservationsSequence"):
            ds.RTROIObservationsSequence = Sequence()

        # Get patient position from image_set if available
        patient_position = getattr(image_set, "patient_position", "HFS") if image_set else "HFS"

        # Populate DICOM dataset with ROI information
        for idx, roi in enumerate(roi_list):
            # StructureSetROISequence
            struct_roi = Dataset()
            struct_roi.ROINumber = idx + 1
            struct_roi.ROIName = getattr(roi, "name", f"ROI_{idx + 1}")
            struct_roi.ReferencedFrameOfReferenceUID = getattr(self, "frame_of_reference_uid", "")
            ds.StructureSetROISequence.append(struct_roi)

            # RTROIObservationsSequence
            obs = Dataset()
            obs.ObservationNumber = idx + 1
            obs.ReferencedROINumber = idx + 1
            obs.ROIObservationLabel = getattr(roi, "name", f"ROI_{idx + 1}")
            ds.RTROIObservationsSequence.append(obs)

            # ROIContourSequence
            contour = Dataset()
            contour.ReferencedROINumber = idx + 1
            contour.ContourSequence = Sequence()

            # Add curves to the contour sequence
            if hasattr(roi, "curves") and roi.curves:
                for curve_idx, curve in enumerate(roi.curves):
                    contour_dataset = Dataset()
                    contour_dataset.ContourGeometricType = getattr(curve, "contour_geometric_type", "CLOSED_PLANAR")
                    contour_dataset.NumberOfContourPoints = curve.point_count

                    # Apply coordinate transformation based on patient position and shifts
                    transformed_points = []
                    for point in curve.points:
                        # Convert from Pinnacle coordinates (cm) to DICOM coordinates (mm) with shifts
                        # Apply different transformations based on patient position
                        x, y, z = point[0], point[1], point[2]

                        # Convert to mm (multiply by 10)
                        x_mm = x * 10
                        y_mm = y * 10
                        z_mm = z * 10

                        # Apply coordinate transformations based on patient position
                        if patient_position == "HFS":
                            transformed_points.extend([x_mm - x_shift, -(y_mm) - y_shift, -(z_mm) - z_shift])
                        elif patient_position == "HFP":
                            transformed_points.extend([-(x_mm) - x_shift, y_mm - y_shift, -(z_mm) - z_shift])
                        elif patient_position == "FFP":
                            transformed_points.extend([x_mm - x_shift, y_mm - y_shift, z_mm - z_shift])
                        elif patient_position == "FFS":
                            transformed_points.extend([-(x_mm) - x_shift, -(y_mm) - y_shift, z_mm - z_shift])
                        else:  # Default to HFS if unknown
                            transformed_points.extend([x_mm - x_shift, -(y_mm) - y_shift, -(z_mm) - z_shift])

                    contour_dataset.ContourData = transformed_points

                    # Find the corresponding image for this contour
                    if image_set and hasattr(image_set, "image_info_list") and hasattr(image_set, "instance_uids"):
                        # Create ContourImageSequence to reference the corresponding image
                        contour_dataset.ContourImageSequence = Sequence()
                        image_ref = Dataset()

                        # Find the closest image slice to the z position of this curve
                        z_pos = getattr(curve, "z_position", None)
                        if z_pos is not None:
                            closest_slice_idx = 0
                            closest_distance = float("inf")

                            for i, img_info in enumerate(image_set.image_info_list):
                                slice_z = getattr(img_info, "z_position", 0)
                                distance = abs(slice_z - z_pos)
                                if distance < closest_distance:
                                    closest_distance = distance
                                    closest_slice_idx = i

                            # Reference the closest image
                            if closest_slice_idx < len(image_set.instance_uids):
                                image_ref.ReferencedSOPClassUID = "1.2.840.10008.5.1.4.1.1.2"  # CT Image Storage
                                image_ref.ReferencedSOPInstanceUID = image_set.instance_uids[closest_slice_idx]
                                contour_dataset.ContourImageSequence.append(image_ref)

                    contour.ContourSequence.append(contour_dataset)

            ds.ROIContourSequence.append(contour)

        self.logger.info(f"Added {len(roi_list)} ROIs to the structure set")


def main(plan_path: str, image_set_index: int, output_path: str) -> None:
    """
    Main function to run the StructureConverter as a standalone script.

    Args:
        plan_path (str): Full path to the plan folder containing the structure files.
        image_set_index (int): Index of the image set to load for conversion, starting at 0.
        output_path (str): Directory where output DICOM files will be saved.

    Raises:
        SystemExit: If an error occurs during conversion.
    """
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )
    logger = logging.getLogger(__name__)
    logger.info(f"Starting structure conversion for primary image set {image_set_index} in {plan_path}")

    try:
        patient_path = os.path.dirname(plan_path)
        converter = StructureConverter.from_archive(patient_path)
        point_set = converter.load_point_set(plan_path)
        structure_set = converter.load_structure_set(plan_path)
        planning_ct = converter.load_planning_ct(plan_path)
        if not planning_ct:
            raise ValueError("No planning CT found")
        structure_ds = converter.convert(structure_set, point_set, planning_ct)
        output_file = converter.save_dataset(structure_ds, output_path)
        logger.info(f"Structure conversion completed. Output file: {output_file}")
    except Exception as e:
        logger.error(f"Error during structure conversion: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    # Create argument parser
    parser = argparse.ArgumentParser(description="Convert Pinnacle structure files to DICOM RT Structure Set files")
    # Add required arguments
    parser.add_argument("--plan-path", "-f", required=True, help="Path to the plan folder")
    parser.add_argument(
        "--image-set-index",
        "-n",
        type=int,
        default=0,
        help="Primary image set index for conversion, starting at 0",
    )
    parser.add_argument(
        "--output-path",
        "-o",
        required=True,
        help="Path where output DICOM files will be saved",
    )
    # Parse arguments
    args = parser.parse_args()
    # Call main function with parsed arguments
    main(
        plan_path=args.plan_path,
        image_set_index=args.image_set_index,
        output_path=args.output_path,
    )
