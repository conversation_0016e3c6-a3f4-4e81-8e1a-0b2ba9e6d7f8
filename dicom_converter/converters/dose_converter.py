"""
Dose converter for SimpleDicomConverter.

This module provides functionality to convert Pinnacle dose files to DICOM RT Dose files (RTDOSE).
It contains the DoseConverter class, which supports several workflows:
    1. From a Pinnacle archive (from_archive)
    2. From a pre-loaded trial (from_trial)
    3. From pre-loaded patient, trial, and dose data (from_dose)

The converter handles all necessary transformations based on patient position and
creates standardized DICOM RT Dose objects following the DICOM standard.

Typical Usage:
    # Dose Converter From Models
    dose_converter = DoseConverter(patient)
    dose_ds = dose_converter.convert(trial, dose)
    dose_converter.save_dataset(dose_ds, output_path)

    # Dose Converter From Archive
    dose_converter = DoseConverter.from_archive(patient_path)
    trial_list = dose_converter.load_trials(plan_path)
    trial = trial_list[trial_index]
    dose = dose_converter.load_dose(plan_path, trial)
    dose_ds = dose_converter.convert(trial, dose)
    dose_converter.save_dataset(dose_ds, output_path)
"""

import logging
import numpy as np
import pydicom
from pydicom.dataset import Dataset, FileDataset
from pydicom.sequence import Sequence
from pydicom.uid import RTDoseStorage, RTPlanStorage, generate_uid

from dicom_converter.converters.base_converter import TrialBaseConverter

from pinnacle_io.models import Dose, Patient, Trial
from pinnacle_io.readers import DoseReader
from pinnacle_io.utils.patient_enum import PatientSetupEnum

from dicom_converter.utils.constants import (
    MODALITY_RTDOSE,
    PHOTOMETRIC_INTERPRETATION_MONOCHROME2,
)

logger = logging.getLogger(__name__)


class DoseConverter(TrialBaseConverter):
    """
    Converter for Pinnacle RT Dose (RTDOSE) DICOM objects.

    This class provides methods to read Pinnacle dose files, apply patient position-specific
    transformations, and generate a DICOM-compliant RT Dose dataset. It supports both archive-based
    and in-memory workflows, and can be used as a command-line tool or as a Python class.
    """

    MODALITY = MODALITY_RTDOSE

    def __init__(self, patient: Patient) -> None:
        """
        Initialize the DoseConverter.

        Args:
            patient (Patient): Patient model object.
        """
        super().__init__(patient)

    @classmethod
    def from_archive(cls, patient_path: str) -> "DoseConverter":
        """
        Create a DoseConverter instance from a Pinnacle archive.

        Args:
            patient_path (str): Full path to the patient folder in the Pinnacle archive.

        Returns:
            DoseConverter: Instance of DoseConverter.
        """
        return super().from_archive(patient_path)

    def load_dose(self, plan_path: str, trial: Trial) -> Dose:
        """
        Load the dose data from the plan files for the current trial.

        Args:
            plan_path (str): Full path to the plan folder containing the trial files.
            trial (Trial): Trial model object associated with the plan.

        Returns:
            Dose: Dose model object containing the loaded dose data.
        """
        # dose_reader = DoseFileReader()
        # dose = dose_reader.read(plan_path, trial)
        # return dose
        return DoseReader.read(plan_path, trial)

    def convert(self, trial: Trial, dose: Dose) -> FileDataset:
        """
        Convert Pinnacle dose files to a DICOM RT Dose dataset.

        Args:
            trial (Trial): The Trial model object containing dose grid and geometry information.
            dose (Dose): The Dose model object containing dose pixel data.

        Returns:
            FileDataset: DICOM RT Dose dataset for the current trial.

        Raises:
            Exception: If any step of the conversion fails.
        """
        try:
            # Create base DICOM dataset for RT dose
            plan_dose_ds = self.create_dose_dataset(trial)

            # Copy and transform dose data based on patient position
            pixel_data = dose.pixel_data
            if pixel_data is None or pixel_data.size == 0:
                raise ValueError("No pixel data found in dose")
            pixel_array = pixel_data.copy()
            pixel_array = self.transform_dose_by_patient_position(pixel_array, trial)

            # Scale dose values to fit in 16-bit range
            max_dose = np.max(pixel_array)
            if max_dose > 0:
                scale = max_dose / 65530.0  # Leave some margin below 65535
                plan_dose_ds.DoseGridScaling = scale
                pixel_array = np.round(pixel_array / scale).astype(np.uint32)
            else:
                plan_dose_ds.DoseGridScaling = 1

            # Set pixel data and dose type
            plan_dose_ds.PixelData = pixel_array.tobytes()
            plan_dose_ds.DoseSummationType = "PLAN"
            self.logger.info("Dose conversion completed")
            return plan_dose_ds
        except Exception as e:
            self.logger.error(f"Error during dose conversion: {e}")
            raise

    @staticmethod
    def transform_dose_by_patient_position(pixel_array: np.ndarray, trial: Trial) -> np.ndarray:
        """
        Apply patient-position-specific transformations to a 1D dose pixel array.

        Args:
            pixel_array (np.ndarray): The 1D dose pixel array to transform.
            trial (Trial): The Trial model object containing dose grid and patient position info.

        Returns:
            np.ndarray: The transformed 1D dose pixel array.

        Raises:
            ValueError: If trial.dose_grid is missing.
        """
        result = pixel_array.copy()
        if not trial.dose_grid:
            raise ValueError("No dose grid information found in trial")

        # Reshape array to 3D using dose grid dimensions
        dimension = trial.dose_grid.dimension
        if not dimension:
            raise ValueError("No dose grid dimension found in trial")
        shape = tuple(int(i) for i in (dimension.z, dimension.y, dimension.x))
        result = result.reshape(shape)

        # Get patient position or default to Unknown
        patient_position = getattr(trial, "patient_position", PatientSetupEnum.Unknown)

        # Apply transformations for standard patient positions
        if patient_position in [
            PatientSetupEnum.HFS,
            PatientSetupEnum.HFP,
            PatientSetupEnum.FFS,
            PatientSetupEnum.FFP,
        ]:
            # Flip array slices and flatten
            flat_array = []
            for z in range(result.shape[0]):
                slice_data = result[z, :, :].flatten()
                flat_array.extend(np.flip(slice_data))
            result = np.array(flat_array)
            result = np.flip(result)
        return result

    def set_dose_position_by_patient_position(self, ds: Dataset, trial: Trial) -> None:
        """
        Set the dose grid's position and orientation in the DICOM dataset based on patient position.
        This ensures correct spatial alignment of the dose with the patient anatomy in DICOM viewers.

        Args:
            ds (Dataset): The DICOM dataset to update.
            trial (Trial): The Trial model object containing dose grid and patient position info.

        Raises:
            ValueError: If trial.dose_grid is missing.
        """
        if not trial.dose_grid:
            raise ValueError("No dose grid information found in trial")

        # Calculate dose grid shifts
        voxel_size = trial.dose_grid.voxel_size
        dimension = trial.dose_grid.dimension
        if not voxel_size or not dimension:
            raise ValueError("No voxel size or dimension found in trial")
        y_dose_shift = float(voxel_size.y) * float(dimension.y)
        z_dose_shift = float(voxel_size.z) * float(dimension.z)

        # Get patient position or default to Unknown
        patient_position = getattr(trial, "patient_position", PatientSetupEnum.Unknown)

        # Get the dose grid origin
        origin = trial.dose_grid.origin
        if not origin:
            raise ValueError("No dose grid origin found in trial")

        # Set position and orientation based on patient setup
        if patient_position == PatientSetupEnum.HFS:
            ds.ImagePositionPatient = [
                float(origin.x),
                float(origin.y) - y_dose_shift,
                float(origin.z) - z_dose_shift,
            ]
            ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        elif patient_position == PatientSetupEnum.HFP:
            ds.ImagePositionPatient = [
                float(origin.x),
                float(origin.y) + y_dose_shift,
                float(origin.z) - z_dose_shift,
            ]
            ds.ImageOrientationPatient = [-1, 0, 0, 0, -1, 0]
        elif patient_position == PatientSetupEnum.FFS:
            ds.ImagePositionPatient = [
                float(origin.x),
                float(origin.y) - y_dose_shift,
                float(origin.z) + z_dose_shift,
            ]
            ds.ImageOrientationPatient = [-1, 0, 0, 0, 1, 0]
        elif patient_position == PatientSetupEnum.FFP:
            ds.ImagePositionPatient = [
                float(origin.x),
                float(origin.y) + y_dose_shift,
                float(origin.z) + z_dose_shift,
            ]
            ds.ImageOrientationPatient = [1, 0, 0, 0, -1, 0]
        else:
            # Default position for unknown setup
            ds.ImagePositionPatient = [
                float(origin.x),
                float(origin.y) - y_dose_shift,
                float(origin.z) - z_dose_shift,
            ]
            ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]

        # Set frame of reference
        ds.FrameOfReferenceUID = self.frame_of_reference_uid
        ds.PositionReferenceIndicator = ""

    def create_dose_dataset(self, trial: Trial) -> FileDataset:
        """
        Create a new DICOM FileDataset for an RT Dose (RTDOSE).

        Args:
            trial (Trial): The Trial model object containing dose grid and geometry information.

        Returns:
            FileDataset: DICOM dataset for the RT Dose.

        Raises:
            ValueError: If trial.dose_grid is missing.
        """
        # Generate UIDs for the dataset
        sop_instance_uid = generate_uid()
        dose_series_uid = generate_uid()

        # Check for valid dose grid information
        dose_grid = trial.dose_grid
        if not dose_grid:
            raise ValueError("No dose grid information found in trial")
        voxel_size = dose_grid.voxel_size
        if not voxel_size:
            raise ValueError("No voxel size found in dose grid")
        dimension = dose_grid.dimension
        if not dimension:
            raise ValueError("No dimension found in dose grid")

        # Create base dataset
        file_meta = self.create_file_meta(sop_class_uid=RTDoseStorage, sop_instance_uid=sop_instance_uid)
        ds = self.create_dataset(file_meta)
        self.set_common_elements(ds)

        # Set dose-specific DICOM tags
        ds.Modality = self.MODALITY
        ds.SeriesInstanceUID = dose_series_uid
        ds.DoseUnits = "GY"
        ds.DoseType = "PHYSICAL"
        ds.DoseSummationType = "PLAN"

        # Set image-related tags
        ds.SamplesPerPixel = 1
        ds.PhotometricInterpretation = PHOTOMETRIC_INTERPRETATION_MONOCHROME2
        ds.NumberOfFrames = dimension.z
        ds.Rows = dimension.y
        ds.Columns = dimension.x
        ds.PixelSpacing = [voxel_size.x, voxel_size.y]

        # Set bit depth information
        ds.BitsAllocated = 32
        ds.BitsStored = 32
        ds.HighBit = 31
        ds.PixelRepresentation = 0

        # Set position and frame information
        self.set_dose_position_by_patient_position(ds, trial)
        frame_offset_vector = [int(i * voxel_size.z) for i in range(int(dimension.z))]
        ds.GridFrameOffsetVector = frame_offset_vector
        offset_vector = ds.data_element("GridFrameOffsetVector")
        if offset_vector:
            ds.FrameIncrementPointer = offset_vector.tag

        # Add plan reference if available
        plan_sop_instance_uid = getattr(trial, "plan_sop_instance_uid", "")
        if plan_sop_instance_uid:
            ds.ReferencedRTPlanSequence = Sequence()
            ref_plan = Dataset()
            ds.ReferencedRTPlanSequence.append(ref_plan)
            ref_plan.ReferencedSOPClassUID = RTPlanStorage
            ref_plan.ReferencedSOPInstanceUID = plan_sop_instance_uid
            ref_plan.ReferencedFractionGroupSequence = Sequence()
            ref_fraction_group = Dataset()
            ref_plan.ReferencedFractionGroupSequence.append(ref_fraction_group)
            ref_fraction_group.ReferencedFractionGroupNumber = "1"

        ds.TissueHeterogeneityCorrection = "IMAGE"
        return ds


def main(plan_path: str, trial_index: int, output_path: str) -> None:
    """
    Main function to run the DoseConverter as a standalone script.

    Args:
        plan_path (str): Name of the plan folder (optional for pre-loaded workflow).
        trial_index (int): Trial index for conversion, starting at 0 (optional for pre-loaded workflow).
        output_path (str): Directory where output DICOM files will be saved.
    """
    import os

    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )
    logger = logging.getLogger(__name__)
    logger.info(f"Starting dose conversion for trial {trial_index} in {plan_path}")

    try:
        # Create converter and load data
        patient_path = os.path.dirname(plan_path)
        dose_converter = DoseConverter.from_archive(patient_path)
        trial_list = dose_converter.load_trials(plan_path)
        trial = trial_list[trial_index]
        dose = dose_converter.load_dose(plan_path, trial)

        # Convert and save dose
        dose_ds = dose_converter.convert(trial, dose)
        output_file = dose_converter.save_dataset(dose_ds, output_path)
        logger.info(f"Dose conversion completed. Output file: {output_file}")
    except Exception as e:
        logger.error(f"Error during dose conversion: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    import sys
    import argparse

    # Create argument parser
    parser = argparse.ArgumentParser(description="Convert Pinnacle dose files to DICOM RT Dose files")

    # Add required arguments
    parser.add_argument("--plan-path", "-f", required=True, help="Path to the plan folder")
    parser.add_argument("--trial-index", "-n", type=int, default=0, help="Trial index (default: 0)")
    parser.add_argument(
        "--output-path",
        "-o",
        required=True,
        help="Path where output DICOM dataset will be saved",
    )

    # Parse arguments
    args = parser.parse_args()

    # Call main function with parsed arguments
    main(
        plan_path=args.plan_path,
        trial_index=args.trial_index,
        output_path=args.output_path,
    )
