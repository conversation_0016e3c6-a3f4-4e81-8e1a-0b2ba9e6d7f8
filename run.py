import argparse
from ui.main_view import MainView

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="DICOM Converter - Patient Data Processing"
    )
    parser.add_argument(
        "--archive_path",
        type=str,
        help="Path to a TAR file, ZIP file, or directory to load",
        default=None,
    )
    parser.add_argument(
        "--patient",
        type=str,
        help="Patient to select after loading (patient name or index)",
        default=None,
    )
    parser.add_argument(
        "--plan",
        type=str,
        help="Plan to select after loading (plan name or index)",
        default=None,
    )
    parser.add_argument(
        "--trial",
        type=str,
        help="Trial to select after loading (trial name or index)",
        default=None,
    )
    args = parser.parse_args()

    view = MainView(
        archive_path=args.archive_path,
        patient_selection=args.patient,
        plan_selection=args.plan,
        trial_selection=args.trial,
    )
    view.run()
